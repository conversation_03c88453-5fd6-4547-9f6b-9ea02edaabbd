/* Complete Theme CSS */

/* Light theme variables */
:root {
  /* Tangerine theme - Light mode */
  --background: 210 40% 98%;
  --foreground: 222 47% 11.2%;

  --card: 0 0% 100%;
  --card-foreground: 222 47% 11.2%;

  --popover: 0 0% 100%;
  --popover-foreground: 222 47% 11.2%;

  /* Tangerine primary color */
  --primary: 25 95% 53%;
  --primary-foreground: 210 40% 98%;

  --secondary: 25 30% 96.1%;
  --secondary-foreground: 25 47% 11.2%;

  --muted: 25 30% 96.1%;
  --muted-foreground: 25 16.3% 46.9%;

  --accent: 25 30% 96.1%;
  --accent-foreground: 25 47% 11.2%;

  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;

  --border: 25 31.8% 91.4%;
  --input: 25 31.8% 91.4%;
  --ring: 25 95% 53%;

  --radius: 0.5rem;

  --sidebar-background: 0 0% 100%;
  --sidebar-foreground: 222 47% 11.2%;
  --sidebar-primary: 25 95% 53%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 25 30% 96.1%;
  --sidebar-accent-foreground: 25 47% 11.2%;
  --sidebar-border: 25 31.8% 91.4%;
  --sidebar-ring: 25 95% 53%;
}

/* Dark theme variables */
.dark {
  /* Tangerine theme - Dark mode */
  --background: 222 47% 4.9%;
  --foreground: 210 40% 98%;

  --card: 222 47% 6.9%;
  --card-foreground: 210 40% 98%;

  --popover: 222 47% 6.9%;
  --popover-foreground: 210 40% 98%;

  /* Tangerine primary color in dark mode */
  --primary: 25 95% 53%;
  --primary-foreground: 222 47% 4.9%;

  --secondary: 217.2 32.6% 12%;
  --secondary-foreground: 210 40% 98%;

  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;

  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;

  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;

  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 25 95% 53%;

  --sidebar-background: 222 47% 6.9%;
  --sidebar-foreground: 210 40% 98%;
  --sidebar-primary: 25 95% 53%;
  --sidebar-primary-foreground: 222 47% 4.9%;
  --sidebar-accent: 217.2 32.6% 17.5%;
  --sidebar-accent-foreground: 210 40% 98%;
  --sidebar-border: 217.2 32.6% 17.5%;
  --sidebar-ring: 25 95% 53%;
}

/* Stargety theme variables */
.theme-stargety {
  /* Light mode */
  --background: 0 0% 96%;
  --foreground: 0 0% 20%;

  --card: 0 0% 100%;
  --card-foreground: 0 0% 20%;

  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 20%;

  --primary: 84 65% 58%;
  --primary-foreground: 0 0% 100%;

  --secondary: 84 20% 70%;
  --secondary-foreground: 84 65% 24%;

  --muted: 0 0% 88%;
  --muted-foreground: 84 10% 46%;

  --accent: 84 20% 77%;
  --accent-foreground: 84 65% 17%;

  --destructive: 0 65% 47%;
  --destructive-foreground: 0 0% 100%;

  --border: 0 0% 86%;
  --input: 0 0% 96%;
  --ring: 84 90% 48%;

  --radius: 0.75rem;

  --sidebar-background: 0 0% 100%;
  --sidebar-foreground: 0 0% 20%;
  --sidebar-primary: 84 65% 58%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 0 0% 94%;
  --sidebar-accent-foreground: 0 0% 52%;
  --sidebar-border: 84 10% 92%;
  --sidebar-ring: 84 65% 58%;
}

/* Stargety dark theme variables */
.theme-stargety.dark {
  /* Dark mode */
  --background: 0 0% 8%;
  --foreground: 0 0% 90%;

  --card: 0 0% 11%;
  --card-foreground: 0 0% 90%;

  --popover: 0 0% 23%;
  --popover-foreground: 0 0% 90%;

  --primary: 84 65% 58%;
  --primary-foreground: 0 0% 100%;

  --secondary: 0 0% 4%;
  --secondary-foreground: 0 0% 90%;

  --muted: 0 0% 16%;
  --muted-foreground: 0 0% 51%;

  --accent: 0 0% 13%;
  --accent-foreground: 0 0% 50%;

  --destructive: 0 70% 60%;
  --destructive-foreground: 0 0% 100%;

  --border: 0 0% 16%;
  --input: 0 0% 16%;
  --ring: 84 65% 58%;

  --sidebar-background: 0 0% 25%;
  --sidebar-foreground: 0 0% 90%;
  --sidebar-primary: 84 65% 58%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 0 0% 37%;
  --sidebar-accent-foreground: 0 0% 85%;
  --sidebar-border: 0 0% 28%;
  --sidebar-ring: 84 65% 58%;
}

/* Fix theme application */
:root, .theme-default {
  color-scheme: light;
}

.dark {
  color-scheme: dark;
}

.theme-stargety {
  color-scheme: light;
}

.theme-cosmic {
  color-scheme: light;
}

.theme-modern {
  color-scheme: light;
}

.theme-tangerine {
  color-scheme: light;
}

/* Fix font family */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

/* Fix sidebar styling */
.sidebar {
  background-color: hsl(var(--sidebar-background));
  color: hsl(var(--sidebar-foreground));
  border-color: hsl(var(--sidebar-border));
}

/* Fix button styling */
.btn-primary, button[class*="btn-primary"] {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

/* Fix card styling */
.card, div[class*="card"] {
  background-color: hsl(var(--card));
  color: hsl(var(--card-foreground));
  border-color: hsl(var(--border));
}

/* Fix input styling */
input, select, textarea {
  background-color: hsl(var(--input));
  border-color: hsl(var(--border));
  color: hsl(var(--foreground));
}

/* Fix table styling */
table {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

/* Fix scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted-foreground) / 0.2);
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--muted-foreground) / 0.3);
}
