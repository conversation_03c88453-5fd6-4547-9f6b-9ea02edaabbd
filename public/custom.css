/* Custom CSS to fix styling issues */

/* Fix font loading */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Merriweather:wght@300;400;700&family=Space+Mono&display=swap');

/* Fix theme application */
:root, .theme-default {
  color-scheme: light;
}

.dark {
  color-scheme: dark;
}

.theme-stargety {
  color-scheme: light;
}

.theme-cosmic {
  color-scheme: light;
}

.theme-modern {
  color-scheme: light;
}

.theme-tangerine {
  color-scheme: light;
}

/* Fix font family */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Fix sidebar styling */
.sidebar {
  background-color: hsl(var(--sidebar-background));
  color: hsl(var(--sidebar-foreground));
  border-color: hsl(var(--sidebar-border));
}

/* Fix button styling */
.btn-primary {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

/* Fix card styling */
.card {
  background-color: hsl(var(--card));
  color: hsl(var(--card-foreground));
  border-color: hsl(var(--border));
}

/* Fix input styling */
input, select, textarea {
  background-color: hsl(var(--input));
  border-color: hsl(var(--border));
  color: hsl(var(--foreground));
}

/* Fix table styling */
table {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

/* Fix scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted-foreground) / 0.2);
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--muted-foreground) / 0.3);
}
