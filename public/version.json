{"version": "1.3.2", "lastUpdated": "2025-05-20", "buildDate": "2025-05-20", "environment": "production", "database": "PostgreSQL", "changelog": [{"version": "1.3.2", "date": "2025-05-20", "changes": ["Fixed UI rendering issues with improved error handling", "Fixed naming conflicts in search components", "Enhanced component error boundaries for better fault tolerance", "Improved version display component with better error handling", "Migrated from Teable to PostgreSQL database"], "details": {"Bug Fixes": ["Fixed UI rendering issues that caused the interface to disappear", "Resolved naming conflicts in search components that caused runtime errors", "Added error boundaries to isolate and handle component failures gracefully", "Enhanced version display component with robust error handling", "Fixed import paths for search-related components", "Improved error handling in theme initialization"], "Database Migration": ["Completed migration from Teable to PostgreSQL", "Removed all Teable dependencies and code", "Updated environment variables for PostgreSQL configuration", "Ensured proper database connections in production mode"]}}, {"version": "1.3.1", "date": "2025-05-05", "changes": ["UI Improvements: Removed development mode banner when API is configured", "UI Improvements: Simplified Teable integration interface", "UI Improvements: Consolidated API configuration buttons", "UI Improvements: Enhanced API settings validation and feedback", "Bug Fixes: Fixed inconsistencies in API configuration workflow"]}, {"version": "1.3.0", "date": "2025-04-30", "changes": ["UI Standardization: Created reusable PageHeader component for consistent layout", "UI Standardization: Removed duplicate UI elements and buttons across pages", "UI Standardization: Applied consistent styling to view toggle buttons", "UI Standardization: Integrated filter tabs into PageHeader component", "UI Improvements: Enhanced Candidates view mode toggle functionality", "Database Migration: Replaced Supabase with Teable.io", "Database Migration: Fixed browser compatibility issues with database access", "Database Migration: Created unified database client interface", "Project Cleanup: Removed obsolete test files and scripts", "Project Cleanup: Removed empty directories", "Project Cleanup: Updated documentation to reflect current state", "Docker Configuration: Improved Docker setup for production deployment"]}, {"version": "1.2.0", "date": "2025-04-11", "changes": ["Added theme customization with multiple themes (default, cosmic, modern, stargety, tangerine)", "Implemented dark/light mode toggle in navbar and settings", "Added theme preferences saved in localStorage", "Improved user settings with accessibility options", "Enhanced project documentation", "Code cleanup and optimization", "Fixed various UI bugs and improved responsiveness"]}, {"version": "1.1.0", "date": "2023-07-10", "changes": ["Added comprehensive notification system", "Implemented notification dropdown in navbar", "Created dedicated notifications page with filtering and search", "Added bulk actions for notifications (mark as read, archive, delete)", "Implemented optimistic UI updates for notification actions"]}, {"version": "1.0.0", "date": "2023-07-01", "changes": ["Initial release", "Basic ATS functionality", "Candidate and job management", "Calendar integration", "Messaging system"]}]}