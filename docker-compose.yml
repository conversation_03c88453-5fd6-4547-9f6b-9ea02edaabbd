services:
  # Frontend React application (Production)
  # Using pre-built static files from local build
  frontend:
    image: nginx:alpine
    container_name: ats-dashboard-guru-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      - ./dist:/usr/share/nginx/html
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - ./src/themes:/usr/share/nginx/html/themes
    depends_on:
      - postgres
      - redis
      - minio
    env_file:
      - .env.docker
    environment:
      - VITE_APP_ENV=production

  # API Server
  api:
    image: node:20-alpine
    container_name: ats-dashboard-guru-api
    restart: unless-stopped
    working_dir: /app
    command: sh -c "npm install && node src/server/server.cjs"
    ports:
      - "3001:3001"
    volumes:
      - ./:/app
      - node_modules:/app/node_modules
    env_file:
      - .env.docker
    environment:
      - VITE_APP_ENV=production
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3001/health"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 15s



  # PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: ats-dashboard-guru-postgres
    restart: unless-stopped
    # ports: # ✅ NO exponer puerto en producción - solo acceso interno
    #   - "5432:5432"
    env_file:
      - .env.docker
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./docker-entrypoint-initdb.d/00-init-db.sh:/docker-entrypoint-initdb.d/00-init-db.sh
      - ./docker-entrypoint-initdb.d/sample-data.sql:/docker-entrypoint-initdb.d/02-sample-data.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres && psql -U postgres -d postgres -c 'SELECT 1 FROM users LIMIT 1' || echo 'Database not initialized yet'"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 15s

  # MinIO (S3-compatible storage)
  minio:
    image: minio/minio
    container_name: ats-dashboard-guru-minio
    restart: unless-stopped
    # ports: # ✅ NO exponer puertos en producción - solo acceso interno
    #   - "9000:9000"
    #   - "9001:9001"
    env_file:
      - .env.docker
    command: server /data --console-address ":9001"
    volumes:
      - minio-data:/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s

  # Typesense search engine
  typesense:
    image: typesense/typesense:0.25.1
    container_name: ats-dashboard-guru-typesense
    restart: unless-stopped
    ports: # ✅ Expuesto para desarrollo local con Vite
      - "8108:8108"
    env_file:
      - .env.docker
    volumes:
      - typesense-data:/data

  # Redis cache
  redis:
    image: redis:7.2.4
    container_name: ats-dashboard-guru-redis
    restart: unless-stopped
    # ports: # ✅ NO exponer puerto en producción - solo acceso interno
    #   - "6379:6379"
    command: redis-server
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
      start_period: 5s

  # pgAdmin - PostgreSQL Administration (SOLO para desarrollo local)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: ats-dashboard-guru-pgadmin
    restart: unless-stopped
    # ports: # ✅ NO exponer en producción - solo para desarrollo local
    #   - "5050:80"
    env_file:
      - .env.docker
    volumes:
      - pgadmin-data:/var/lib/pgadmin
    depends_on:
      - postgres

volumes:
  postgres-data:
  minio-data:
  typesense-data:
  pgadmin-data:
  node_modules:
