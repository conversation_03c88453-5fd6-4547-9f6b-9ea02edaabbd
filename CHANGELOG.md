# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.3.1] - 2025-05-01

### Fixed
- Theme System: Fixed issue where theme selection required two clicks to apply
- Theme System: Implemented atomic theme updates to prevent race conditions
- Theme System: Updated documentation with detailed explanation of the theme system
- Theme System: Improved theme selection UI responsiveness

## [1.3.0] - 2025-04-30

### Added
- UI Standardization: Created reusable PageHeader component for consistent layout
- UI Standardization: Integrated filter tabs into PageHeader component
- UI Improvements: Enhanced Candidates view mode toggle functionality
- Database Migration: Created unified database client interface
- Project Cleanup: Updated documentation to reflect current state
- Docker Configuration: Improved Docker setup for production deployment

### Changed
- UI Standardization: Removed duplicate UI elements and buttons across pages
- UI Standardization: Applied consistent styling to view toggle buttons
- Database Migration: Replaced Supabase with Teable.io
- Database Migration: Fixed browser compatibility issues with database access

### Removed
- Project Cleanup: Removed obsolete test files and scripts
- Project Cleanup: Removed empty directories
- Removed all Supabase dependencies and code

### Performance Improvements
- Optimized dependencies:
  - Updated Node.js from v16.15.1 to v18.18.0 for better performance and compatibility
  - Removed crypto polyfill in vite.config.ts that's no longer needed with Node.js 18
  - Standardized Radix UI component versions for better compatibility
- Optimized loading:
  - Implemented lazy loading for non-essential page components
  - Optimized web font loading to reduce initial load time
  - Implemented dynamic CSS theme loading on demand
  - Added PageLoader component to display during lazy component loading
- Optimized Vite configuration:
  - Improved build configuration to split code into smaller chunks
  - Configured more aggressive minification for production
  - Implemented dependency caching to improve development times
- Code refactoring:
  - Improved TypeScript configuration to detect potential issues
  - Optimized GlobalSearch component with memoization to reduce unnecessary renders
  - Replaced setTimeout with requestAnimationFrame for better performance
  - Implemented useCallback for event handler functions

### Configuration Changes
- Enabled linting options in tsconfig.app.json:
  - noUnusedLocals: true
  - noUnusedParameters: true
  - noFallthroughCasesInSwitch: true
  - forceConsistentCasingInFileNames: true

### Database Migration Details
- Created Teable.io client for API access
- Updated hooks to work with Teable.io
- Created database configuration file
- Updated docker-compose.yml to include PostgreSQL and Teable.io
- Created .env.example file with new environment variables
- Updated package.json with new dependencies
- Fixed browser compatibility issues with database access
- Created browser-compatible API client
- Updated hooks to use the API client
- Simplified database access code

### UI Improvements Details
- Moved view mode toggle (list/kanban) from the top of the page to the right of import/export buttons
- Enhanced CSVUploadExport component to include view mode toggle functionality
- Updated ListView component to pass view mode props to CSVUploadExport
- Maintained localStorage persistence for user's view mode preference
- Fixed view mode toggle buttons not appearing in the UI
- Added view mode toggle to KanbanViewWrapper component
- Ensured consistent UI between list and kanban views
- Positioned toggle buttons to the right of import/export buttons

### UI Standardization Details
- Created reusable PageHeader component for consistent layout across pages
- Standardized header structure with title, description, search, and actions
- Implemented consistent positioning of primary actions and import/export buttons
- Added support for view mode toggle in the header component
- Refactored Candidates page to use the new PageHeader component
- Refactored Clients page to use the new PageHeader component
- Refactored Jobs page to use the new PageHeader component
- Improved database connection warning with icon and better styling
- Ensured consistent spacing and layout across all main pages
- Fixed duplicate UI elements in Candidates page
- Removed redundant search bars and action buttons
- Integrated filter tabs into PageHeader component
- Positioned view mode toggle buttons consistently
- Eliminated code duplication in ListView and KanbanViewWrapper components
- Removed duplicate "Add" buttons next to filters in all pages
- Applied consistent bg-muted styling with padding to view toggle buttons
- Ensured consistent UI patterns across all main pages

## [1.2.0] - 2025-04-11

### Added
- Theme customization with multiple themes (default, cosmic, modern, stargety, tangerine)
- Dark/light mode toggle in navbar and settings
- Theme preferences saved in localStorage
- User settings with accessibility options
- Enhanced project documentation

### Changed
- Code cleanup and optimization
- Fixed various UI bugs and improved responsiveness

### Theme Customization Details
- Created ThemeProvider component to manage theme state
- Implemented ThemeToggle component for the navbar
- Added UserPreferencesSettings component for the Settings page
- Updated Navbar to include the ThemeToggle
- Updated Settings page to include the UserPreferences submenu
- Imported theme CSS files in main.tsx
- Theme switching between 5 themes (default, cosmic, modern, stargety, tangerine)
- Dark/light mode toggle
- Theme preferences saved in localStorage
- Theme selection in User Preferences settings
- Dark/light mode selection in User Preferences settings
- Modified index.css to import theme CSS files directly
- Updated tailwind.config.ts to use hexadecimal color values via CSS variables
- Removed duplicate theme imports from main.tsx
- Fixed theme application to ensure proper color rendering
- Fixed CSS issues by replacing Tailwind @apply directives with standard CSS
- Corrected import order in index.css to ensure proper theme loading

## [1.1.0] - 2023-07-10

### Added
- Comprehensive notification system
- Notification dropdown in navbar
- Dedicated notifications page with filtering and search
- Bulk actions for notifications (mark as read, archive, delete)
- Optimistic UI updates for notification actions

## [1.0.0] - 2023-07-01

### Added
- Initial release
- Basic ATS functionality
- Candidate and job management
- Calendar integration
- Messaging system
