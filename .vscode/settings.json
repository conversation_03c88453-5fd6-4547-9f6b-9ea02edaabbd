{"terminal.integrated.defaultProfile.linux": "bash", "terminal.integrated.profiles.linux": {"bash": {"path": "/bin/bash", "args": ["-l"]}, "docker-bash": {"path": "/bin/bash", "args": ["-c", "exec /bin/bash --login"], "icon": "terminal-bash"}}, "terminal.integrated.env.linux": {"DOCKER_BUILDKIT": "1"}, "docker.dockerPath": "docker", "docker.dockerComposePath": "docker-compose", "docker.enableDockerComposeLanguageService": true, "files.watcherExclude": {"**/node_modules/**": true, "**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/dist/**": true, "**/build/**": true}, "containers.containerCommand": "docker", "containers.enableComposeLanguageService": true}