{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "outDir": "dist/server", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noImplicitAny": false, "noUnusedParameters": false, "noUnusedLocals": false, "strictNullChecks": false}, "include": ["src/server/**/*"]}