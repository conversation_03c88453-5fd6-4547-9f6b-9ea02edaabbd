# Column Visibility System Documentation

## Overview

The Column Visibility System provides a comprehensive solution for managing table column visibility in AG Grid tables. It features a slide-out side panel that allows users to show/hide columns, search through available columns, and persist their preferences.

## Architecture

### Components

#### 1. ColumnVisibilityContext (`src/contexts/ColumnVisibilityContext.tsx`)
- **Purpose**: Manages column visibility state and localStorage persistence
- **Key Features**:
  - Table-specific column state management
  - Automatic localStorage persistence with prefixed keys
  - Default column visibility based on column definitions
  - Cross-tab synchronization support

#### 2. ColumnVisibilityPanel (`src/components/grid/ColumnVisibilityPanel.tsx`)
- **Purpose**: Main UI component for the column visibility side panel
- **Key Features**:
  - Slide-in panel from the right side
  - Real-time column search/filtering
  - Grouped display (pinned vs regular columns)
  - Bulk actions (Show All, Hide All, Reset)
  - Visual indicators for column visibility
  - Statistics display (visible/hidden counts)

#### 3. ColumnVisibilityTrigger (`src/components/grid/ColumnVisibilityTrigger.tsx`)
- **Purpose**: Button component to trigger the column visibility panel
- **Key Features**:
  - Customizable appearance (variant, size)
  - Tooltip support
  - Accessibility features

### Integration

The system is integrated into the `TableView` component using a provider pattern:

```tsx
// Wrapper component with provider
export default function TableView(props: TableViewProps) {
  const baseColumnDefs = useMemo(() => [...], []);

  return (
    <ColumnVisibilityProvider tableId="candidates-table" columns={baseColumnDefs}>
      <TableViewInternal {...props} />
    </ColumnVisibilityProvider>
  );
}

// Internal component that uses the context
function TableViewInternal({ candidates, onCandidatesChange }: TableViewProps) {
  const { columnVisibility } = useColumnVisibility();
  // ... component implementation
}
```

## Features

### 1. Panel Behavior & Layout
- **Full Height**: Panel occupies 100vh with fixed width (400-500px)
- **Overlay**: Appears as overlay with backdrop
- **Animations**: Smooth slide-in/slide-out transitions
- **Responsive**: Adapts to different screen sizes

### 2. Column Management
- **Real-time Updates**: Column visibility changes immediately affect the grid
- **Search Functionality**: Filter columns by name or field
- **Grouping**: Pinned columns displayed separately
- **Visual Indicators**: Eye icons show current visibility state

### 3. Bulk Operations
- **Show All**: Make all columns visible
- **Hide All**: Hide all columns (except protected ones)
- **Reset to Defaults**: Restore original column visibility

### 4. Persistence
- **localStorage**: Preferences saved automatically
- **Table-specific**: Each table has its own visibility state
- **Cross-tab**: Changes sync across browser tabs

### 5. Accessibility
- **Keyboard Support**: ESC key closes panel
- **Screen Reader**: Proper ARIA labels and descriptions
- **Focus Management**: Logical tab order

## Usage

### Basic Implementation

1. **Wrap your table component with the provider**:
```tsx
<ColumnVisibilityProvider tableId="unique-table-id" columns={columnDefs}>
  <YourTableComponent />
</ColumnVisibilityProvider>
```

2. **Use the context in your table component**:
```tsx
function YourTableComponent() {
  const { columnVisibility } = useColumnVisibility();

  const columnDefs = useMemo(() => [
    {
      field: 'name',
      headerName: 'Name',
      hide: columnVisibility.name === false
    },
    // ... other columns
  ], [columnVisibility]);
}
```

3. **Add the trigger button and panel**:
```tsx
<ColumnVisibilityTrigger onClick={() => setShowPanel(true)} />
<ColumnVisibilityPanel
  isOpen={showPanel}
  onClose={() => setShowPanel(false)}
  columns={columnDefs}
  gridApi={gridApi}
/>
```

### Advanced Configuration

#### Custom Column Grouping
Columns are automatically grouped by their `pinned` property:
- Pinned columns (left/right) appear in "Pinned Columns" section
- Regular columns appear in "Regular Columns" section

#### Excluding Columns from Panel
Use `suppressColumnsToolPanel: true` in column definition:
```tsx
{
  field: 'actions',
  headerName: 'Actions',
  suppressColumnsToolPanel: true, // Won't appear in visibility panel
}
```

#### Default Visibility
Set default visibility using the `hide` property:
```tsx
{
  field: 'optional',
  headerName: 'Optional Column',
  hide: true, // Hidden by default
}
```

## Styling & Theming

The system uses shadcn/ui components and follows the project's theme system:

- **Theme Support**: Automatically adapts to light/dark mode
- **CSS Variables**: Uses theme-aware CSS custom properties
- **Responsive**: Mobile-friendly design
- **Consistent**: Follows established UI patterns

## localStorage Schema

Column visibility preferences are stored with the following structure:

```
Key: columnVisibility_{tableId}
Value: {
  "columnField1": true,
  "columnField2": false,
  "columnField3": true
}
```

## Performance Considerations

- **Memoization**: Column definitions are memoized to prevent unnecessary re-renders
- **Efficient Updates**: Only changed columns are updated in AG Grid
- **Debounced Search**: Search input is optimized for performance
- **Lazy Loading**: Panel content is only rendered when open

## Browser Compatibility

- **Modern Browsers**: Chrome, Firefox, Safari, Edge (latest versions)
- **localStorage**: Required for persistence functionality
- **CSS Grid/Flexbox**: Used for layout

## Troubleshooting

### Common Issues

1. **Panel not opening**: Check if trigger button is properly connected
2. **Columns not hiding**: Verify column definitions include `hide` property
3. **Persistence not working**: Ensure localStorage is available and not blocked
4. **Theme issues**: Check if theme context is properly provided

### Debug Mode

Enable debug logging by setting localStorage item:
```javascript
localStorage.setItem('columnVisibility_debug', 'true');
```

## Available Columns (32 Total)

### **✅ Currently Implemented**

**Core Information (Always Visible):**
- Name (first_name + last_name combined, pinned left)
- Actions (pinned right)

**Contact & Location:**
- Email, Phone, Location

**Professional Profile:**
- Position, Current Position, Current Company, Experience Years, Education, Skills

**Portfolio & Links:**
- Resume URL, LinkedIn URL, GitHub URL, Portfolio URL

**Compensation & Availability:**
- Desired Salary, Currency, Availability Date

**Application Status:**
- Applied Date, Primary Status, Secondary Status, Source

**Internal Tracking:**
- Stargety ID, Duplicate Status

**Assessment & Evaluation:**
- English Level, Interview Score, Challenge, Drive Score, Resilience Score, Collaboration Score

**System Status:**
- In Incubator, Created Date, Updated Date (hidden by default)

### **🚫 Excluded Columns (Per Requirements)**
- **System Metadata**: `id` (primary key)
- **Notes/Feedback**: `notes`, `interview_notes`, `challenge_notes`, `challenge_feedback`
- **Result Field**: `result` (outcome field)

## Future Enhancements

- Column reordering via drag & drop
- Column width persistence
- Export/import column configurations
- Column grouping management
- Advanced filtering options
