# Theme System Documentation

## Overview

The ATS Dashboard Guru application includes a flexible theme system that allows users to customize the appearance of the application. The system supports multiple themes, including light and dark modes, as well as custom themes like "Stargety", "Cosmic", "Modern", and "Tangerine".

## Available Themes

### Default Themes
- **Default Light**: The default light theme with neutral colors
- **Default Dark**: The default dark theme with neutral colors

### Custom Themes
- **Stargety**: A fresh green theme with modern aesthetics, available in both light and dark variants
- **Cosmic**: A purple-themed design with space-inspired aesthetics
- **Modern**: A clean blue-themed design with contemporary styling
- **Tangerine**: A warm orange-themed design with vibrant accents

## Theme Structure

Each theme defines a set of CSS variables that control the colors and other visual aspects of the application. The main variables include:

- `--background`: The main background color
- `--foreground`: The main text color
- `--card`: The background color for cards
- `--card-foreground`: The text color for cards
- `--primary`: The primary accent color
- `--primary-foreground`: The text color on primary-colored elements
- `--secondary`: The secondary color
- `--secondary-foreground`: The text color on secondary-colored elements
- `--muted`: The background color for muted elements
- `--muted-foreground`: The text color for muted elements
- `--accent`: The accent color
- `--accent-foreground`: The text color on accent-colored elements
- `--destructive`: The color for destructive actions
- `--destructive-foreground`: The text color on destructive elements
- `--border`: The color for borders
- `--input`: The background color for input fields
- `--ring`: The color for focus rings

Additionally, there are sidebar-specific variables:
- `--sidebar-background`: The background color for the sidebar
- `--sidebar-foreground`: The text color for the sidebar
- `--sidebar-primary`: The primary color for the sidebar
- `--sidebar-primary-foreground`: The text color on primary-colored elements in the sidebar
- `--sidebar-accent`: The accent color for the sidebar
- `--sidebar-accent-foreground`: The text color on accent-colored elements in the sidebar
- `--sidebar-border`: The color for borders in the sidebar
- `--sidebar-ring`: The color for focus rings in the sidebar

## Theme Implementation

The theme system is implemented using the following components:

### CSS Files
- `src/index.css`: Imports all theme CSS files
- `src/themes/*.css`: Individual CSS files for each theme (default.css, cosmic.css, modern.css, stargety.css, tangerine.css)

### Components
- `ThemeToggle`: A dropdown menu in the navbar that allows users to switch between light and dark modes, as well as select different themes
- `ThemeSelector`: A dialog that provides a visual preview of available themes and allows users to select them
- `ThemeInitializer`: A component that initializes the theme on page load based on localStorage preferences

### Theme Context
The application uses a custom `ThemeContext` to manage themes:
- `ThemeProvider`: Provides theme state and functions to the application
- `useTheme`: A custom hook to access the theme context in components

## Theme Storage

Theme preferences are stored in localStorage with two keys:
- `themeStyle`: Stores the selected theme style (e.g., 'theme-default', 'theme-cosmic')
- `themeMode`: Stores the selected mode ('light' or 'dark')

## Adding a New Theme

To add a new theme to the application:

1. Create a new CSS file in the `src/themes` directory with your theme variables
2. Import the CSS file in `src/index.css`
3. Add the theme to the `ThemeStyle` type in `ThemeContext.tsx`
4. Add the theme to the themes array in `ThemeToggle.tsx`
5. Add the theme to the `themeOptions` array in `ThemeSelector.tsx`

## Usage

Users can change the theme in two ways:
1. Using the theme toggle button in the navbar, which provides a dropdown menu with theme options and a light/dark mode toggle
2. Using the theme selector dialog in User Settings, which provides a visual preview of available themes

The selected theme is stored in localStorage and persists across sessions.

## Best Practices

When working with the theme system, follow these best practices:

1. Always use the CSS variables defined in the theme system instead of hardcoding colors
2. Test your changes in all available themes to ensure they look good
3. Ensure sufficient contrast between text and background colors for accessibility
4. Consider adding both light and dark variants of custom themes
5. Use the `useTheme` hook to access the current theme in your components
6. When applying themes, always clean existing theme classes before adding new ones
7. Use the dark class for dark mode (not a separate 'light' class)
