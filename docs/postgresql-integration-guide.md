# PostgreSQL Integration Guide

This guide provides information on how to integrate the ATS Dashboard Guru application with PostgreSQL.

## Overview

The ATS Dashboard Guru application uses PostgreSQL as its primary database for storing all application data. The integration is implemented through a RESTful API server that communicates with the PostgreSQL database.

## Architecture

The database integration architecture consists of the following components:

1. **PostgreSQL Database**: Stores all application data in a relational database.
2. **API Server**: A Node.js Express server that provides RESTful API endpoints for interacting with the database.
3. **API Client**: A client-side library that communicates with the API server.

## Configuration

### Environment Variables

The following environment variables are used to configure the PostgreSQL connection:

```
VITE_POSTGRES_HOST=localhost
VITE_POSTGRES_PORT=5432
VITE_POSTGRES_DATABASE=postgres
VITE_POSTGRES_USER=postgres
VITE_POSTGRES_PASSWORD=postgres
VITE_POSTGRES_SSL=false
VITE_API_URL=http://localhost:3001/api
```

These variables should be defined in a `.env` file in the root of the project.

### Database Schema

The database schema is defined in the `schema.sql` file in the root of the project. This file contains the SQL statements to create all the necessary tables and indexes.

The main tables in the database are:

- `users`: Stores user information
- `clients`: Stores client (company) information
- `jobs`: Stores job posting information
- `candidates`: Stores candidate information
- `job_applications`: Stores job application information
- `interviews`: Stores interview information
- `assessments`: Stores assessment information
- `communications`: Stores communication information
- `notifications`: Stores notification information

## API Server

The API server is implemented using Node.js and Express. It provides RESTful API endpoints for interacting with the database.

### Starting the API Server

To start the API server, run the following command:

```bash
npm run server
```

This will start the API server on port 3001 (or the port specified in the `VITE_SERVER_PORT` environment variable).

### API Endpoints

The API server provides the following endpoints:

#### Candidates

- `GET /api/candidates`: Get all candidates
- `GET /api/candidates/:id`: Get a candidate by ID
- `POST /api/candidates`: Create a new candidate
- `PUT /api/candidates/:id`: Update a candidate
- `DELETE /api/candidates/:id`: Delete a candidate

#### Jobs

- `GET /api/jobs`: Get all jobs
- `GET /api/jobs/:id`: Get a job by ID
- `POST /api/jobs`: Create a new job
- `PUT /api/jobs/:id`: Update a job
- `DELETE /api/jobs/:id`: Delete a job

#### Clients

- `GET /api/clients`: Get all clients
- `GET /api/clients/:id`: Get a client by ID
- `POST /api/clients`: Create a new client
- `PUT /api/clients/:id`: Update a client
- `DELETE /api/clients/:id`: Delete a client

#### Job Applications

- `GET /api/job-applications`: Get all job applications
- `GET /api/job-applications/:id`: Get a job application by ID
- `POST /api/job-applications`: Create a new job application
- `PUT /api/job-applications/:id`: Update a job application
- `DELETE /api/job-applications/:id`: Delete a job application

## API Client

The API client is a client-side library that communicates with the API server. It is implemented in the `src/lib/database/api-client.ts` file.

### Usage

To use the API client in your components, import it from the database library:

```typescript
import apiClient from '@/lib/database';

// Example: Get all clients
const { data: clients } = await apiClient.getRecords('clients');

// Example: Get a client by ID
const { data: client } = await apiClient.getRecord('clients', '123');

// Example: Create a new client
const result = await apiClient.createRecord('clients', { name: 'New Client' });
```

## Docker Configuration

The application includes Docker configuration for running the PostgreSQL database and the API server in containers.

### Starting the Docker Containers

To start the Docker containers, run the following command:

```bash
docker-compose up -d
```

This will start the PostgreSQL database and the API server in containers.

### Stopping the Docker Containers

To stop the Docker containers, run the following command:

```bash
docker-compose down
```

## Troubleshooting

### Connection Issues

If you're having trouble connecting to the PostgreSQL database, check the following:

1. Make sure the PostgreSQL server is running.
2. Check that the environment variables are correctly set in the `.env` file.
3. Verify that the PostgreSQL port (default: 5432) is not blocked by a firewall.
4. If using Docker, make sure the Docker containers are running.

### Database Schema Issues

If you're having issues with the database schema, you can recreate the database schema by running the following command:

```bash
docker exec -i ats-dashboard-guru-postgres psql -U postgres -d postgres -f - < schema.sql
```

This will drop all existing tables and recreate them according to the schema defined in the `schema.sql` file.

## Further Reading

- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Express.js Documentation](https://expressjs.com/)
- [Docker Documentation](https://docs.docker.com/)
