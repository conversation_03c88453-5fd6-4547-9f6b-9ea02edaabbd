# Notification System Documentation

## Overview

The notification system provides a comprehensive way to display and manage notifications throughout the application. It includes a dropdown in the navbar for quick access to recent notifications and a dedicated page for managing all notifications.

## Features

- **Notification Dropdown**: Displays the 10 most recent unarchived notifications in the navbar
- **Mark as Read**: Ability to mark individual notifications as read
- **Mark All as Read**: Ability to mark all notifications as read at once
- **Dedicated Notifications Page**: A full page for managing all notifications
- **Filtering**: Filter notifications by status (all, unread, archived)
- **Search**: Search notifications by title, message, or related entity
- **Bulk Actions**: Select multiple notifications to perform bulk actions
  - Mark as Read
  - Archive
  - Delete Forever (with confirmation)
- **Optimistic UI Updates**: All actions are applied immediately in the UI

## Components

### NotificationContext

The `NotificationContext` provides state management for notifications across the application. It includes:

- `notifications`: Array of all notifications
- `unreadCount`: Count of unread notifications
- `markAsRead`: Function to mark a notification as read
- `markAllAsRead`: Function to mark all notifications as read
- `archiveNotification`: Function to archive a notification
- `deleteNotification`: Function to delete a notification
- `bulkMarkAsRead`: Function to mark multiple notifications as read
- `bulkArchive`: Function to archive multiple notifications
- `bulkDelete`: Function to delete multiple notifications

### NotificationDropdown

The `NotificationDropdown` component displays recent notifications in a dropdown menu in the navbar. It shows:

- The 10 most recent unarchived notifications
- A badge indicator for unread notifications
- A "Mark all as read" button
- A "View all notifications" link to the dedicated page

### NotificationItem

The `NotificationItem` component displays an individual notification. It includes:

- Icon based on notification type
- Title and message
- Relative timestamp
- "Mark as read" button (for unread notifications)
- Selection checkbox (on the notifications page)

### Notifications Page

The dedicated notifications page provides a full interface for managing all notifications:

- Tabs for filtering by status (All, Unread, Archived)
- Search functionality
- Multi-select for bulk actions
- Bulk actions: Mark as Read, Archive, Delete Forever
- Confirmation dialog for delete actions

## Data Structure

Notifications have the following structure:

```typescript
interface Notification {
  id: string;
  type: 'application' | 'message' | 'interview' | 'task' | 'system';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  archived: boolean;
  actionUrl?: string;
  relatedTo?: {
    type: 'candidate' | 'job' | 'task' | 'message';
    id: string;
    name: string;
  };
}
```

## Usage

### Accessing the Notification System

- Click the bell icon in the navbar to view recent notifications
- Click "View all notifications" to access the dedicated page
- Use the sidebar navigation to access the notifications page directly

### Managing Notifications

- Click a notification to mark it as read and navigate to the related content
- Click "Mark as read" on a notification to mark it as read without navigating
- Use the checkboxes on the notifications page to select multiple notifications
- Use the bulk action buttons to perform actions on selected notifications

## Implementation Details

- The notification system uses optimistic UI updates for a responsive user experience
- Notifications are sorted by timestamp (newest first)
- The unread count is updated automatically when notifications are marked as read
- The notification dropdown only shows unarchived notifications
- The notifications page provides tabs for viewing all, unread, or archived notifications

## Future Enhancements

- Real-time notifications using WebSockets
- Push notifications for desktop and mobile
- Notification preferences and settings
- Notification categories and priority levels
- Read receipts for important notifications
