# Configuración de Producción Segura

## 🔒 Configuración Actual (Segura para Producción Pública)

### Puertos Expuestos al Público:
- **Puerto 80**: Frontend (Nginx) - ✅ SEGURO
- **Puerto 3001**: API Server - ✅ SEGURO

### Servicios Internos (NO expuestos):
- **PostgreSQL**: Solo acceso interno entre contenedores
- **Redis**: Solo acceso interno entre contenedores  
- **MinIO**: Solo acceso interno entre contenedores
- **Typesense**: Solo acceso interno entre contenedores
- **pgAdmin**: Solo acceso interno entre contenedores

## 🌐 Configuración de Router para Producción

### Port Forwarding Recomendado:
```
Router External Port → Internal Port
80                  → 80    (Frontend)
443                 → 443   (HTTPS - recomendado)
3001                → 3001  (API)
```

### ⚠️ NO hacer Port Forwarding de estos puertos:
- 5432 (PostgreSQL) - RIESGO DE SEGURIDAD
- 6379 (Redis) - RIESGO DE SEGURIDAD  
- 9000/9001 (MinIO) - RIESGO DE SEGURIDAD
- 8108 (Typesense) - RIESGO DE SEGURIDAD
- 5050 (pgAdmin) - RIESGO DE SEGURIDAD

## 🚀 Comandos de Despliegue

### Producción (Segura):
```bash
docker compose up -d
```

### Desarrollo Local (Con todos los puertos):
```bash
docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d
```

## 🔐 Recomendaciones Adicionales de Seguridad

1. **Usar HTTPS**: Configurar certificado SSL/TLS
2. **Firewall**: Configurar firewall en el servidor
3. **Contraseñas fuertes**: Cambiar contraseñas por defecto
4. **Actualizaciones**: Mantener contenedores actualizados
5. **Monitoreo**: Implementar logs y monitoreo

## 📍 Acceso en Producción

- **Aplicación**: http://tu-ip-publica
- **API**: http://tu-ip-publica:3001/api
- **Administración**: Solo acceso local o VPN
