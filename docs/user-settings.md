# User Settings Documentation

## Overview

The User Settings feature provides a centralized location for users to customize their experience within the ATS Dashboard Guru application. It includes theme selection, accessibility options, and language preferences.

## Features

### Theme Selection
- Users can choose from multiple themes:
  - Light: Default light theme with tangerine accents
  - Dark: Default dark theme with tangerine accents
  - Stargety: Fresh green theme with modern aesthetics (in both light and dark variants)
- Theme preferences are persisted in the user profile
- Theme changes are applied immediately across the application

### Accessibility Options
- High Contrast: Increases contrast for better readability
- Large Text: Increases text size for better readability
- Reduced Motion: Minimizes animations and transitions
- All accessibility preferences are persisted in the user profile

### Language Settings
- Currently displays the current language (English)
- Placeholder for future language options

## Implementation

### User Context

The `UserContext` provides state management for user preferences across the application. It includes:

- `user`: Object containing user profile information and preferences
- `updateUserPreferences`: Function to update user preferences

The user profile structure is as follows:

```typescript
interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: string;
  avatar?: string;
  preferences: {
    theme: string;
    language?: string;
    notifications?: {
      email: boolean;
      browser: boolean;
      mobile?: boolean;
    };
    accessibility?: {
      highContrast: boolean;
      largeText: boolean;
      reducedMotion: boolean;
    };
  };
}
```

### ThemeSelector Component

The `ThemeSelector` component provides a visual interface for selecting themes. It can be used in two ways:

1. As a dialog trigger with buttons for each theme
2. As a set of buttons for direct theme selection

The component syncs with both the `next-themes` library and the `UserContext` to ensure theme preferences are persisted.

### UserSettings Component

The `UserSettings` component is integrated into the Settings page between the Profile and Notifications sections. It includes:

- Theme selection using the `ThemeSelector` component
- Accessibility toggles for high contrast, large text, and reduced motion
- Language information (currently English only)

## Usage

### Accessing User Settings

1. Navigate to the Settings page
2. Click on "User Settings" in the settings menu
3. Use the theme selector to choose a theme
4. Toggle accessibility options as needed

### Programmatic Access

To access user preferences in components:

```typescript
import { useUser } from '@/contexts/UserContext';

function MyComponent() {
  const { user, updateUserPreferences } = useUser();
  
  // Access user preferences
  const currentTheme = user.preferences.theme;
  
  // Update user preferences
  const handleThemeChange = (theme) => {
    updateUserPreferences({ theme });
  };
  
  // ...
}
```

## Data Persistence

User preferences are stored in the browser's localStorage under the key 'user'. This ensures that preferences persist across sessions.

## Future Enhancements

- Additional themes with more customization options
- More accessibility features
- Multiple language support
- User preference sync across devices via backend storage
