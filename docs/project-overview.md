# ATS Dashboard Guru - Project Overview

## Introduction

ATS Dashboard Guru is a modern Applicant Tracking System (ATS) dashboard built with React, TypeScript, and ShadCN UI components. It provides a comprehensive solution for managing the recruitment process, including candidate tracking, job posting management, interview scheduling, and more.

## Architecture

The application follows a modern React architecture with the following key components:

### Frontend Architecture

- **React**: The core UI library
- **TypeScript**: For type safety and better developer experience
- **React Router**: For client-side routing
- **TanStack Query (React Query)**: For data fetching, caching, and state management
- **Context API**: For global state management
- **Tailwind CSS**: For styling
- **shadcn/ui**: For UI components based on Radix UI primitives

### State Management

The application uses a combination of:

1. **React Context API**: For global state (user preferences, notifications)
2. **TanStack Query**: For server state management
3. **Local Component State**: For UI-specific state

### Data Flow

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  UI Components  │◄────┤  Context API    │◄────┤  Local Storage  │
│                 │     │                 │     │                 │
└────────┬────────┘     └────────┬────────┘     └─────────────────┘
         │                       │
         │                       │
         ▼                       ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  React Query    │◄────┤  API Clients    │◄────┤  Backend API    │
│                 │     │                 │     │  (Future)       │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

## Project Structure

```
ats-dashboard-guru/
├── docs/                  # Documentation files
├── public/                # Static assets
├── src/
│   ├── components/        # Reusable UI components
│   │   ├── candidates/    # Candidate-related components
│   │   ├── common/        # Common UI components
│   │   ├── dashboard/     # Dashboard components
│   │   ├── kanban/        # Kanban board components
│   │   ├── layout/        # Layout components
│   │   ├── notifications/ # Notification components
│   │   ├── search/        # Search components
│   │   ├── settings/      # Settings components
│   │   ├── theme/         # Theme components
│   │   └── ui/            # ShadCN UI components
│   ├── contexts/          # React context providers
│   ├── data/              # Mock data
│   ├── hooks/             # Custom React hooks
│   ├── lib/               # Utility libraries
│   ├── pages/             # Page components
│   ├── themes/            # Theme CSS files
│   ├── types/             # TypeScript type definitions
│   ├── utils/             # Utility functions
│   ├── App.tsx            # Main App component
│   ├── index.css          # Global CSS
│   └── main.tsx           # Entry point
├── .eslintrc.cjs          # ESLint configuration
├── package.json           # Project dependencies
├── tailwind.config.js     # Tailwind CSS configuration
├── tsconfig.json          # TypeScript configuration
├── vite.config.ts         # Vite configuration
└── version.json           # Version information
```

## Key Features

1. **Dashboard Overview**: Get a quick overview of your recruitment pipeline
2. **Candidate Management**: Track and manage candidates through the hiring process
3. **Job Management**: Create and manage job postings
4. **Kanban Board**: Visualize your recruitment pipeline with a drag-and-drop interface
5. **Calendar Integration**: Schedule and manage interviews and events
6. **Messaging System**: Communicate with candidates and team members
7. **Notification System**: Stay updated with real-time notifications
8. **Theme Customization**: Choose from multiple themes including light, dark, and custom themes
9. **User Settings**: Personalize your experience with accessibility options and preferences

## Context Providers

### UserContext

The `UserContext` provides access to the current user's profile and preferences. It includes:

- User profile information (name, email, role)
- User preferences (theme, language, accessibility settings)
- Functions to update user preferences

### NotificationContext

The `NotificationContext` provides access to the user's notifications. It includes:

- List of notifications
- Unread notification count
- Functions to mark notifications as read, archive, or delete them
- Bulk actions for notifications

## Future Plans

Based on the `plans.txt` file, the following features are planned for future releases:

1. **Search & AI Integration**:
   - Instant, typo-tolerant global search using Typesense
   - REST API for automation
   - Configurable AI message assistance

2. **Testing & Optimization**:
   - Load testing
   - API testing
   - Performance optimization

## Development Guidelines

### Code Style

- Use TypeScript for all new code
- Follow the existing component structure
- Use shadcn/ui components for UI elements
- Use React Query for data fetching
- Use Context API for global state

### Adding New Features

1. Create a new branch for your feature
2. Implement the feature
3. Add tests
4. Update documentation
5. Submit a pull request

### Theme Customization

The application supports multiple themes:

- Light theme
- Dark theme
- System theme (follows OS preference)
- Custom themes (e.g., "Stargety")

To add a new theme, follow the pattern in `theme ideas.txt` and add the theme to the `themes` array in `main.tsx`.

## Database Integration (Future)

The application currently uses mock data, but plans to integrate with a proper database in the future. Options being considered include:

1. **Teable.io**: No-code PostgreSQL database with spreadsheet-like interface
2. **Firebase**: NoSQL database with real-time capabilities
3. **MongoDB Atlas**: Document-based database
4. **PostgreSQL with Prisma**: Relational database with type-safe ORM

## Deployment

The application can be deployed to any static site hosting service:

1. Build the project: `npm run build`
2. Deploy the contents of the `dist/` directory to your hosting provider

Popular hosting options include:
- Netlify
- Vercel
- GitHub Pages
- AWS S3 + CloudFront
