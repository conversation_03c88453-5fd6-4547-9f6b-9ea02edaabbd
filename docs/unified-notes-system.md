# Unified Notes System Documentation

## Overview

The unified notes system provides a consistent, reusable solution for managing timestamped notes across different entity types (clients, candidates, etc.) in the ATS Dashboard application.

## Architecture

### Core Principles

1. **DRY (Don't Repeat Yourself)**: Single implementation that works for all entity types
2. **Type Safety**: Full TypeScript support with generics
3. **UI Consistency**: Identical visual design across all entity types
4. **Code Reusability**: Shared components, hooks, and utilities

### File Structure

```
src/
├── types/note.ts                          # Generic note types and configurations
├── services/notesApi.ts                   # Unified API service
├── hooks/useNotes.ts                      # Generic notes hook
├── components/notes/                      # Generic note components
│   ├── NoteCreator.tsx                   # Note creation interface
│   ├── NoteItem.tsx                      # Individual note display
│   ├── NotesTimeline.tsx                 # Notes timeline view
│   └── NotesSection.tsx                  # Complete notes section
├── components/clients/                    # Client-specific wrappers
│   ├── ClientNotesSection.tsx            # Client notes wrapper
│   ├── ClientNoteCreator.tsx             # Client note creator
│   └── ClientNotesTimeline.tsx           # Client notes timeline
└── components/candidates/                 # Updated candidate components
    └── CandidateNoteCreator.tsx          # Updated to use unified system
```

## Components

### Generic Components

#### `NotesSection<T>`
Complete notes interface combining creator and timeline.

```tsx
<NotesSection<ClientNote>
  entityType="client"
  entityId={clientId}
  notes={notes}
  isLoading={isLoading}
  isSubmitting={isSubmitting}
  onAddNote={addNote}
  onUpdateNote={updateNote}
  onDeleteNote={deleteNote}
/>
```

#### `NoteCreator<T>`
Note creation interface with validation and submission.

#### `NotesTimeline<T>`
Timeline view of notes with edit/delete functionality.

#### `NoteItem<T>`
Individual note display with inline editing.

### Entity-Specific Wrappers

#### `ClientNotesSection`
```tsx
<ClientNotesSection clientId={clientId} />
```

#### `ClientNoteCreator`
```tsx
<ClientNoteCreator 
  clientId={clientId}
  onNoteAdded={(note) => console.log('Note added:', note.id)}
/>
```

## Hooks

### `useClientNotes(clientId: string)`
```tsx
const {
  notes,
  isLoading,
  isSubmitting,
  addNote,
  updateNote,
  deleteNote,
  refreshNotes,
  notesCount
} = useClientNotes(clientId);
```

### `useCandidateNotes(candidateId: string)`
Maintains backward compatibility with existing candidate notes.

## API Endpoints

### Client Notes
- `GET /api/client-notes/client/:clientId` - Get all notes for a client
- `POST /api/client-notes` - Create a new client note
- `PUT /api/client-notes/:id` - Update a client note
- `DELETE /api/client-notes/:id` - Delete a client note

### Candidate Notes
- `GET /api/candidate-notes/candidate/:candidateId` - Get all notes for a candidate
- `POST /api/candidate-notes` - Create a new candidate note
- `PUT /api/candidate-notes/:id` - Update a candidate note
- `DELETE /api/candidate-notes/:id` - Delete a candidate note

## Database Schema

### Client Notes Table
```sql
CREATE TABLE client_notes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### Candidate Notes Table
```sql
CREATE TABLE candidate_notes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  candidate_id UUID NOT NULL REFERENCES candidates(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## Usage Examples

### Adding Notes to Client Details Page

```tsx
import { ClientNotesSection } from '@/components/clients/ClientNotesSection';

// In your component
<TabsContent value="notes">
  <ClientNotesSection clientId={client.id} />
</TabsContent>
```

### Adding Quick Note Creator

```tsx
import { ClientNoteCreator } from '@/components/clients/ClientNoteCreator';

<ClientNoteCreator
  clientId={client.id}
  onNoteAdded={(note) => {
    // Switch to notes tab and highlight new note
    setActiveTab('notes');
    setHighlightedNoteId(note.id);
  }}
/>
```

## Features

### User Experience
- **Inline Editing**: Click to edit notes directly in the timeline
- **Real-time Updates**: Immediate UI updates with optimistic rendering
- **Keyboard Shortcuts**: Ctrl+Enter to submit, Esc to cancel
- **Visual Feedback**: Highlighting for newly added notes
- **Responsive Design**: Works on all screen sizes

### Security
- **User Authorization**: Users can only edit/delete their own notes
- **Input Validation**: Content validation on both client and server
- **SQL Injection Protection**: Parameterized queries

### Performance
- **Optimistic Updates**: UI updates immediately before server confirmation
- **Efficient Queries**: Indexed database queries with user joins
- **Lazy Loading**: Notes loaded only when needed

## Extending the System

### Adding New Entity Types

1. **Add to types**: Update `NoteEntityType` and `NOTE_CONFIGS`
2. **Create API methods**: Add to `notesApi.ts`
3. **Add database table**: Create migration with proper schema
4. **Add server endpoints**: Follow the existing pattern
5. **Create wrapper components**: Entity-specific convenience components

### Example: Adding Job Notes

```typescript
// 1. Update types
export type NoteEntityType = 'client' | 'candidate' | 'job';

export const NOTE_CONFIGS: Record<NoteEntityType, NoteConfig> = {
  // ... existing configs
  job: {
    entityType: 'job',
    entityIdField: 'job_id',
    apiEndpoint: '/job-notes',
    displayName: 'Job',
    placeholder: 'Add a note about this job...'
  }
};

// 2. Create hook
export function useJobNotes(jobId: string): UseNotesReturn<JobNote> {
  return useNotesGeneric<JobNote>({
    entityType: 'job',
    entityId: jobId,
    api: jobNotesApi,
  });
}

// 3. Create wrapper component
export function JobNotesSection({ jobId }: { jobId: string }) {
  const { notes, isLoading, /* ... */ } = useJobNotes(jobId);
  
  return (
    <NotesSection<JobNote>
      entityType="job"
      entityId={jobId}
      // ... other props
    />
  );
}
```

## Migration Guide

### From Legacy Candidate Notes
The system maintains backward compatibility. Existing candidate note components will continue to work but now use the unified system internally.

### From Static Notes Fields
For entities with static notes fields (like `client.notes`), the system can display both:
- Legacy notes from the static field
- New timeline-based notes

This allows for gradual migration without data loss.

## Best Practices

1. **Use Entity-Specific Wrappers**: Prefer `ClientNotesSection` over generic `NotesSection`
2. **Handle Loading States**: Always show loading indicators
3. **Provide Feedback**: Use toast notifications for user actions
4. **Maintain Consistency**: Follow the established UI patterns
5. **Test Thoroughly**: Verify all CRUD operations work correctly

## Troubleshooting

### Common Issues

1. **Notes not loading**: Check API endpoints and database connections
2. **Permission errors**: Verify user authentication and authorization
3. **UI not updating**: Ensure proper state management and re-renders
4. **Database errors**: Check foreign key constraints and table existence

### Debug Tools

- Browser console logs with detailed API calls
- Network tab to inspect API requests/responses
- Database query logs for server-side debugging
