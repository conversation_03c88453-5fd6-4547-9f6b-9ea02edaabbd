# Guía de Despliegue de ATS Dashboard Guru en tu VPS

Esta guía proporciona instrucciones paso a paso para desplegar ATS Dashboard Guru con Teable en un servidor VPS.

## Índice

1. [Requisitos Previos](#requisitos-previos)
2. [Preparación del Servidor](#preparación-del-servidor)
3. [Instalación de Docker y Docker Compose](#instalación-de-docker-y-docker-compose)
4. [Configuración del Proyecto](#configuración-del-proyecto)
5. [Despliegue de la Aplicación](#despliegue-de-la-aplicación)
6. [Configuración de Teable](#configuración-de-teable)
7. [Configuración de Seguridad](#configuración-de-seguridad)
8. [Mantenimiento](#mantenimiento)
9. [Solución de Problemas](#solución-de-problemas)

## Requisitos Previos

Antes de comenzar, asegúrate de tener:

- Un servidor VPS con al menos 2GB de RAM y 2 núcleos de CPU
- Un nombre de dominio (opcional pero recomendado)
- Acceso SSH al servidor
- Permisos de administrador (sudo)

## Preparación del Servidor

1. **Actualiza el sistema**:

   ```bash
   sudo apt update
   sudo apt upgrade -y
   ```

2. **Instala dependencias básicas**:

   ```bash
   sudo apt install -y curl git wget nano unzip
   ```

3. **Configura la zona horaria**:

   ```bash
   sudo timedatectl set-timezone America/Mexico_City  # Ajusta según tu ubicación
   ```

## Instalación de Docker y Docker Compose

1. **Instala Docker**:

   ```bash
   # Añade la clave GPG oficial de Docker
   sudo apt install -y apt-transport-https ca-certificates curl software-properties-common
   curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -
   
   # Añade el repositorio de Docker
   sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"
   
   # Actualiza e instala Docker
   sudo apt update
   sudo apt install -y docker-ce docker-ce-cli containerd.io
   
   # Inicia y habilita Docker
   sudo systemctl start docker
   sudo systemctl enable docker
   
   # Añade tu usuario al grupo docker (para ejecutar Docker sin sudo)
   sudo usermod -aG docker $USER
   ```

   **Nota**: Después de añadir tu usuario al grupo docker, cierra la sesión y vuelve a iniciarla para que los cambios surtan efecto.

2. **Instala Docker Compose**:

   ```bash
   # Descarga la última versión de Docker Compose
   COMPOSE_VERSION=$(curl -s https://api.github.com/repos/docker/compose/releases/latest | grep 'tag_name' | cut -d\" -f4)
   sudo curl -L "https://github.com/docker/compose/releases/download/${COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   
   # Haz ejecutable el binario
   sudo chmod +x /usr/local/bin/docker-compose
   
   # Verifica la instalación
   docker-compose --version
   ```

## Configuración del Proyecto

1. **Clona el repositorio**:

   ```bash
   # Crea un directorio para el proyecto
   mkdir -p /opt/ats-dashboard
   cd /opt/ats-dashboard
   
   # Clona el repositorio
   git clone https://github.com/c42705/ats-dashboard-guru.git .
   ```

2. **Configura las variables de entorno**:

   ```bash
   # Copia el archivo de ejemplo
   cp .env.example .env
   
   # Edita el archivo .env
   nano .env
   ```

   Ajusta las siguientes variables en el archivo `.env`:

   ```
   # API URL (ajusta según tu dominio)
   VITE_API_URL=https://tu-dominio.com/api
   
   # Database Configuration
   VITE_DATABASE_TYPE=teable
   
   # Teable.io Configuration
   VITE_TEABLE_URL=https://tu-dominio.com/teable
   VITE_TEABLE_API_KEY=tu-api-key-generada-por-teable
   VITE_TEABLE_BASE_ID=tu-base-id
   
   # Otros ajustes según sea necesario
   ```

3. **Configura el archivo docker-compose.yml**:

   ```bash
   # Edita el archivo docker-compose.yml
   nano docker-compose.yml
   ```

   Realiza los siguientes ajustes:

   - Actualiza las rutas de volúmenes si es necesario
   - Configura los puertos según tus necesidades
   - Ajusta las variables de entorno para producción

## Despliegue de la Aplicación

1. **Construye e inicia los contenedores**:

   ```bash
   # Ejecuta el script de despliegue
   bash deploy.sh
   ```

   O manualmente:

   ```bash
   # Construye e inicia los contenedores
   docker-compose up -d
   ```

2. **Verifica que los contenedores estén funcionando**:

   ```bash
   docker-compose ps
   ```

   Deberías ver todos los servicios en estado "Up".

3. **Verifica los logs para detectar posibles errores**:

   ```bash
   # Logs de todos los servicios
   docker-compose logs
   
   # Logs de un servicio específico (por ejemplo, teable)
   docker-compose logs teable
   ```

## Configuración de Teable

1. **Accede a la interfaz de Teable**:

   Abre tu navegador y visita `http://tu-ip-o-dominio:3333` o la URL que hayas configurado.

2. **Crea un usuario administrador**:

   Sigue las instrucciones en pantalla para crear el primer usuario administrador.

3. **Configura la base de datos**:

   - Crea una nueva base (Base)
   - Configura las tablas necesarias según la estructura de datos de ATS Dashboard Guru
   - Configura las vistas y relaciones entre tablas

4. **Genera un token de API**:

   - Ve a Configuración > Tokens de API
   - Crea un nuevo token con los permisos necesarios
   - Copia el token generado y actualízalo en el archivo `.env` (VITE_TEABLE_API_KEY)

5. **Obtén el ID de la base**:

   - El ID de la base se encuentra en la URL cuando estás viendo la base
   - Por ejemplo, en `http://localhost:3333/base/bseRg2INvUDRQ3ejV6d/`, el ID es `bseRg2INvUDRQ3ejV6d`
   - Actualiza este ID en el archivo `.env` (VITE_TEABLE_BASE_ID)

6. **Reinicia los servicios para aplicar los cambios**:

   ```bash
   docker-compose down
   docker-compose up -d
   ```

## Configuración de Seguridad

1. **Configura un proxy inverso (Nginx)**:

   ```bash
   # Instala Nginx
   sudo apt install -y nginx
   
   # Crea una configuración para el sitio
   sudo nano /etc/nginx/sites-available/ats-dashboard
   ```

   Añade la siguiente configuración:

   ```nginx
   server {
       listen 80;
       server_name tu-dominio.com;
   
       location / {
           proxy_pass http://localhost:80;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
   
       location /teable/ {
           proxy_pass http://localhost:3333/;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection "upgrade";
           proxy_http_version 1.1;
       }
   }
   ```

   Activa la configuración:

   ```bash
   sudo ln -s /etc/nginx/sites-available/ats-dashboard /etc/nginx/sites-enabled/
   sudo nginx -t  # Verifica la configuración
   sudo systemctl restart nginx
   ```

2. **Configura SSL con Let's Encrypt**:

   ```bash
   # Instala Certbot
   sudo apt install -y certbot python3-certbot-nginx
   
   # Obtén un certificado SSL
   sudo certbot --nginx -d tu-dominio.com
   
   # Sigue las instrucciones en pantalla
   ```

3. **Configura un firewall**:

   ```bash
   # Instala y configura UFW
   sudo apt install -y ufw
   
   # Permite SSH, HTTP y HTTPS
   sudo ufw allow ssh
   sudo ufw allow http
   sudo ufw allow https
   
   # Habilita el firewall
   sudo ufw enable
   ```

## Mantenimiento

1. **Actualización de la aplicación**:

   ```bash
   # Navega al directorio del proyecto
   cd /opt/ats-dashboard
   
   # Obtén los últimos cambios
   git pull
   
   # Reconstruye e inicia los contenedores
   docker-compose down
   docker-compose up -d --build
   ```

2. **Copias de seguridad**:

   ```bash
   # Copia de seguridad de la base de datos PostgreSQL
   docker-compose exec postgres pg_dump -U postgres postgres > backup_$(date +%Y%m%d).sql
   
   # Copia de seguridad de los volúmenes de Docker
   sudo tar -czvf docker_volumes_$(date +%Y%m%d).tar.gz /var/lib/docker/volumes
   ```

3. **Monitoreo**:

   ```bash
   # Monitorea el uso de recursos
   docker stats
   
   # Verifica el estado de los contenedores
   docker-compose ps
   
   # Revisa los logs
   docker-compose logs -f
   ```

## Solución de Problemas

### Problemas comunes y soluciones

1. **Los contenedores no inician**:

   ```bash
   # Verifica los logs
   docker-compose logs
   
   # Verifica si hay conflictos de puertos
   sudo netstat -tulpn | grep -E '80|3333|5432|6379|9000'
   ```

2. **No se puede acceder a Teable**:

   ```bash
   # Verifica que el contenedor esté funcionando
   docker-compose ps teable
   
   # Verifica los logs
   docker-compose logs teable
   
   # Verifica la configuración de puertos
   docker-compose exec teable netstat -tulpn
   ```

3. **Problemas de conexión entre contenedores**:

   ```bash
   # Verifica la red de Docker
   docker network inspect ats-dashboard-guru_default
   
   # Prueba la conectividad entre contenedores
   docker-compose exec frontend ping teable
   ```

4. **Problemas con la base de datos**:

   ```bash
   # Verifica el estado de PostgreSQL
   docker-compose exec postgres pg_isready -U postgres
   
   # Verifica los logs
   docker-compose logs postgres
   ```

### Contacto y soporte

Si encuentras problemas que no puedes resolver, puedes:

1. Revisar la [documentación oficial de Teable](https://help.teable.io/)
2. Unirte al [canal de Discord de Teable](https://discord.gg/uZwp7tDE5W)
3. Abrir un issue en el [repositorio de GitHub](https://github.com/c42705/ats-dashboard-guru)
4. Contactar al equipo de <NAME_EMAIL>

## Conclusión

Has completado el despliegue de ATS Dashboard Guru con Teable en tu VPS. La aplicación ahora debería estar accesible a través de tu dominio y lista para ser utilizada.

Recuerda mantener actualizado el sistema y realizar copias de seguridad periódicas para garantizar la seguridad y disponibilidad de tus datos.
