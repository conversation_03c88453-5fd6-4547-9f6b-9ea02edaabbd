# API Server Documentation

This document provides information about the API server used in the ATS Dashboard Guru application.

## Overview

The API server is a Node.js Express server that provides RESTful API endpoints for interacting with the PostgreSQL database. It serves as the backend for the ATS Dashboard Guru application.

## Architecture

The API server is implemented using the following technologies:

- **Node.js**: JavaScript runtime
- **Express.js**: Web framework for Node.js
- **PostgreSQL**: Relational database
- **pg**: PostgreSQL client for Node.js

## Server Setup

The API server is implemented in the `src/server/server.cjs` file. It uses the following components:

- **Express Application**: The main Express application that handles HTTP requests
- **PostgreSQL Connection Pool**: A connection pool for the PostgreSQL database
- **Query Builder**: A utility class for building SQL queries
- **API Endpoints**: RESTful API endpoints for interacting with the database

## Starting the Server

To start the API server, run the following command:

```bash
npm run server
```

This will start the API server on port 3001 (or the port specified in the `VITE_SERVER_PORT` environment variable).

## API Endpoints

The API server provides the following endpoints:

### Health Check

- `GET /health`: Get the health status of the API server

### Candidates

- `GET /api/candidates`: Get all candidates
  - Query Parameters:
    - `limit`: Maximum number of records to return
    - `offset`: Number of records to skip
    - `sort`: Sort order (e.g., `name_asc`, `name_desc`)
    - `status`: Filter by status
- `GET /api/candidates/:id`: Get a candidate by ID
- `POST /api/candidates`: Create a new candidate
  - Required Fields:
    - `first_name`: First name of the candidate
    - `last_name`: Last name of the candidate
    - `email`: Email address of the candidate
- `PUT /api/candidates/:id`: Update a candidate (not implemented yet)
- `DELETE /api/candidates/:id`: Delete a candidate (not implemented yet)

### Jobs

- `GET /api/jobs`: Get all jobs
  - Query Parameters:
    - `limit`: Maximum number of records to return
    - `offset`: Number of records to skip
    - `sort`: Sort order (e.g., `title_asc`, `title_desc`, `priority_high`)
    - `status`: Filter by status
    - `client_id`: Filter by client ID
- `GET /api/jobs/:id`: Get a job by ID
- `POST /api/jobs`: Create a new job
  - Required Fields:
    - `title`: Title of the job
- `PUT /api/jobs/:id`: Update a job (not implemented yet)
- `DELETE /api/jobs/:id`: Delete a job (not implemented yet)

### Clients

- `GET /api/clients`: Get all clients
  - Query Parameters:
    - `limit`: Maximum number of records to return
    - `offset`: Number of records to skip
    - `sort`: Sort order (e.g., `name_asc`, `name_desc`)
    - `status`: Filter by status
- `GET /api/clients/:id`: Get a client by ID
- `POST /api/clients`: Create a new client
  - Required Fields:
    - `company_name`: Name of the company
- `PUT /api/clients/:id`: Update a client (not implemented yet)
- `DELETE /api/clients/:id`: Delete a client (not implemented yet)

### Job Applications

- `GET /api/job-applications`: Get all job applications (not implemented yet)
- `GET /api/job-applications/:id`: Get a job application by ID (not implemented yet)
- `POST /api/job-applications`: Create a new job application (not implemented yet)
- `PUT /api/job-applications/:id`: Update a job application (not implemented yet)
- `DELETE /api/job-applications/:id`: Delete a job application (not implemented yet)

## Query Builder

The API server includes a query builder utility class for building SQL queries. This class is used to build SELECT and INSERT queries for the database.

### Usage

```javascript
// Create a new query builder for the 'clients' table
const queryBuilder = new QueryBuilder('clients');

// Add a WHERE clause
queryBuilder.where('status', '=', 'active');

// Add an ORDER BY clause
queryBuilder.orderBy('company_name', 'ASC');

// Add a LIMIT clause
queryBuilder.limit(10);

// Add an OFFSET clause
queryBuilder.offset(0);

// Build a SELECT query
const { text, params } = queryBuilder.buildSelect();

// Execute the query
const result = await query(text, params);
```

## Error Handling

The API server includes error handling middleware that catches any errors that occur during request processing and returns an appropriate error response.

## Database Connection

The API server uses a PostgreSQL connection pool to manage database connections. The connection pool is configured using environment variables:

```javascript
const pool = new Pool({
  host: process.env.VITE_POSTGRES_HOST || 'localhost',
  port: parseInt(process.env.VITE_POSTGRES_PORT || '5432'),
  database: process.env.VITE_POSTGRES_DATABASE || 'postgres',
  user: process.env.VITE_POSTGRES_USER || 'postgres',
  password: process.env.VITE_POSTGRES_PASSWORD || 'postgres',
  ssl: process.env.VITE_POSTGRES_SSL === 'true',
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000
});
```

## Transaction Support

The API server includes support for database transactions. Transactions are used to ensure that multiple database operations are executed atomically.

```javascript
// Execute a transaction
const result = await withTransaction(async (client) => {
  // Execute database operations using the client
  const result1 = await client.query('INSERT INTO clients (name) VALUES ($1) RETURNING id', ['Client 1']);
  const result2 = await client.query('INSERT INTO jobs (title, client_id) VALUES ($1, $2) RETURNING id', ['Job 1', result1.rows[0].id]);
  return result2.rows[0];
});
```

## Further Reading

- [Express.js Documentation](https://expressjs.com/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [pg Documentation](https://node-postgres.com/)
