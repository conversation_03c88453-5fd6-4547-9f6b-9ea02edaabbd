# Development Approach
- Be autonomous, follow plans, solve problems independently, and only commit complete functional work.
- Implement plans in order, reference documentation during development, test progress regularly, and check with user for feedback before continuing.
- User prefers a structured implementation approach (analyze, plan, implement, test) with clear testing instructions for each component change.
- User prefers longer wait times (at least 50 seconds instead of 30) when waiting for processes to complete.
- User prefers to test Docker configurations locally before deploying to VPS to verify functionality.
- User prefers simple HTML files for testing API connections independently from the main application.
- User expects the application to be pre-configured and ready to run from Docker even after a clean installation, with proper database initialization.
- User uses the Spanish phrase 'echalo a volar' to mean 'run/execute the project'.
- User prefers to have both Docker production environment and Vite development server (npx vite) running simultaneously for development workflow.
- User wants to deploy the Docker application to production using port forwarding on their router to make it publicly accessible from the internet.
- User prefers using 'npx vite preview' for quick development testing to avoid frequent container rebuilds, then doing final Docker builds for localhost testing.

# Code Quality & Cleanup
- User prefers production-ready codebases with console.log statements wrapped in development-only conditions, removal of test artifacts and temporary files, organized migration sequences, and comprehensive cleanup including unused imports, commented code, and duplicate files.
- User prefers production-ready codebases with cleanup of non-essential code, removal of safe-to-delete files, and elimination of development artifacts while maintaining all functionality.
- User wants to add generated/compiled files to .gitignore to exclude them from version control, including Playwright test reports and coverage files.
- User prefers standardized localStorage keys with consistent naming conventions, application-specific prefixes, and centralized management with proper error handling and automatic validation.
- User prefers to execute database cleanup scripts inside Docker containers rather than from the host system, and expects cleanup of temporary development files.

# Database Strategy
- User wants to migrate the project to PostgreSQL exclusively, removing all Teable components and establishing a direct PostgreSQL implementation.
- User operates exclusively in production mode with PostgreSQL as the only database solution, removing all development/localStorage mode functionality.
- User prefers environment variables to be defined only in .env files, not in system environment variables.
- User wants to implement a data saving system for entities with proper relationship modeling in PostgreSQL database.
- User prefers tables to be created automatically via API when needed rather than manually.
- User wants automated database initialization in Docker with entrypoint scripts, healthchecks, connection retries, and enhanced error handling with specific error messages.
- User wants npm scripts for database cleanup operations (e.g., dropping all data from specific tables like candidates).
- User prefers to always use Docker containers for PostgreSQL database operations, with .env file configured for Docker hostnames, and all database-related commands should be executed inside the Docker containers rather than on the host system.
- Docker containers are running successfully with API on localhost:3001, database on postgres:5432, and frontend on localhost:80, but there's a database constraint issue with secondary_status field rejecting "with reservations" value.
- User prefers to include pgAdmin service in Docker Compose for database administration access.

# User Interface Standards
- Use only hexadecimal color values in the project.
- User prefers simplified UI with fewer buttons and maintaining design hierarchy.
- User prefers status indicators to be placed outside buttons rather than inside them.
- Standardize the top section layout and component organization across jobs, candidates, and clients pages.
- Style list/kanban views with bg-muted background and padding for consistency.
- User prefers modals to have a maximum height of 90vh with scrolling functionality to prevent oversized dialogs.
- User prefers to display placeholder messages for missing information in detail view pages rather than hiding those fields.
- User prefers primary action buttons (like Edit Candidate) to remain in the top section with primary color styling rather than being moved to sidebar quick actions.
- User prefers edit modals with pre-populated fields, comprehensive field exposure organized in logical sections, unique HTML IDs for programmatic access, simplified two-tier status selection interface, and proper validation while maintaining clean organized layouts.
- User prefers comprehensive detail views with tabbed organization (Basic Info, Professional, Social & Portfolio, Assessment, Process, Intake Responses), 'N/A' placeholders with muted styling for null/empty values, and complete database field exposure in detail components.

# Theme Management
- User prefers a custom theme management solution using CSS theme files and toggling the .dark class based on localStorage values.
- Dark and light are modes of each theme, not separate themes themselves; themes are defined in src/themes/ directory.
- ThemeMode should only store 'light' or 'dark' values, while themeStyle should store the selected CSS theme name.
- User prefers visual theme cards in ThemeSelector.tsx over radio buttons for theme selection.

# AG Grid Configuration
- When using AG Grid, modules must be registered by importing and registering AllCommunityModule.
- User prefers AG Grid themes to be implemented in a separate file that responds to the toggletheme setup by checking if the HTML contains the .dark class.
- User prefers AG Grid checkboxSelection is deprecated as of v32.2 and should be replaced with rowSelection.checkboxes in GridOptions.
- User wants AG Grid tables to implement modern row selection with checkboxes in the first column, header checkbox for selecting all rows, and bulk delete functionality with confirmation modal.
- User prefers AG Grid cellEditor with dropdown selectors for status fields, using agSelectCellEditor with dynamic valueListGetter for secondary status filtering based on primary status selection, following AG Grid best practices for maintainability.
- User prefers following official AG Grid documentation for cell editors, specifically referencing the provided select cell editor documentation at ag-grid.com for proper implementation patterns.
- User prefers 'View Details' links to be placed inside dropdown menus rather than as standalone links in AG Grid action columns.
- User prefers AG Grid tables with separate primary and secondary status columns.
- User prefers using the official AG Grid Enterprise tool panel (https://ag-grid.com/react-data-grid/tool-panel-columns/) over custom column visibility implementations.
- User wants side panel functionality enabled for column visibility toggles and reordering capabilities.
- User prefers AG Grid column visibility panels implemented as full-height slide-in drawers from the right with search functionality, real-time column toggling, backdrop closing, and localStorage persistence for column preferences.
- User prefers to include most database columns in column visibility except system metadata (id, result, notes/feedback columns), with updated_at hidden by default but available.

# Candidate Schema & Progress Tracking
- User wants to add specific fields to the candidate schema including name, location, status, phone, email, portfolio, duplicate status, English screen, interview score, challenge details, feedback, and results.
- User prefers making last_name field optional for candidates, implementing flexible URL validation that accepts various formats, and automatically adding https:// prefix to URLs when missing.
- User wants to implement a two-tier candidate status system with primary statuses (pipeline stages) and secondary statuses (detailed tracking within stages) that must accommodate existing import values.
- User prefers email field to be optional rather than required in candidate data validation.
- User expects the Stargety ID field to be properly saved to the database when edited in candidate forms, ensuring data persistence for this internal identifier.
- User wants to replace the 'Add to Talent Pool' button in CandidateDetails.tsx with a comprehensive 'Talent Incubator' system featuring progress tracking with notes/journal, document management with upload/download, email integration with templates, task assignment with milestones, and completion certification with customizable templates.
- User expects both add and remove functionality for the Talent Incubator feature, not just one-way addition.
- User prefers Talent Incubator implementation with boolean database fields, visual indicators using gold/amber borders or sprout badges on profile pictures, AG Grid action menus for removal functionality, and confirmation dialogs for destructive actions.
- User prefers removal actions for incubator status to be placed in AG Grid actions column rather than in detail view cards.

# CSV Import
- User prefers CSV import solutions with data preview before import, correction of data misalignments/mapping issues, minimal user steps, integration with existing API endpoints, and complete removal of old implementations during redesigns.
- User prefers to fill empty required fields with clearly labeled dummy data during imports to ensure successful processing.
- User prefers to verify data mappings by cross-referencing with a mappings.csv file in the docs folder, checking consistency across database, frontend, API, and other system components before testing.

# Testing
- User prefers Playwright for E2E testing with visual testing suites to verify UI and functionality.
- User prefers Playwright for E2E testing with focus on candidates functionality: creation, editing, details, list view, and table view of candidates.

# Other
- Stargety is the correct name of the company, and 'Stargety ID' should be used for the internal user identification system instead of 'Strategy ID' in database schema and UI.
- User prefers simplified Docker setup without profiles, only production environment needed.

¡¡¡¡¡¡¡¡¡¡¡¡¡¡

---cuando escriba "echalo a volar" me refiero a ejecutar el proyecto  ---considera ejecutar todos los comando de consola usando powershell y no linux commands. ---para ejecutar comandos seguidos, no uses "&&" sino ";"--- cuando haya errores, elabora planes de accion y razonamiento detras de las soluciones para elegir la mejor y tener alternativas para su solucion. evita soluciones drasticas a menos que la situacion lo amerita y corrobora con el usuario antes de hacer cambios mayores. ---el ambiente docker sera el mas relevante e importante de que siempre funcione, ya que sera nuestro objetivo de produccion.  --- Meaningful Naming: Variables, functions, and classes should be self-documenting (e.g., calculateTotalPrice()).


DRY Principle: Avoid duplication; reuse code via functions/modules.

SOLID Principles: Follow object-oriented design best practices.

3. Documentation
Inline Comments: Explain why complex logic exists, not what it does.

README.md:

Keep Docs Updated: Ensure documentation evolves with the code.

5. Logging & Monitoring
Structured Logging: Use JSON/logfmt for easy parsing (e.g., {"level": "error", "message": ...}).

Log Levels:

DEBUG for development details.

INFO for key events.

WARN for potential issues.

ERROR for recoverable failures.

FATAL for critical crashes.

Avoid Sensitive Data: Never log passwords, tokens, or PII.

6. Security
Secrets Management: Store API keys, passwords, etc., in environment variables or vaults (never in code).

Input Validation: Sanitize user inputs to prevent SQLi, XSS, etc.

Dependency Scanning: Use tools like Dependabot or Snyk to patch vulnerabilities.

Principle of Least Privilege: Restrict permissions for services/users.

7. Dependency Management
Pin Versions: Use exact versions in requirements.txt or package.json.

Update Regularly: Patch dependencies to address security/issues.

Minimize Bloat: Avoid unnecessary third-party libraries.

8. Environment & Configuration
Environment-Specific Configs: Use .env files (excluded via .gitignore).

Reproducible Builds: Use Docker or containerization for consistency.

9. Error Handling
Graceful Degradation: Handle failures without crashing (e.g., retries, fallbacks).

Meaningful Errors: Provide actionable messages for debugging.

10. Collaboration & Workflow

Changelogs: Document user-facing changes for releases.

11. Performance
Optimize Judiciously: Prioritize readability over premature optimization.
all files must be under 490 lines of code, use references and imports to reduce size, as needed, withou compromising functionality

Caching: Cache frequent operations where appropriate.

12. Repository Hygiene
.gitignore: Exclude build artifacts, logs, and IDE files.