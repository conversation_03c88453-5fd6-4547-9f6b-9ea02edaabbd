# Docker Desktop Setup

This document explains how to configure the ATS Dashboard Guru application to work with Docker Desktop, ensuring all containers are visible in the Docker Desktop interface.

## Overview

The application has been configured to use Docker Desktop context instead of the system Docker daemon. This provides better integration with Docker Desktop's GUI and makes container management easier.

## Quick Start

### 1. Start with Docker Desktop
```bash
# Start the application with Docker Desktop
./start-docker-desktop.sh
```

### 2. Stop the Application
```bash
# Stop all containers
./stop-docker.sh
```

## Manual Configuration

If you need to manually configure Docker Desktop context:

### 1. Setup Docker Desktop Context
```bash
# Run the setup script
./scripts/setup-docker-desktop.sh
```

### 2. Verify Configuration
```bash
# Check active context (should show desktop-linux with *)
docker context ls
```

### 3. Start Services
```bash
# For development (with exposed ports)
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# For production
docker-compose up -d
```

## Configuration Changes Made

### 1. VS Code Settings (`.vscode/settings.json`)
- Removed `DOCKER_HOST` environment variable override
- Kept `DOCKER_BUILDKIT=1` for improved build performance

### 2. Launch Configuration (`.vscode/launch.json`)
- Removed `DOCKER_HOST` override from debug configuration

### 3. New Scripts
- `scripts/setup-docker-desktop.sh` - Configures Docker Desktop context
- `start-docker-desktop.sh` - Starts application with Docker Desktop
- `stop-docker.sh` - Stops application containers

### 4. Updated Deployment Script (`deploy.sh`)
- Added Docker Desktop context configuration
- Unsets `DOCKER_HOST` to avoid conflicts

## Troubleshooting

### Containers Not Visible in Docker Desktop
1. Ensure Docker Desktop is running
2. Run the setup script: `./scripts/setup-docker-desktop.sh`
3. Verify context: `docker context ls`
4. Restart containers: `./start-docker-desktop.sh`

### Context Switching Issues
```bash
# Force switch to Docker Desktop
docker context use desktop-linux

# Unset conflicting environment variable
unset DOCKER_HOST

# Verify
docker context ls
```

### Environment Variable Conflicts
If you see warnings about `DOCKER_HOST` overriding context:
```bash
# Temporarily unset for current session
unset DOCKER_HOST

# Or permanently remove from shell profile
# Edit ~/.bashrc, ~/.zshrc, etc. and remove DOCKER_HOST export
```

## Benefits of Docker Desktop Integration

1. **Visual Container Management** - See all containers in Docker Desktop GUI
2. **Easy Log Access** - View container logs through the interface
3. **Resource Monitoring** - Monitor CPU, memory usage per container
4. **Volume Management** - Easily manage Docker volumes
5. **Network Inspection** - View container networks and connections

## Development vs Production

### Development Mode
- Uses `docker-compose.dev.yml` override
- Exposes database and service ports for local access
- Includes pgAdmin for database management

### Production Mode
- Uses base `docker-compose.yml` only
- Only exposes frontend (port 80) and API (port 3001)
- Internal service communication only

## Service Access

When running in development mode:
- **Frontend**: http://localhost (port 80)
- **API**: http://localhost:3001
- **pgAdmin**: http://localhost:5050
- **MinIO Console**: http://localhost:9001
- **Typesense**: http://localhost:8108
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379
