# ATS Dashboard Guru

A modern Applicant Tracking System (ATS) dashboard built with React, TypeScript, and ShadCN UI components, featuring PostgreSQL integration for data storage.

## Project info

**URL**: https://lovable.dev/projects/af4db429-4f27-4467-b746-c91dbcac93e4

## Features

- **Dashboard Overview**: Get a quick overview of your recruitment pipeline
- **Candidate Management**: Track and manage candidates through the hiring process
- **Job Management**: Create and manage job postings
- **Kanban Board**: Visualize your recruitment pipeline with a drag-and-drop interface
- **Calendar Integration**: Schedule and manage interviews and events
- **Messaging System**: Communicate with candidates and team members
- **Notification System**: Stay updated with real-time notifications
- **Theme Customization**: Choose from multiple themes including light, dark, and custom themes
- **User Settings**: Personalize your experience with accessibility options and preferences
- **Global Search**: Instantly search across all data with typo tolerance (Ctrl+K / Cmd+K)
- **AI-Powered Message Assistance**: Get AI-generated suggestions to enhance your messages
- **PostgreSQL Integration**: Store and manage data with PostgreSQL, a robust relational database
- **API Server**: RESTful API server for database operations

## Complete Setup Guide (For New Users)

This guide will help you set up the project from scratch, even if you don't have any development tools installed on your machine.

### 1. Install Git

Git is a version control system that helps you track changes to your code.

**For Windows:**
1. Download Git from [git-scm.com](https://git-scm.com/download/win)
2. Run the installer with default settings
3. Open Command Prompt or PowerShell to verify installation by typing: `git --version`

**For macOS:**
1. Install Homebrew (if not already installed) by opening Terminal and running:
   ```
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   ```
2. Install Git using Homebrew:
   ```
   brew install git
   ```
3. Verify installation: `git --version`

**For Linux (Ubuntu/Debian):**
1. Open Terminal and run:
   ```
   sudo apt update
   sudo apt install git
   ```
2. Verify installation: `git --version`

### 2. Install Node.js and npm

Node.js is the JavaScript runtime that powers the project, and npm is the package manager.

**For Windows:**
1. Download the LTS version from [nodejs.org](https://nodejs.org/)
2. Run the installer with default settings
3. Verify installation by opening a new Command Prompt or PowerShell window and typing:
   ```
   node --version
   npm --version
   ```

**For macOS:**
1. Using Homebrew:
   ```
   brew install node@16
   ```
2. Verify installation:
   ```
   node --version
   npm --version
   ```

**For Linux (Ubuntu/Debian):**
1. Install Node.js 16.x:
   ```
   curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
   sudo apt install -y nodejs
   ```
2. Verify installation:
   ```
   node --version
   npm --version
   ```

### 3. Clone and Set Up the Project

Now that you have Git and Node.js installed, you can set up the project:

```sh
# Step 1: Clone the repository
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory
cd ats-dashboard-guru

# Step 3: Create a local development branch
git checkout -b local_development

# Step 4: Install the necessary dependencies
npm install

# Step 5: Start the development server
npm run dev
```

After running these commands, the application should be available at http://localhost:3000 in your web browser.

### Troubleshooting Common Issues

**Node.js Version Compatibility:**
This project works best with Node.js version 16.x. If you encounter errors related to Node.js versions, consider installing Node.js 16.x specifically.

**Port Already in Use:**
If port 3000 is already in use on your machine, you can modify the port in `vite.config.ts` by changing the port number.

**Dependencies Installation Errors:**
If you encounter errors during `npm install`, try running:
```
npm install --force
```

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/af4db429-4f27-4467-b746-c91dbcac93e4) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

After following the setup instructions above, you can use any code editor like Visual Studio Code, Sublime Text, or Atom to edit the files locally.

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## Tech Stack

- **Frontend Framework**: React with TypeScript
- **Build Tool**: Vite
- **UI Components**: ShadCN UI (Radix UI + Tailwind CSS)
- **Routing**: React Router
- **State Management**: React Context API
- **Data Fetching**: TanStack Query (React Query)
- **Form Handling**: React Hook Form with Zod validation
- **Data Visualization**: Recharts
- **Styling**: Tailwind CSS
- **Icons**: Lucide React

## Project Structure

```
ats-dashboard-guru/
├── docs/                  # Documentation files
├── public/                # Static assets
├── scripts/               # Utility scripts for setup and maintenance
├── src/
│   ├── components/        # Reusable UI components
│   │   ├── candidates/    # Candidate-related components
│   │   ├── clients/       # Client-related components
│   │   ├── common/        # Common UI components
│   │   ├── dashboard/     # Dashboard components
│   │   ├── jobs/          # Job-related components
│   │   ├── kanban/        # Kanban board components
│   │   ├── layout/        # Layout components
│   │   ├── messages/      # Messaging components
│   │   ├── notifications/ # Notification components
│   │   ├── search/        # Search components
│   │   ├── settings/      # Settings components
│   │   ├── theme/         # Theme components
│   │   └── ui/            # ShadCN UI components
│   ├── contexts/          # React context providers
│   │   ├── AIContext.tsx  # AI assistance context
│   │   ├── NotificationContext.tsx # Notification management
│   │   ├── SearchContext.tsx # Global search functionality
│   │   ├── ThemeContext.tsx # Theme management
│   │   └── UserContext.tsx # User state management
│   ├── hooks/             # Custom React hooks
│   ├── lib/               # Utility libraries
│   │   ├── database/      # Database integration code
│   │   └── search/        # Search functionality
│   ├── pages/             # Page components
│   ├── server/            # Server-side code
│   ├── services/          # Service layer for API interactions
│   ├── themes/            # Theme CSS files
│   ├── types/             # TypeScript type definitions
│   ├── utils/             # Utility functions
│   ├── App.tsx            # Main App component
│   ├── index.css          # Global CSS
│   └── main.tsx           # Entry point
├── docker-compose.yml     # Docker configuration
├── Dockerfile             # Production Docker configuration
├── Dockerfile.frontend    # Frontend Docker configuration
├── Dockerfile.dev         # Development Docker configuration
├── version.json           # Version history and changelog
```

## Available Scripts

### Development
- `npm run dev`: Starts the development server
- `npm run dev:verbose`: Starts the development server in verbose mode
- `npm run dev:full`: Starts both the development server and the API server

### Building
- `npm run build`: Builds the app for production
- `npm run build:dev`: Builds the app for development
- `npm run preview`: Previews the production build locally

### Database Integration
- `npm run db:init`: Initializes PostgreSQL database with schema and sample data
- `npm run db:check`: Checks PostgreSQL tables and data
- `npm run db:migrate`: Runs database migrations

### Docker Deployment
- `./deploy-unified.sh start`: Start the application with Docker Desktop (development)
- `./deploy-unified.sh start --dev`: Start with development configuration
- `./deploy-unified.sh deploy`: Deploy for production
- `./deploy-unified.sh stop`: Stop all containers
- `./deploy-unified.sh restart`: Restart the application
- `./deploy-unified.sh status`: Show container status
- `./deploy-unified.sh logs`: Show container logs
- `./deploy-unified.sh clean`: Stop containers and remove volumes

### Other
- `npm run lint`: Runs ESLint to check for code issues
- `npm run server`: Starts the API server

## Features

- **Dashboard Overview**: Get a quick overview of your recruitment pipeline
- **Candidate Management**: Track and manage candidates through the hiring process
- **Job Management**: Create and manage job postings
- **Kanban Board**: Visualize your recruitment pipeline with a drag-and-drop interface
- **Calendar Integration**: Schedule and manage interviews and events
- **Messaging System**: Communicate with candidates and team members
- **Notification System**: Stay updated with real-time notifications
- **Theme Customization**: Choose from multiple themes including light, dark, and custom themes
- **User Settings**: Personalize your experience with accessibility options and preferences
- **Global Search**: Instantly search across all data with typo tolerance (Ctrl+K / Cmd+K)
- **AI-Powered Message Assistance**: Get AI-generated suggestions to enhance your messages
- **PostgreSQL Integration**: Store and manage data with PostgreSQL, a robust relational database
- **API Server**: RESTful API server for database operations

## Version History

### Version 1.3.0 (2025-04-30)
- UI Standardization: Created reusable PageHeader component for consistent layout
- UI Standardization: Removed duplicate UI elements and buttons across pages
- UI Standardization: Applied consistent styling to view toggle buttons
- UI Standardization: Integrated filter tabs into PageHeader component
- UI Improvements: Enhanced Candidates view mode toggle functionality
- Database Migration: Migrated to PostgreSQL database
- Database Migration: Implemented RESTful API server for database operations
- Database Migration: Created unified database client interface
- Project Cleanup: Removed obsolete test files and scripts
- Project Cleanup: Removed empty directories
- Project Cleanup: Updated documentation to reflect current state
- Docker Configuration: Improved Docker setup for production deployment

For detailed changelog, see [version.json](version.json).

### Version 1.2.0 (2025-04-11)
- Added theme customization with multiple themes (default, cosmic, modern, stargety, tangerine)
- Implemented dark/light mode toggle in navbar and settings
- Added theme preferences saved in localStorage
- Improved user settings with accessibility options
- Enhanced project documentation
- Code cleanup and optimization
- Fixed various UI bugs and improved responsiveness

### Version 1.1.0 (2023-07-10)
- Added comprehensive notification system
- Implemented notification dropdown in navbar
- Created dedicated notifications page with filtering and search
- Added bulk actions for notifications (mark as read, archive, delete)
- Implemented optimistic UI updates for notification actions

### Version 1.0.0 (2023-07-01)
- Initial release
- Basic ATS functionality
- Candidate and job management
- Calendar integration
- Messaging system

## Documentation

Detailed documentation is available in the `docs/` directory:

- [Project Overview](docs/project-overview.md): High-level overview of the project
- [Notification System](docs/notification-system.md): Documentation for the notification system
- [Theme System](docs/theme-system.md): Documentation for the theme system
- [Phase 2 Features](docs/phase2-features.md): Documentation for search and AI integration features
- [PostgreSQL Integration Guide](docs/postgresql-integration-guide.md): Guide for integrating with PostgreSQL
- [API Server Documentation](docs/api-server-documentation.md): Documentation for the API server

## How can I deploy this project?

### Option 1: Using Lovable

Simply open [Lovable](https://lovable.dev/projects/af4db429-4f27-4467-b746-c91dbcac93e4) and click on Share -> Publish.

### Option 2: Docker Deployment

For a complete deployment with PostgreSQL database and all services:

1. Use the unified deployment script: `./deploy-unified.sh deploy`
2. Access the application at http://localhost

### Option 3: Manual Deployment

You can deploy this project to any static site hosting service like Netlify, Vercel, or GitHub Pages:

1. Build the project: `npm run build`
2. Deploy the contents of the `dist/` directory to your hosting provider

## Using a Custom Domain

If you want to deploy your project under your own domain, we recommend using Netlify or Vercel. Visit our docs for more details: [Custom domains](https://docs.lovable.dev/tips-tricks/custom-domain/)

## Contributing

To contribute to this project:

1. Create a new branch for your feature: `git checkout -b feature-name`
2. Make your changes and commit them: `git commit -m "Add some feature"`
3. Push to the branch: `git push origin feature-name`
4. Submit a pull request
