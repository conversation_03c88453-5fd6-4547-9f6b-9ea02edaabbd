# ATS Dashboard - Environment Configuration Example
# Copy this file to .env for local development

# Application Environment
# Options: 'development' or 'production'
VITE_APP_ENV=development

# Feature Flags
# Enable/disable specific features
VITE_FEATURE_DEBUG_TOOLS=true

# PostgreSQL Configuration
# Required for database connection
VITE_POSTGRES_HOST=localhost
VITE_POSTGRES_PORT=5432
VITE_POSTGRES_DATABASE=postgres
VITE_POSTGRES_USER=postgres
VITE_POSTGRES_PASSWORD=postgres
VITE_POSTGRES_SSL=false

# Server Configuration
VITE_SERVER_PORT=3001

# Search Configuration (Typesense)
# Required for search functionality
VITE_TYPESENSE_HOST=localhost
VITE_TYPESENSE_PORT=8108
VITE_TYPESENSE_PROTOCOL=http
VITE_TYPESENSE_API_KEY=xyz123

# Docker Deployment Variables
# These are only used when running the application with Docker

# PostgreSQL Docker Configuration
POSTGRES_PASSWORD=postgres
POSTGRES_USER=postgres
POSTGRES_DB=postgres
