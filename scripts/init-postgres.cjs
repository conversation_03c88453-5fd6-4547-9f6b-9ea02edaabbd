/**
 * <PERSON><PERSON><PERSON> to initialize PostgreSQL database with sample data
 */
require('dotenv').config();
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// PostgreSQL connection
const pool = new Pool({
  host: process.env.VITE_POSTGRES_HOST || 'localhost',
  port: parseInt(process.env.VITE_POSTGRES_PORT || '5432'),
  database: process.env.VITE_POSTGRES_DATABASE || 'postgres',
  user: process.env.VITE_POSTGRES_USER || 'postgres',
  password: process.env.VITE_POSTGRES_PASSWORD || 'postgres',
  ssl: process.env.VITE_POSTGRES_SSL === 'true'
});

// Sample data
const sampleUsers = [
  {
    id: uuidv4(),
    email: '<EMAIL>',
    first_name: 'Admin',
    last_name: 'User',
    role: 'admin'
  },
  {
    id: uuidv4(),
    email: '<EMAIL>',
    first_name: 'Recruiter',
    last_name: 'User',
    role: 'recruiter'
  }
];

const sampleClients = [
  {
    id: uuidv4(),
    company_name: 'Acme Corporation',
    industry: 'Technology',
    website: 'https://acme.example.com',
    city: 'New York',
    country: 'USA',
    status: 'active'
  },
  {
    id: uuidv4(),
    company_name: 'Globex Industries',
    industry: 'Manufacturing',
    website: 'https://globex.example.com',
    city: 'Chicago',
    country: 'USA',
    status: 'active'
  }
];

const sampleJobs = [
  {
    id: uuidv4(),
    title: 'Senior Software Engineer',
    description: 'We are looking for a senior software engineer with experience in React and Node.js.',
    requirements: 'At least 5 years of experience in web development.',
    location: 'New York, NY',
    salary_min: 120000,
    salary_max: 150000,
    salary_currency: 'USD',
    employment_type: 'full-time',
    remote_type: 'hybrid',
    status: 'open',
    priority: 'high'
  },
  {
    id: uuidv4(),
    title: 'UX Designer',
    description: 'We are looking for a UX designer to join our product team.',
    requirements: 'Experience with Figma and user research.',
    location: 'Chicago, IL',
    salary_min: 90000,
    salary_max: 120000,
    salary_currency: 'USD',
    employment_type: 'full-time',
    remote_type: 'remote',
    status: 'open',
    priority: 'medium'
  }
];

const sampleCandidates = [
  {
    id: uuidv4(),
    first_name: 'John',
    last_name: 'Doe',
    email: '<EMAIL>',
    phone: '************',
    location: 'New York, NY',
    skills: ['JavaScript', 'React', 'Node.js'],
    experience_years: 7,
    current_company: 'Previous Corp',
    current_position: 'Senior Developer',
    status: 'active'
  },
  {
    id: uuidv4(),
    first_name: 'Jane',
    last_name: 'Smith',
    email: '<EMAIL>',
    phone: '************',
    location: 'San Francisco, CA',
    skills: ['UI/UX', 'Figma', 'Adobe XD'],
    experience_years: 5,
    current_company: 'Design Agency',
    current_position: 'UX Designer',
    status: 'active'
  }
];

// Helper function to insert data into a table
async function insertData(tableName, data, client) {
  if (!data || data.length === 0) {
    console.log(`No data to insert into ${tableName}`);
    return [];
  }
  
  // Get column names from first record
  const columns = Object.keys(data[0]);
  const insertedIds = [];
  
  for (const record of data) {
    const values = columns.map(col => record[col]);
    const placeholders = columns.map((_, i) => `$${i + 1}`).join(', ');
    
    const query = `
      INSERT INTO ${tableName} (${columns.join(', ')})
      VALUES (${placeholders})
      ON CONFLICT (id) DO NOTHING
      RETURNING id
    `;
    
    const result = await client.query(query, values);
    if (result.rows.length > 0) {
      insertedIds.push(result.rows[0].id);
    } else {
      insertedIds.push(record.id);
    }
  }
  
  console.log(`Successfully inserted ${insertedIds.length} records into ${tableName}`);
  return insertedIds;
}

// Main function to initialize database
async function initializeDatabase() {
  console.log('Initializing PostgreSQL database with sample data...');
  
  try {
    // Read and execute schema.sql
    const schemaPath = path.join(__dirname, '..', 'schema.sql');
    const schemaSql = fs.readFileSync(schemaPath, 'utf8');
    
    // Create a client for transaction
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Execute schema SQL
      await client.query(schemaSql);
      console.log('Schema created successfully');
      
      // Insert sample data
      const userIds = await insertData('users', sampleUsers, client);
      const clientIds = await insertData('clients', sampleClients, client);
      
      // Update jobs with client_id and created_by
      const updatedJobs = sampleJobs.map((job, index) => ({
        ...job,
        client_id: clientIds[index % clientIds.length],
        created_by: userIds[0] // Admin user
      }));
      
      const jobIds = await insertData('jobs', updatedJobs, client);
      const candidateIds = await insertData('candidates', sampleCandidates, client);
      
      // Create sample job applications
      const sampleJobApplications = [
        {
          id: uuidv4(),
          job_id: jobIds[0],
          candidate_id: candidateIds[0],
          status: 'in_review',
          stage: 'interview',
          applied_date: new Date().toISOString(),
          cover_letter: 'I am excited to apply for this position...',
          salary_expectation: 130000
        },
        {
          id: uuidv4(),
          job_id: jobIds[1],
          candidate_id: candidateIds[1],
          status: 'applied',
          stage: 'screening',
          applied_date: new Date().toISOString(),
          cover_letter: 'Please consider my application...',
          salary_expectation: 100000
        }
      ];
      
      const jobApplicationIds = await insertData('job_applications', sampleJobApplications, client);
      
      await client.query('COMMIT');
      console.log('Database initialization completed successfully!');
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Database initialization failed:', error);
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Database initialization failed:', error);
  } finally {
    // Close the pool
    await pool.end();
  }
}

// Run the initialization
initializeDatabase();
