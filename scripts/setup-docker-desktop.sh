#!/bin/bash

# <PERSON>ript to configure Dock<PERSON> to use Docker Desktop context
# This ensures containers are visible in Docker Desktop

echo "🐳 Configuring Docker to use Docker Desktop..."

# Check if Docker Desktop context exists
if docker context ls | grep -q "desktop-linux"; then
    echo "✅ Docker Desktop context found"
    
    # Switch to Docker Desktop context
    echo "🔄 Switching to Docker Desktop context..."
    docker context use desktop-linux
    
    # Unset DOCKER_HOST if it's set in current session
    if [ ! -z "$DOCKER_HOST" ]; then
        echo "⚠️  DOCKER_HOST is set to: $DOCKER_HOST"
        echo "🔧 Unsetting DOCKER_HOST for this session..."
        unset DOCKER_HOST
    fi
    
    # Verify the context is active
    echo "📋 Current Docker context:"
    docker context ls | grep "*"
    
    echo "✅ Docker Desktop context is now active!"
    echo "🎯 Your containers will now be visible in Docker Desktop"
    
else
    echo "❌ Docker Desktop context not found"
    echo "Please make sure Docker Desktop is installed and running"
    exit 1
fi

# Test Docker connection
echo "🧪 Testing Docker connection..."
if docker version > /dev/null 2>&1; then
    echo "✅ Docker is working correctly"
else
    echo "❌ Docker connection failed"
    exit 1
fi

echo "🎉 Setup complete! You can now run your containers and they will appear in Docker Desktop."
