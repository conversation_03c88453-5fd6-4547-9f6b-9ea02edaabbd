/**
 * Utility functions for validating data during CSV import
 */
import { isValidUrl } from '@/utils/urlUtils';
import {
  PrimaryStatus,
  SECONDARY_STATUS_OPTIONS,
  getAllSecondaryStatusOptions,
  getPrimaryStatusFromSecondary,
  IMPORT_SECONDARY_STATUS_MAPPING
} from '@/lib/constants/candidateStatus';

// Email validation
export const isValidEmail = (value: string): boolean => {
  if (!value) return false;
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(value);
};

// Number validation
export const isValidNumber = (value: any): boolean => {
  if (value === null || value === undefined || value === '') return true; // Empty values are allowed
  return !isNaN(Number(value));
};

// Date validation
export const isValidDate = (value: string): boolean => {
  if (!value) return true; // Empty dates are allowed
  const date = new Date(value);
  return !isNaN(date.getTime());
};

// Phone number validation (basic)
export const isValidPhone = (value: string): boolean => {
  if (!value) return true; // Empty phone numbers are allowed
  const phoneRegex = /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/;
  return phoneRegex.test(value.replace(/\s/g, ''));
};

// Field validators for different entity types
export const candidateValidators = {
  name: (value: string) => ({
    valid: !!value && value.length > 0,
    message: 'Full name is required'
  }),
  first_name: (value: string) => ({
    valid: !!value && value.length > 0,
    message: 'First name is required'
  }),
  last_name: (value: string) => ({
    valid: true, // Last name is now optional
    message: ''
  }),
  email: (value: string) => ({
    valid: isValidEmail(value),
    message: 'Invalid email format'
  }),
  phone: (value: string) => ({
    valid: isValidPhone(value),
    message: 'Invalid phone number format'
  }),
  resume_url: (value: string) => ({
    valid: isValidUrl(value),
    message: 'Invalid URL format'
  }),
  linkedin_url: (value: string) => ({
    valid: isValidUrl(value),
    message: 'Invalid URL format'
  }),
  github_url: (value: string) => ({
    valid: isValidUrl(value),
    message: 'Invalid URL format'
  }),
  portfolio_url: (value: string) => ({
    valid: isValidUrl(value),
    message: 'Invalid URL format'
  }),
  experience_years: (value: any) => ({
    valid: isValidNumber(value),
    message: 'Experience years must be a number'
  }),
  desired_salary: (value: any) => ({
    valid: isValidNumber(value),
    message: 'Desired salary must be a number'
  }),
  availability_date: (value: string) => ({
    valid: isValidDate(value),
    message: 'Invalid date format'
  }),
  status: (value: string) => {
    if (!value || value.trim() === '') return { valid: true, message: '' }; // Optional field, will default to 'new'

    // Normalize the status value first
    const normalizedValue = normalizeStatusValue(value);
    const validStatuses = ['new', 'screening', 'interview', 'challenge', 'client_feedback', 'offer', 'hired', 'rejected'];

    return {
      valid: validStatuses.includes(normalizedValue),
      message: `Invalid status value: "${value}". Valid options: New, Screening, Interviews, Challenge, Client Feedback, Offer, Hired, Rejected`
    };
  },
  secondary_status: (value: string) => {
    if (!value || value.trim() === '') return { valid: true, message: '' }; // Optional field

    // Get all valid secondary status values from our constants
    const allSecondaryStatuses = getAllSecondaryStatusOptions();
    const validSecondaryStatuses = allSecondaryStatuses.map(option => option.value);

    // Normalize the input value
    const normalizedValue = normalizeSecondaryStatusValue(value);

    // Check if the normalized value is in the valid list
    const isValid = validSecondaryStatuses.includes(normalizedValue);

    return {
      valid: isValid,
      message: `Invalid secondary status value: "${value}". Valid options: ${validSecondaryStatuses.join(', ')}`
    };
  },
  interview_score: (value: any) => {
    if (!value || value === '') return { valid: true, message: '' }; // Optional field
    const numValue = Number(value);
    return {
      valid: !isNaN(numValue) && numValue >= 0 && numValue <= 10,
      message: 'Interview score must be a number between 0 and 10'
    };
  },
  interview_notes: (value: string) => ({
    valid: true, // Always valid, just text
    message: ''
  }),
  english_level: (value: string) => {
    if (!value || value.trim() === '') return { valid: true, message: '' }; // Optional field
    const validLevels = ['beginner', 'intermediate', 'advance', 'advanced', 'unknow', 'unknown'];
    return {
      valid: validLevels.includes(value.toLowerCase()),
      message: 'English level must be one of: Beginner, Intermediate, Advanced, Unknown'
    };
  },
  is_duplicate: (value: string) => {
    if (!value || value.trim() === '') return { valid: true, message: '' }; // Optional field
    const validValues = ['new', 'duplicate', 'yes', 'no', 'true', 'false'];
    return {
      valid: validValues.includes(value.toLowerCase()),
      message: 'Duplicate status must be one of: new, duplicate, yes, no'
    };
  },
  candidate_id: (value: string) => ({
    valid: true, // Always valid, just text identifier
    message: ''
  }),
  stargety_id: (value: string) => ({
    valid: true, // Always valid, just text identifier
    message: ''
  }),
  challenge: (value: string) => ({
    valid: true, // Always valid, just text
    message: ''
  }),
  challenge_notes: (value: string) => ({
    valid: true, // Always valid, just text
    message: ''
  }),
  challenge_feedback: (value: string) => ({
    valid: true, // Always valid, just text
    message: ''
  }),
  drive_score: (value: any) => {
    if (!value || value === '') return { valid: true, message: '' }; // Optional field
    const numValue = Number(value);
    return {
      valid: !isNaN(numValue) && numValue >= 0 && numValue <= 10,
      message: 'Drive score must be a number between 0 and 10'
    };
  },
  resilience_score: (value: any) => {
    if (!value || value === '') return { valid: true, message: '' }; // Optional field
    const numValue = Number(value);
    return {
      valid: !isNaN(numValue) && numValue >= 0 && numValue <= 10,
      message: 'Resilience score must be a number between 0 and 10'
    };
  },
  collaboration_score: (value: any) => {
    if (!value || value === '') return { valid: true, message: '' }; // Optional field
    const numValue = Number(value);
    return {
      valid: !isNaN(numValue) && numValue >= 0 && numValue <= 10,
      message: 'Collaboration score must be a number between 0 and 10'
    };
  },
  result: (value: string) => ({
    valid: true, // Always valid, just text
    message: ''
  })
};

/**
 * Smart status mapping for CSV imports
 * Handles cases where only secondary status is provided or status mapping is needed
 * Enhanced to handle complex status scenarios from sample CSV data
 */
export const mapCandidateStatus = (data: any) => {
  let primaryStatus = data.status;
  let secondaryStatus = data.secondary_status;

  // Normalize status values (handle case variations and common misspellings)
  if (primaryStatus) {
    primaryStatus = normalizeStatusValue(primaryStatus);
  }
  if (secondaryStatus) {
    secondaryStatus = normalizeSecondaryStatusValue(secondaryStatus);
  }

  // If no primary status but secondary status exists, derive primary from secondary
  if (!primaryStatus && secondaryStatus) {
    primaryStatus = getPrimaryStatusFromSecondary(secondaryStatus);
  }

  // If primary status exists but no secondary status, set default secondary status
  if (primaryStatus && !secondaryStatus) {
    const secondaryOptions = SECONDARY_STATUS_OPTIONS[primaryStatus as any] || [];
    secondaryStatus = secondaryOptions.length > 0 ? secondaryOptions[0].value : '';
  }

  // Ensure primary status defaults to 'new' if still not set
  if (!primaryStatus) {
    primaryStatus = PrimaryStatus.NEW;
    const secondaryOptions = SECONDARY_STATUS_OPTIONS[PrimaryStatus.NEW] || [];
    secondaryStatus = secondaryOptions.length > 0 ? secondaryOptions[0].value : '';
  }

  return {
    ...data,
    status: primaryStatus,
    secondary_status: secondaryStatus
  };
};

/**
 * Normalize primary status values to handle variations in CSV data
 */
export const normalizeStatusValue = (status: string): string => {
  if (!status) return '';

  const normalized = status.toLowerCase().trim();

  // Handle exact CSV values and common variations
  const statusMappings: Record<string, string> = {
    'interviews': 'interview',
    'interview': 'interview',
    'screening': 'screening',
    'challenge': 'challenge',
    'client feedback': 'client_feedback',
    'client_feedback': 'client_feedback',
    'offer': 'offer',
    'hired': 'hired',
    'rejected': 'rejected',
    'new': 'new'
  };

  return statusMappings[normalized] || status.toLowerCase();
};

/**
 * Enhanced fuzzy matching for secondary status values
 * Uses import mapping first, then similarity scoring to handle variations in CSV data
 */
export const normalizeSecondaryStatusValue = (secondaryStatus: string): string => {
  if (!secondaryStatus) return '';

  const input = secondaryStatus.trim();

  // First try exact mapping from import table
  if (IMPORT_SECONDARY_STATUS_MAPPING[input]) {
    return IMPORT_SECONDARY_STATUS_MAPPING[input];
  }

  const allSecondaryStatuses = getAllSecondaryStatusOptions();

  // Then try exact match (case insensitive)
  const exactMatch = allSecondaryStatuses.find(option =>
    option.value.toLowerCase() === input.toLowerCase()
  );
  if (exactMatch) return exactMatch.value;

  // Try case-insensitive mapping from import table
  const lowerInput = input.toLowerCase();
  for (const [key, value] of Object.entries(IMPORT_SECONDARY_STATUS_MAPPING)) {
    if (key.toLowerCase() === lowerInput) {
      return value;
    }
  }

  // Finally try fuzzy matching with similarity scoring
  let bestMatch = { option: null as any, score: 0 };

  for (const option of allSecondaryStatuses) {
    const score = calculateStringSimilarity(input.toLowerCase(), option.value.toLowerCase());
    if (score > bestMatch.score && score > 0.7) { // 70% similarity threshold
      bestMatch = { option, score };
    }
  }

  return bestMatch.option ? bestMatch.option.value : input;
};

/**
 * Calculate string similarity using a combination of techniques
 */
const calculateStringSimilarity = (str1: string, str2: string): number => {
  // Exact match
  if (str1 === str2) return 1.0;

  // Contains match
  if (str1.includes(str2) || str2.includes(str1)) return 0.85;

  // Levenshtein distance based similarity
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;

  if (longer.length === 0) return 1.0;

  const distance = levenshteinDistance(longer, shorter);
  return (longer.length - distance) / longer.length;
};

/**
 * Calculate Levenshtein distance between two strings
 */
const levenshteinDistance = (str1: string, str2: string): number => {
  const matrix = [];

  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }

  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }

  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }

  return matrix[str2.length][str1.length];
};

/**
 * Transform and clean candidate data for import
 * Handles complex field mappings and data normalization
 */
export const transformCandidateData = (data: any) => {
  const transformed = { ...data };

  // Handle name fields - combine first and last name if full name is not provided
  if (!transformed.name && (transformed.first_name || transformed.last_name)) {
    const firstName = transformed.first_name || '';
    const lastName = transformed.last_name || '';
    transformed.name = `${firstName} ${lastName}`.trim();
  }

  // Split full name into first and last name if individual fields are not provided
  if (transformed.name && !transformed.first_name && !transformed.last_name) {
    const nameParts = transformed.name.trim().split(' ');
    transformed.first_name = nameParts[0] || '';
    transformed.last_name = nameParts.slice(1).join(' ') || '';
  }

  // Normalize email field (handle "E-mail" variations)
  if (transformed['E-mail'] && !transformed.email) {
    transformed.email = transformed['E-mail'];
  }

  // Handle duplicate status normalization
  if (transformed.is_duplicate) {
    const normalized = transformed.is_duplicate.toLowerCase().trim();
    if (['duplicate', 'yes', 'true'].includes(normalized)) {
      transformed.is_duplicate = 'duplicate';
    } else {
      transformed.is_duplicate = 'new';
    }
  }

  // Normalize English level
  if (transformed.english_level) {
    const normalized = transformed.english_level.toLowerCase().trim();
    const levelMappings: Record<string, string> = {
      'unknow': 'unknown',
      'advance': 'advanced',
      'beginner': 'beginner',
      'intermediate': 'intermediate',
      'advanced': 'advanced',
      'unknown': 'unknown'
    };
    transformed.english_level = levelMappings[normalized] || transformed.english_level;
  }

  // Handle numeric fields
  if (transformed.interview_score) {
    const score = parseFloat(transformed.interview_score);
    if (!isNaN(score)) {
      transformed.interview_score = Math.min(Math.max(score, 0), 10); // Clamp between 0-10
    }
  }

  // Clean up URL fields - add https:// if missing
  const urlFields = ['resume_url', 'linkedin_url', 'github_url', 'portfolio_url'];
  urlFields.forEach(field => {
    if (transformed[field] && typeof transformed[field] === 'string') {
      let url = transformed[field].trim();
      if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
        // Only add https:// if it looks like a URL
        if (url.includes('.') || url.startsWith('www.')) {
          transformed[field] = `https://${url}`;
        }
      }
    }
  });

  // Handle multi-line text fields (interview notes, challenge feedback)
  const textFields = ['interview_notes', 'challenge', 'challenge_feedback', 'notes'];
  textFields.forEach(field => {
    if (transformed[field] && typeof transformed[field] === 'string') {
      // Clean up multi-line text, preserve line breaks but remove excessive whitespace
      transformed[field] = transformed[field]
        .replace(/\r\n/g, '\n')
        .replace(/\r/g, '\n')
        .replace(/\n\s*\n/g, '\n\n')
        .trim();
    }
  });

  return transformed;
};

export const jobValidators = {
  title: (value: string) => ({
    valid: !!value && value.length > 0,
    message: 'Job title is required'
  }),
  client_id: (value: string) => ({
    valid: !!value && value.length > 0,
    message: 'Client ID is required'
  }),
  salary_min: (value: any) => ({
    valid: isValidNumber(value),
    message: 'Minimum salary must be a number'
  }),
  salary_max: (value: any) => ({
    valid: isValidNumber(value),
    message: 'Maximum salary must be a number'
  }),
  status: (value: string) => ({
    valid: ['open', 'closed', 'draft', 'archived'].includes(value.toLowerCase()),
    message: 'Invalid status value'
  }),
  published_at: (value: string) => ({
    valid: isValidDate(value),
    message: 'Invalid date format'
  }),
  closed_at: (value: string) => ({
    valid: isValidDate(value),
    message: 'Invalid date format'
  })
};

export const clientValidators = {
  company_name: (value: string) => ({
    valid: !!value && value.length > 0,
    message: 'Company name is required'
  }),
  website: (value: string) => ({
    valid: isValidUrl(value),
    message: 'Invalid URL format'
  }),
  logo_url: (value: string) => ({
    valid: isValidUrl(value),
    message: 'Invalid URL format'
  }),
  email: (value: string) => ({
    valid: !value || isValidEmail(value),
    message: 'Invalid email format'
  }),
  phone: (value: string) => ({
    valid: !value || isValidPhone(value),
    message: 'Invalid phone number format'
  }),
  status: (value: string) => ({
    valid: ['active', 'inactive', 'lead', 'former'].includes(value.toLowerCase()),
    message: 'Invalid status value'
  })
};

// Get validators by entity type
export const getValidatorsByEntityType = (entityType: string) => {
  switch (entityType) {
    case 'candidates':
      return candidateValidators;
    case 'jobs':
      return jobValidators;
    case 'clients':
      return clientValidators;
    default:
      return {};
  }
};

// Get field options by entity type
export const getFieldOptionsByEntityType = (entityType: string) => {
  switch (entityType) {
    case 'candidates':
      return [
        { value: 'name', label: 'Full Name' },
        { value: 'first_name', label: 'First Name' },
        { value: 'last_name', label: 'Last Name' },
        { value: 'email', label: 'Email' },
        { value: 'phone', label: 'Phone' },
        { value: 'location', label: 'Location' },
        { value: 'resume_url', label: 'Resume URL' },
        { value: 'linkedin_url', label: 'LinkedIn URL' },
        { value: 'github_url', label: 'GitHub URL' },
        { value: 'portfolio_url', label: 'Portfolio URL' },
        { value: 'skills', label: 'Skills' },
        { value: 'experience_years', label: 'Experience Years' },
        { value: 'education', label: 'Education' },
        { value: 'current_company', label: 'Current Company' },
        { value: 'current_position', label: 'Current Position' },
        { value: 'desired_salary', label: 'Desired Salary' },
        { value: 'salary_currency', label: 'Salary Currency' },
        { value: 'availability_date', label: 'Availability Date' },
        { value: 'status', label: 'Primary Status' },
        { value: 'secondary_status', label: 'Secondary Status' },
        { value: 'source', label: 'Source' },
        { value: 'notes', label: 'Notes' },
        { value: 'is_duplicate', label: 'Is Duplicate' },
        { value: 'english_level', label: 'English Level' },
        { value: 'interview_score', label: 'Interview Score' },
        { value: 'interview_notes', label: 'Interview Notes' },
        { value: 'challenge', label: 'Challenge' },
        { value: 'challenge_notes', label: 'Challenge Notes' },
        { value: 'challenge_feedback', label: 'Challenge Feedback' },
        { value: 'drive_score', label: 'Drive Score' },
        { value: 'resilience_score', label: 'Resilience Score' },
        { value: 'collaboration_score', label: 'Collaboration Score' },
        { value: 'result', label: 'Result' },
        { value: 'candidate_id', label: 'Candidate ID' },
        { value: 'stargety_id', label: 'Stargety ID' }
      ];
    case 'jobs':
      return [
        { value: 'client_id', label: 'Client ID' },
        { value: 'title', label: 'Title' },
        { value: 'description', label: 'Description' },
        { value: 'requirements', label: 'Requirements' },
        { value: 'location', label: 'Location' },
        { value: 'salary_min', label: 'Minimum Salary' },
        { value: 'salary_max', label: 'Maximum Salary' },
        { value: 'salary_currency', label: 'Salary Currency' },
        { value: 'employment_type', label: 'Employment Type' },
        { value: 'remote_type', label: 'Remote Type' },
        { value: 'status', label: 'Status' },
        { value: 'priority', label: 'Priority' }
      ];
    case 'clients':
      return [
        { value: 'company_name', label: 'Company Name' },
        { value: 'industry', label: 'Industry' },
        { value: 'website', label: 'Website' },
        { value: 'logo_url', label: 'Logo URL' },
        { value: 'address', label: 'Address' },
        { value: 'city', label: 'City' },
        { value: 'state', label: 'State' },
        { value: 'zip_code', label: 'ZIP Code' },
        { value: 'country', label: 'Country' },
        { value: 'phone', label: 'Phone' },
        { value: 'email', label: 'Email' },
        { value: 'status', label: 'Status' },
        { value: 'notes', label: 'Notes' }
      ];
    default:
      return [];
  }
};

// Get required fields by entity type
export const getRequiredFieldsByEntityType = (entityType: string) => {
  switch (entityType) {
    case 'candidates':
      return ['first_name', 'email'];
    case 'jobs':
      return ['title', 'client_id'];
    case 'clients':
      return ['company_name'];
    default:
      return [];
  }
};
