/**
 * Utility functions for handling names
 */

/**
 * Splits a full name into first name and last name
 * If the name has only one part, it will be used as the first name
 * and the last name will be set to the provided defaultLastName or empty string
 * 
 * @param fullName The full name to split
 * @param defaultLastName Default last name to use if none is found
 * @returns Object with firstName and lastName properties
 */
export function splitFullName(fullName: string, defaultLastName: string = ''): { firstName: string; lastName: string } {
  if (!fullName || typeof fullName !== 'string') {
    return { firstName: '', lastName: defaultLastName };
  }

  const nameParts = fullName.trim().split(/\s+/);
  
  if (nameParts.length === 0) {
    return { firstName: '', lastName: defaultLastName };
  }
  
  if (nameParts.length === 1) {
    return { firstName: nameParts[0], lastName: defaultLastName };
  }
  
  const firstName = nameParts[0];
  const lastName = nameParts.slice(1).join(' ');
  
  return { firstName, lastName };
}

/**
 * Joins first name and last name into a full name
 * 
 * @param firstName The first name
 * @param lastName The last name
 * @returns The full name
 */
export function joinNames(firstName: string, lastName: string): string {
  return `${firstName || ''} ${lastName || ''}`.trim();
}
