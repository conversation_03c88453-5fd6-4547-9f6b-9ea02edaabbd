import { candidatesApi } from '@/services/apiService';
import { transformCandidateData, mapCandidateStatus } from '@/utils/importValidators';

export interface BatchImportOptions {
  batchSize?: number;
  delayBetweenBatches?: number;
  onProgress?: (progress: number, processed: number, total: number) => void;
  onBatchComplete?: (batchIndex: number, results: BatchResult[]) => void;
  onError?: (error: Error, rowIndex: number, data: any) => void;
}

export interface BatchResult {
  success: boolean;
  rowIndex: number;
  data: any;
  error?: string;
  candidateId?: string;
}

export interface ImportSummary {
  total: number;
  successful: number;
  failed: number;
  errors: Array<{
    row: number;
    message: string;
    data: any;
  }>;
  duration: number;
}

/**
 * Batch importer for CSV candidate data
 * Processes data in chunks to avoid overwhelming the API and provide progress feedback
 */
export class BatchImporter {
  private options: Required<BatchImportOptions>;

  constructor(options: BatchImportOptions = {}) {
    this.options = {
      batchSize: options.batchSize || 10,
      delayBetweenBatches: options.delayBetweenBatches || 100,
      onProgress: options.onProgress || (() => {}),
      onBatchComplete: options.onBatchComplete || (() => {}),
      onError: options.onError || (() => {})
    };
  }

  /**
   * Import candidate data in batches
   */
  async importCandidates(
    data: Record<string, string>[],
    columnMappings: Record<string, string>
  ): Promise<ImportSummary> {
    const startTime = Date.now();
    const results: BatchResult[] = [];
    const errors: Array<{ row: number; message: string; data: any }> = [];

    try {
      // Process data in batches
      for (let i = 0; i < data.length; i += this.options.batchSize) {
        const batch = data.slice(i, i + this.options.batchSize);
        const batchIndex = Math.floor(i / this.options.batchSize);

        const batchResults = await this.processBatch(batch, columnMappings, i);
        results.push(...batchResults);

        // Collect errors
        batchResults.forEach(result => {
          if (!result.success && result.error) {
            errors.push({
              row: result.rowIndex + 1,
              message: result.error,
              data: result.data
            });
          }
        });

        // Update progress
        const processed = Math.min(i + this.options.batchSize, data.length);
        const progress = (processed / data.length) * 100;
        this.options.onProgress(progress, processed, data.length);

        // Notify batch completion
        this.options.onBatchComplete(batchIndex, batchResults);

        // Delay between batches to avoid rate limiting
        if (i + this.options.batchSize < data.length) {
          await this.delay(this.options.delayBetweenBatches);
        }
      }
    } catch (error) {
      console.error('Batch import failed:', error);
      throw error;
    }

    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    const duration = Date.now() - startTime;

    return {
      total: data.length,
      successful,
      failed,
      errors,
      duration
    };
  }

  /**
   * Process a single batch of candidates
   */
  private async processBatch(
    batch: Record<string, string>[],
    columnMappings: Record<string, string>,
    startIndex: number
  ): Promise<BatchResult[]> {
    const promises = batch.map(async (row, index) => {
      const rowIndex = startIndex + index;

      try {
        // Transform row data using column mappings
        const mappedData: any = {};
        Object.entries(row).forEach(([csvColumn, value]) => {
          const targetField = columnMappings[csvColumn];
          if (targetField && value !== undefined) {
            mappedData[targetField] = value;
          }
        });

        // Apply status mapping and data transformation
        const statusMapped = mapCandidateStatus(mappedData);
        const transformed = transformCandidateData(statusMapped);

        // Validate required fields
        if (!transformed.first_name) {
          throw new Error('First name is required');
        }

        // Validate email format if provided
        if (transformed.email && transformed.email.trim() && !isValidEmail(transformed.email)) {
          throw new Error('Invalid email format');
        }

        // Create candidate via API
        const candidate = await candidatesApi.create(transformed);

        return {
          success: true,
          rowIndex,
          data: transformed,
          candidateId: candidate.id
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        this.options.onError(error as Error, rowIndex, row);

        return {
          success: false,
          rowIndex,
          data: row,
          error: errorMessage
        };
      }
    });

    return Promise.all(promises);
  }

  /**
   * Validate data before import
   */
  static validateImportData(
    data: Record<string, string>[],
    columnMappings: Record<string, string>
  ): {
    isValid: boolean;
    errors: Array<{ row: number; field: string; message: string }>;
    warnings: Array<{ row: number; field: string; message: string }>;
  } {
    const errors: Array<{ row: number; field: string; message: string }> = [];
    const warnings: Array<{ row: number; field: string; message: string }> = [];

    // Check if required mappings exist
    const hasFirstName = Object.values(columnMappings).includes('first_name');

    if (!hasFirstName) {
      errors.push({ row: 0, field: 'first_name', message: 'First name column mapping is required' });
    }

    // Validate each row
    data.forEach((row, index) => {
      const mappedData: any = {};
      Object.entries(row).forEach(([csvColumn, value]) => {
        const targetField = columnMappings[csvColumn];
        if (targetField) {
          mappedData[targetField] = value;
        }
      });

      // Apply transformations
      const statusMapped = mapCandidateStatus(mappedData);
      const transformed = transformCandidateData(statusMapped);

      // Check required fields
      if (!transformed.first_name || transformed.first_name.trim() === '') {
        errors.push({
          row: index + 1,
          field: 'first_name',
          message: 'First name is required and cannot be empty'
        });
      }

      // Validate email format if provided
      if (transformed.email && transformed.email.trim() !== '' && !isValidEmail(transformed.email)) {
        errors.push({
          row: index + 1,
          field: 'email',
          message: 'Invalid email format'
        });
      }

      // Check for potential duplicates (same email)
      const duplicateIndex = data.findIndex((otherRow, otherIndex) => {
        if (otherIndex >= index) return false;
        const otherMappedData: any = {};
        Object.entries(otherRow).forEach(([csvColumn, value]) => {
          const targetField = columnMappings[csvColumn];
          if (targetField) {
            otherMappedData[targetField] = value;
          }
        });
        const otherTransformed = transformCandidateData(mapCandidateStatus(otherMappedData));
        return otherTransformed.email === transformed.email;
      });

      if (duplicateIndex !== -1) {
        warnings.push({
          row: index + 1,
          field: 'email',
          message: `Duplicate email found in row ${duplicateIndex + 1}`
        });
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Utility method to add delay between batches
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Email validation - more flexible to handle various formats
 */
function isValidEmail(email: string): boolean {
  if (!email || typeof email !== 'string') return false;

  // Clean the email - remove ALL types of whitespace and control characters
  const cleanEmail = email
    .replace(/[\r\n\t\f\v]/g, '') // Remove all whitespace control characters
    .trim(); // Remove leading/trailing spaces

  // Skip validation if email is empty after cleaning
  if (!cleanEmail) return true; // Empty emails are allowed

  // Check for obviously invalid patterns first
  if (cleanEmail.includes(' ')) return false; // No spaces allowed in emails
  if (cleanEmail.split('@').length !== 2) return false; // Must have exactly one @

  // More flexible email regex that handles various real-world formats
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

  // Additional checks for common edge cases
  if (cleanEmail.includes('..')) return false; // No consecutive dots
  if (cleanEmail.startsWith('.') || cleanEmail.endsWith('.')) return false; // No leading/trailing dots
  if (cleanEmail.includes('@.') || cleanEmail.includes('.@')) return false; // No dots adjacent to @

  return emailRegex.test(cleanEmail);
}

/**
 * Export convenience function for simple imports
 */
export async function importCandidatesFromCSV(
  data: Record<string, string>[],
  columnMappings: Record<string, string>,
  options?: BatchImportOptions
): Promise<ImportSummary> {
  const importer = new BatchImporter(options);
  return importer.importCandidates(data, columnMappings);
}
