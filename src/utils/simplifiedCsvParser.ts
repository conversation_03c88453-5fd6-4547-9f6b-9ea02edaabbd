import Papa from 'papaparse';

export interface ParsedCSVData {
  data: Record<string, string>[];
  headers: string[];
  errors: string[];
  meta: {
    totalRows: number;
    delimiter: string;
    encoding: string;
  };
}

/**
 * Simplified CSV parser with intelligent column detection and data cleaning
 */
export const parseCSVFile = async (file: File): Promise<ParsedCSVData> => {
  return new Promise((resolve, reject) => {
    const errors: string[] = [];

    // Detect delimiter by sampling the file
    const detectDelimiter = (sample: string): string => {
      const delimiters = [',', ';', '\t', '|'];
      let bestDelimiter = ',';
      let maxColumns = 0;

      delimiters.forEach(delimiter => {
        const lines = sample.split('\n').slice(0, 5); // Check first 5 lines
        const columnCounts = lines.map(line => line.split(delimiter).length);
        const avgColumns = columnCounts.reduce((a, b) => a + b, 0) / columnCounts.length;

        if (avgColumns > maxColumns) {
          maxColumns = avgColumns;
          bestDelimiter = delimiter;
        }
      });

      return bestDelimiter;
    };

    // Read a sample of the file to detect delimiter
    const reader = new FileReader();
    reader.onload = (e) => {
      const sample = (e.target?.result as string)?.substring(0, 1000) || '';
      const delimiter = detectDelimiter(sample);

      // Parse the full file with detected delimiter
      Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        delimiter: delimiter,
        transformHeader: (header: string) => {
          // Clean and normalize header names
          return header.trim().replace(/\s+/g, ' ');
        },
        transform: (value: string) => {
          // Clean and normalize cell values - remove control characters and trim
          return value ? value.replace(/[\r\n\t\f\v]/g, '').trim() : value;
        },
        complete: (results) => {
          try {
            const data = results.data as Record<string, string>[];
            const headers = Object.keys(data[0] || {});

            // Validate data structure
            if (headers.length === 0) {
              errors.push('No valid columns found in CSV file');
            }

            if (data.length === 0) {
              errors.push('No data rows found in CSV file');
            }

            // Check for common issues
            const emptyHeaders = headers.filter(h => !h || h.trim() === '');
            if (emptyHeaders.length > 0) {
              errors.push(`Found ${emptyHeaders.length} empty column header(s)`);
            }

            // Remove rows that are completely empty
            const cleanedData = data.filter(row => {
              return Object.values(row).some(value => value && value.trim() !== '');
            });

            if (cleanedData.length < data.length) {
              const removedRows = data.length - cleanedData.length;
              console.log(`Removed ${removedRows} empty rows`);
            }

            // Add parsing errors from Papa Parse
            if (results.errors && results.errors.length > 0) {
              results.errors.forEach(error => {
                errors.push(`Row ${error.row}: ${error.message}`);
              });
            }

            const parsedData: ParsedCSVData = {
              data: cleanedData,
              headers,
              errors,
              meta: {
                totalRows: cleanedData.length,
                delimiter,
                encoding: 'UTF-8'
              }
            };

            resolve(parsedData);
          } catch (error) {
            reject(new Error(`Failed to process CSV data: ${(error as Error).message}`));
          }
        },
        error: (error) => {
          reject(new Error(`Failed to parse CSV file: ${error.message}`));
        }
      });
    };

    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };

    reader.readAsText(file);
  });
};

/**
 * Validate CSV data structure for candidate import
 */
export const validateCSVStructure = (data: ParsedCSVData): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check minimum requirements
  if (data.data.length === 0) {
    errors.push('CSV file contains no data rows');
  }

  if (data.headers.length === 0) {
    errors.push('CSV file contains no column headers');
  }

  // Check for potential candidate data columns
  const commonCandidateColumns = [
    'name', 'first name', 'last name', 'email', 'phone', 'status'
  ];

  const hasCommonColumns = commonCandidateColumns.some(col =>
    data.headers.some(header =>
      header.toLowerCase().includes(col.toLowerCase())
    )
  );

  if (!hasCommonColumns) {
    warnings.push('No common candidate columns detected. Please verify this is candidate data.');
  }

  // Check data consistency
  if (data.data.length > 0) {
    const firstRowKeys = Object.keys(data.data[0]);
    const inconsistentRows = data.data.filter(row =>
      Object.keys(row).length !== firstRowKeys.length
    );

    if (inconsistentRows.length > 0) {
      warnings.push(`${inconsistentRows.length} rows have inconsistent column counts`);
    }
  }

  // Check for large files
  if (data.data.length > 1000) {
    warnings.push(`Large file detected (${data.data.length} rows). Import may take some time.`);
  }

  return {
    isValid: errors.length === 0,
    errors: [...data.errors, ...errors],
    warnings
  };
};

/**
 * Generate column mapping suggestions based on header names
 */
export const generateColumnSuggestions = (headers: string[]): Array<{
  csvColumn: string;
  suggestedField: string;
  confidence: number;
}> => {
  const mappingRules: Array<{
    patterns: string[];
    field: string;
    confidence: number;
  }> = [
    { patterns: ['first name', 'firstname', 'fname'], field: 'first_name', confidence: 0.9 },
    { patterns: ['last name', 'lastname', 'lname', 'surname'], field: 'last_name', confidence: 0.9 },
    { patterns: ['full name', 'name', 'candidate name'], field: 'name', confidence: 0.8 },
    { patterns: ['email', 'e-mail', 'email address'], field: 'email', confidence: 0.95 },
    { patterns: ['phone', 'telephone', 'mobile', 'contact'], field: 'phone', confidence: 0.8 },
    { patterns: ['location', 'city', 'address'], field: 'location', confidence: 0.7 },
    { patterns: ['portfolio', 'portfolio url', 'website'], field: 'portfolio_url', confidence: 0.8 },
    { patterns: ['linkedin', 'linkedin url'], field: 'linkedin_url', confidence: 0.9 },
    { patterns: ['github', 'github url'], field: 'github_url', confidence: 0.9 },
    { patterns: ['resume', 'resume url', 'cv'], field: 'resume_url', confidence: 0.8 },
    { patterns: ['status', 'primary status'], field: 'status', confidence: 0.9 },
    { patterns: ['secondary status', 'sub status'], field: 'secondary_status', confidence: 0.9 },
    { patterns: ['english level', 'english'], field: 'english_level', confidence: 0.8 },
    { patterns: ['interview score', 'score'], field: 'interview_score', confidence: 0.8 },
    { patterns: ['interview notes', 'notes'], field: 'interview_notes', confidence: 0.7 },
    { patterns: ['challenge'], field: 'challenge', confidence: 0.8 },
    { patterns: ['Stargety ID', 'id'], field: 'stargety_id', confidence: 0.7 },
    { patterns: ['duplicate', 'new or duplicate'], field: 'is_duplicate', confidence: 0.8 }
  ];

  return headers.map(header => {
    const normalizedHeader = header.toLowerCase().trim();

    let bestMatch = { field: '', confidence: 0 };

    mappingRules.forEach(rule => {
      rule.patterns.forEach(pattern => {
        const similarity = calculateStringSimilarity(normalizedHeader, pattern);
        const confidence = similarity * rule.confidence;

        if (confidence > bestMatch.confidence) {
          bestMatch = { field: rule.field, confidence };
        }
      });
    });

    return {
      csvColumn: header,
      suggestedField: bestMatch.confidence > 0.5 ? bestMatch.field : '',
      confidence: bestMatch.confidence
    };
  });
};

/**
 * Calculate string similarity using Levenshtein distance
 */
const calculateStringSimilarity = (str1: string, str2: string): number => {
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;

  if (longer.length === 0) return 1.0;

  // Check for exact match or contains
  if (str1 === str2) return 1.0;
  if (str1.includes(str2) || str2.includes(str1)) return 0.8;

  const distance = levenshteinDistance(longer, shorter);
  return (longer.length - distance) / longer.length;
};

/**
 * Calculate Levenshtein distance between two strings
 */
const levenshteinDistance = (str1: string, str2: string): number => {
  const matrix = [];

  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }

  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }

  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }

  return matrix[str2.length][str1.length];
};
