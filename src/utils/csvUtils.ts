
import { Candidate } from "@/types/candidate";
import <PERSON> from "papaparse";

export interface Lead {
  id: string;
  name: string;
  contact: string;
  position: string;
  status: string;
  date: string;
  image?: string;
}

// Parse CSV/TSV file and return array of objects
export const parseCSV = <T>(file: File, delimiter: string = ','): Promise<T[]> => {
  return new Promise((resolve, reject) => {
    if (import.meta.env.DEV) {
      console.log('🔧 CSV Parser: Starting Papa Parse...');
      console.log('🔧 CSV Parser: Using delimiter:', delimiter);
    }

    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      delimiter: delimiter,
      transform: (value: string, field: string | number) => {
        // Clean and normalize cell values - remove control characters and trim
        const originalValue = value;
        const cleanedValue = value ? value.replace(/[\r\n\t\f\v]/g, '').trim() : value;

        if (originalValue !== cleanedValue && import.meta.env.DEV) {
          console.log(`🔧 CSV Parser: Cleaned field "${field}": "${originalValue}" -> "${cleanedValue}"`);
        }

        return cleanedValue;
      },
      complete: (results) => {
        console.log('🔧 CSV Parser: Parse complete');
        console.log('🔧 CSV Parser: Results meta:', results.meta);
        console.log('🔧 CSV Parser: Data rows:', results.data?.length || 0);
        console.log('🔧 CSV Parser: Errors:', results.errors?.length || 0);

        if (results.errors && results.errors.length > 0) {
          console.log('🔧 CSV Parser: Parse errors:', results.errors);
          reject(new Error(results.errors[0].message));
        } else {
          console.log('🔧 CSV Parser: Parse successful');
          resolve(results.data as T[]);
        }
      },
      error: (error) => {
        console.log('🔧 CSV Parser: Parse failed:', error);
        reject(error);
      },
    });
  });
};

// Convert array of objects to CSV and trigger download
export const exportToCSV = <T>(data: T[], filename: string): void => {
  const csv = Papa.unparse(data);
  const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.setAttribute("href", url);
  link.setAttribute("download", filename);
  link.style.visibility = "hidden";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// Map CSV data to candidate structure
export const mapCSVToCandidates = (csvData: any[]): Partial<Candidate>[] => {
  return csvData.map((row) => ({
    id: row.id || crypto.randomUUID(),
    name: row.name || "",
    email: row.email || "",
    phone: row.phone || "",
    position: row.position || "",
    status: row.status as any || "new",
    appliedDate: row.appliedDate || new Date().toLocaleDateString(),
    inTalentPool: row.inTalentPool === "true" || false,
    communications: []
  }));
};

// Map CSV data to lead structure
export const mapCSVToLeads = (csvData: any[]): Lead[] => {
  return csvData.map((row) => ({
    id: row.id || `l${crypto.randomUUID()}`,
    name: row.name || "",
    contact: row.contact || "",
    position: row.position || "",
    status: row.status || "new",
    date: row.date || new Date().toLocaleDateString(),
    image: row.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(row.name || "Company")}&background=0D8ABC&color=fff`
  }));
};
