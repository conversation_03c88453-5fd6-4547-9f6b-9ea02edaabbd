/**
 * Utility functions for handling URLs
 */

/**
 * Validates a URL with flexible rules
 * - Accepts URLs with or without protocol prefixes (http://, https://)
 * - Accepts URLs starting with www.
 * - Accepts simple domain formats (domain.com, domain.org, etc.)
 * 
 * @param value The URL to validate
 * @returns True if the URL is valid, false otherwise
 */
export function isValidUrl(value: string): boolean {
  if (!value) return true; // Empty URLs are allowed
  
  // If the URL already has a protocol, use the URL constructor
  if (value.startsWith('http://') || value.startsWith('https://')) {
    try {
      new URL(value);
      return true;
    } catch {
      return false;
    }
  }
  
  // If the URL starts with www., add https:// and validate
  if (value.startsWith('www.')) {
    try {
      new URL(`https://${value}`);
      return true;
    } catch {
      return false;
    }
  }
  
  // For simple domain formats, add https:// and validate
  try {
    new URL(`https://${value}`);
    return true;
  } catch {
    return false;
  }
}

/**
 * Normalizes a URL by adding https:// if needed
 * 
 * @param url The URL to normalize
 * @returns The normalized URL
 */
export function normalizeUrl(url: string): string {
  if (!url) return '';
  
  // If the URL already has a protocol, return it as is
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }
  
  // Otherwise, add https:// prefix
  return `https://${url}`;
}

/**
 * Creates a safe external link with target="_blank" and rel attributes
 * 
 * @param url The URL to link to
 * @returns An object with href, target, and rel attributes
 */
export function createExternalLinkProps(url: string): { href: string; target: string; rel: string } {
  return {
    href: normalizeUrl(url),
    target: '_blank',
    rel: 'noopener noreferrer'
  };
}
