/**
 * Utility functions for generating IDs for different entities
 */

/**
 * Generates a candidate ID based on name and phone number
 * Format: initials-lastFourDigits
 * 
 * @param firstName First name of the candidate
 * @param lastName Last name of the candidate
 * @param phone Phone number of the candidate
 * @returns Generated ID
 */
export function generateCandidateId(firstName: string, lastName: string, phone: string): string {
  // Extract initials from name
  const firstInitial = firstName ? firstName.charAt(0).toUpperCase() : '';
  const lastInitial = lastName ? lastName.charAt(0).toUpperCase() : '';
  const initials = `${firstInitial}${lastInitial}`;
  
  // Extract last 4 digits from phone number
  const cleanPhone = phone.replace(/\D/g, ''); // Remove non-digit characters
  const lastFourDigits = cleanPhone.length >= 4 
    ? cleanPhone.substring(cleanPhone.length - 4) 
    : cleanPhone.padStart(4, '0');
  
  // Combine to form ID
  return `${initials}-${lastFourDigits}`;
}

/**
 * Fallback function to generate a unique ID when required fields are missing
 * 
 * @param prefix Prefix for the ID
 * @returns Generated ID
 */
export function generateFallbackId(prefix: string = 'cand'): string {
  const timestamp = Date.now().toString(36);
  const randomStr = Math.random().toString(36).substring(2, 5);
  return `${prefix}-${timestamp}${randomStr}`;
}
