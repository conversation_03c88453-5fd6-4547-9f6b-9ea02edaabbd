/**
 * PostgreSQL database connection utility with retry mechanism
 */
const { Pool } = require('pg');
require('dotenv').config();

// Log environment variables
console.log('Database connection parameters:');
console.log('VITE_POSTGRES_HOST:', process.env.VITE_POSTGRES_HOST || 'localhost');
console.log('VITE_POSTGRES_PORT:', process.env.VITE_POSTGRES_PORT || '5432');
console.log('VITE_POSTGRES_DATABASE:', process.env.VITE_POSTGRES_DATABASE || 'postgres');
console.log('VITE_POSTGRES_USER:', process.env.VITE_POSTGRES_USER || 'postgres');
console.log('VITE_POSTGRES_SSL:', process.env.VITE_POSTGRES_SSL || 'false');

// Create PostgreSQL connection pool
const pool = new Pool({
  host: process.env.VITE_POSTGRES_HOST || 'localhost',
  port: parseInt(process.env.VITE_POSTGRES_PORT || '5432'),
  database: process.env.VITE_POSTGRES_DATABASE || 'postgres',
  user: process.env.VITE_POSTGRES_USER || 'postgres',
  password: process.env.VITE_POSTGRES_PASSWORD || 'postgres',
  ssl: process.env.VITE_POSTGRES_SSL === 'true',
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 5000 // Increased from 2000 to 5000
});

// Connection status
let connectionStatus = {
  isConnected: false,
  lastError: null,
  lastChecked: null,
  retryCount: 0
};

/**
 * Exponential backoff retry function
 * @param {Function} fn - Function to retry
 * @param {number} maxRetries - Maximum number of retries
 * @param {number} baseDelay - Base delay in milliseconds
 * @returns {Promise<any>} - Result of the function
 */
async function withRetry(fn, maxRetries = 5, baseDelay = 1000) {
  let retries = 0;

  while (true) {
    try {
      return await fn();
    } catch (error) {
      retries++;
      connectionStatus.retryCount = retries;
      connectionStatus.lastError = error;

      if (retries >= maxRetries) {
        console.error(`Max retries (${maxRetries}) reached. Last error:`, error);
        throw error;
      }

      // Calculate delay with exponential backoff and jitter
      const delay = baseDelay * Math.pow(2, retries) + Math.random() * 1000;
      // Log retry attempts in development only
      if (process.env.NODE_ENV === 'development') {
        console.log(`Retry ${retries}/${maxRetries} after ${Math.round(delay)}ms`);
      }

      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}

/**
 * Check database connection
 * @returns {Promise<boolean>} - True if connected, false otherwise
 */
async function checkConnection() {
  try {
    const client = await pool.connect();
    try {
      await client.query('SELECT 1');
      connectionStatus.isConnected = true;
      connectionStatus.lastError = null;
      connectionStatus.lastChecked = new Date();
      connectionStatus.retryCount = 0;
      return true;
    } finally {
      client.release();
    }
  } catch (error) {
    connectionStatus.isConnected = false;
    connectionStatus.lastError = error;
    connectionStatus.lastChecked = new Date();
    console.error('Database connection check failed:', error);
    return false;
  }
}

/**
 * Initialize database connection with retry
 * @returns {Promise<boolean>} - True if connected, false otherwise
 */
async function initializeConnection() {
  try {
    return await withRetry(async () => {
      const connected = await checkConnection();
      if (!connected) {
        throw new Error('Database connection failed');
      }
      console.log('Successfully connected to PostgreSQL database');
      return true;
    });
  } catch (error) {
    console.error('Failed to connect to database after multiple retries:', error);
    return false;
  }
}

/**
 * Execute a query with retry
 * @param {string} text - SQL query text
 * @param {Array} params - Query parameters
 * @returns {Promise<any>} - Query result
 */
async function query(text, params) {
  return withRetry(async () => {
    const start = Date.now();
    try {
      const res = await pool.query(text, params);
      const duration = Date.now() - start;
      // Log queries in development only
      if (process.env.NODE_ENV === 'development') {
        console.log('Executed query', { text, duration, rows: res.rowCount });
      }
      return res;
    } catch (error) {
      console.error('Error executing query', { text, error });
      throw error;
    }
  });
}

/**
 * Execute a transaction
 * @param {Function} callback - Transaction callback
 * @returns {Promise<any>} - Transaction result
 */
async function withTransaction(callback) {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Get database connection status
 * @returns {Object} - Connection status
 */
function getConnectionStatus() {
  return {
    ...connectionStatus,
    poolStatus: {
      totalCount: pool.totalCount,
      idleCount: pool.idleCount,
      waitingCount: pool.waitingCount
    }
  };
}

// Initialize connection when module is loaded
initializeConnection().catch(console.error);

// Export functions
module.exports = {
  pool,
  query,
  withTransaction,
  checkConnection,
  initializeConnection,
  getConnectionStatus
};
