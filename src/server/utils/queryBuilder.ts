export class QueryBuilder {
  private table: string;
  private conditions: string[] = [];
  private params: any[] = [];
  private orderByClause: string = '';
  private limitValue: number | null = null;
  private offsetValue: number | null = null;
  
  constructor(table: string) {
    this.table = table;
  }
  
  where(field: string, operator: string, value: any): QueryBuilder {
    this.conditions.push(`${field} ${operator} $${this.params.length + 1}`);
    this.params.push(value);
    return this;
  }
  
  orderBy(field: string, direction: 'ASC' | 'DESC' = 'ASC'): QueryBuilder {
    this.orderByClause = `ORDER BY ${field} ${direction}`;
    return this;
  }
  
  limit(limit: number): QueryBuilder {
    this.limitValue = limit;
    return this;
  }
  
  offset(offset: number): QueryBuilder {
    this.offsetValue = offset;
    return this;
  }
  
  buildSelect(fields: string[] = ['*']): { text: string; params: any[] } {
    const whereClause = this.conditions.length > 0 
      ? `WHERE ${this.conditions.join(' AND ')}` 
      : '';
      
    const limitClause = this.limitValue !== null 
      ? `LIMIT ${this.limitValue}` 
      : '';
      
    const offsetClause = this.offsetValue !== null 
      ? `OFFSET ${this.offsetValue}` 
      : '';
    
    const text = `
      SELECT ${fields.join(', ')} 
      FROM ${this.table} 
      ${whereClause} 
      ${this.orderByClause} 
      ${limitClause} 
      ${offsetClause}
    `.trim().replace(/\s+/g, ' ');
    
    return { text, params: this.params };
  }
  
  buildInsert(data: Record<string, any>): { text: string; params: any[] } {
    const fields = Object.keys(data);
    const values = Object.values(data);
    const placeholders = fields.map((_, i) => `$${i + 1}`).join(', ');
    
    const text = `
      INSERT INTO ${this.table} (${fields.join(', ')})
      VALUES (${placeholders})
      RETURNING *
    `.trim().replace(/\s+/g, ' ');
    
    return { text, params: values };
  }
  
  buildUpdate(data: Record<string, any>): { text: string; params: any[] } {
    const fields = Object.keys(data);
    const values = Object.values(data);
    
    // Create SET clause
    const setClauses = fields.map((field, i) => `${field} = $${i + 1}`);
    const setClause = setClauses.join(', ');
    
    // Create WHERE clause
    const whereClause = this.conditions.length > 0 
      ? `WHERE ${this.conditions.join(' AND ')}` 
      : '';
    
    const text = `
      UPDATE ${this.table}
      SET ${setClause}
      ${whereClause}
      RETURNING *
    `.trim().replace(/\s+/g, ' ');
    
    return { text, params: [...values, ...this.params] };
  }
  
  buildDelete(): { text: string; params: any[] } {
    const whereClause = this.conditions.length > 0 
      ? `WHERE ${this.conditions.join(' AND ')}` 
      : '';
    
    const text = `
      DELETE FROM ${this.table}
      ${whereClause}
      RETURNING *
    `.trim().replace(/\s+/g, ' ');
    
    return { text, params: this.params };
  }
}
