/**
 * QueryBuilder class for building SQL queries
 */

class QueryBuilder {
  constructor(table) {
    this.table = table;
    this.conditions = [];
    this.params = [];
    this.orderByClause = '';
    this.limitValue = null;
    this.offsetValue = null;
  }

  where(field, operator, value) {
    this.conditions.push(`${field} ${operator} $${this.params.length + 1}`);
    this.params.push(value);
    return this;
  }

  orderBy(field, direction = 'ASC') {
    this.orderByClause = `ORDER BY ${field} ${direction}`;
    return this;
  }

  limit(limit) {
    this.limitValue = limit;
    return this;
  }

  offset(offset) {
    this.offsetValue = offset;
    return this;
  }

  buildSelect(fields = ['*']) {
    const whereClause = this.conditions.length > 0
      ? `WHERE ${this.conditions.join(' AND ')}`
      : '';

    const limitClause = this.limitValue !== null
      ? `LIMIT ${this.limitValue}`
      : '';

    const offsetClause = this.offsetValue !== null
      ? `OFFSET ${this.offsetValue}`
      : '';

    const text = `
      SELECT ${fields.join(', ')}
      FROM ${this.table}
      ${whereClause}
      ${this.orderByClause}
      ${limitClause}
      ${offsetClause}
    `.trim().replace(/\s+/g, ' ');

    return { text, params: this.params };
  }

  buildInsert(data) {
    const fields = Object.keys(data);
    const values = Object.values(data);
    const placeholders = fields.map((_, i) => `$${i + 1}`).join(', ');

    const text = `
      INSERT INTO ${this.table} (${fields.join(', ')})
      VALUES (${placeholders})
      RETURNING *
    `.trim().replace(/\s+/g, ' ');

    return { text, params: values };
  }

  buildUpdate(data) {
    const fields = Object.keys(data);
    const values = Object.values(data);

    // Create SET clause with proper type handling
    const setClauses = fields.map((field, i) => {
      // Handle timestamp fields
      if (field === 'updated_at' || field === 'created_at') {
        if (typeof values[i] === 'string' || values[i] instanceof Date) {
          return `${field} = $${i + 1}::timestamp with time zone`;
        }
      }
      return `${field} = $${i + 1}`;
    });

    const setClause = setClauses.join(', ');

    // Create WHERE clause with correct parameter numbering
    const whereConditions = this.conditions.map((condition, i) => {
      // Replace the parameter placeholder with the correct number
      return condition.replace(`$${i + 1}`, `$${values.length + i + 1}`);
    });

    const whereClause = whereConditions.length > 0
      ? `WHERE ${whereConditions.join(' AND ')}`
      : '';

    const text = `
      UPDATE ${this.table}
      SET ${setClause}
      ${whereClause}
      RETURNING *
    `.trim().replace(/\s+/g, ' ');

    // Log SQL generation in development only
    if (process.env.NODE_ENV === 'development') {
      console.log('Generated SQL:', text);
      console.log('SET Parameters:', JSON.stringify(values));
      console.log('WHERE Parameters:', JSON.stringify(this.params));
      console.log('All Parameters:', JSON.stringify([...values, ...this.params]));
    }

    return { text, params: [...values, ...this.params] };
  }

  buildDelete() {
    const whereClause = this.conditions.length > 0
      ? `WHERE ${this.conditions.join(' AND ')}`
      : '';

    const text = `
      DELETE FROM ${this.table}
      ${whereClause}
      RETURNING *
    `.trim().replace(/\s+/g, ' ');

    return { text, params: this.params };
  }
}

module.exports = QueryBuilder;
