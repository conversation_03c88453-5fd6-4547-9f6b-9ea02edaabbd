-- Migration: Add client_notes table
-- Description: Creates a new table to store timestamped notes for clients with proper user attribution

-- Create client_notes table
CREATE TABLE IF NOT EXISTS client_notes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_client_notes_client_id ON client_notes(client_id);
CREATE INDEX IF NOT EXISTS idx_client_notes_user_id ON client_notes(user_id);
CREATE INDEX IF NOT EXISTS idx_client_notes_created_at ON client_notes(created_at);

-- Add comments for documentation
COMMENT ON TABLE client_notes IS 'Stores timestamped notes for clients with user attribution';
COMMENT ON COLUMN client_notes.id IS 'Unique identifier for the note';
COMMENT ON COLUMN client_notes.client_id IS 'Reference to the client this note belongs to';
COMMENT ON COLUMN client_notes.user_id IS 'Reference to the user who created this note';
COMMENT ON COLUMN client_notes.content IS 'The actual note content';
COMMENT ON COLUMN client_notes.created_at IS 'When the note was created';
COMMENT ON COLUMN client_notes.updated_at IS 'When the note was last updated';
