-- Migration: Add candidate_notes table
-- Description: Creates a new table to store timestamped notes for candidates with proper user attribution

-- Create candidate_notes table
CREATE TABLE IF NOT EXISTS candidate_notes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  candidate_id UUID NOT NULL REFERENCES candidates(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_candidate_notes_candidate_id ON candidate_notes(candidate_id);
CREATE INDEX IF NOT EXISTS idx_candidate_notes_created_at ON candidate_notes(created_at);
CREATE INDEX IF NOT EXISTS idx_candidate_notes_user_id ON candidate_notes(user_id);

-- Add comment to table for documentation
COMMENT ON TABLE candidate_notes IS 'Stores timestamped notes for candidates with user attribution';
COMMENT ON COLUMN candidate_notes.candidate_id IS 'Foreign key reference to candidates table';
COMMENT ON COLUMN candidate_notes.user_id IS 'Foreign key reference to users table - who created the note';
COMMENT ON COLUMN candidate_notes.content IS 'The actual note content';
COMMENT ON COLUMN candidate_notes.created_at IS 'When the note was created';
COMMENT ON COLUMN candidate_notes.updated_at IS 'When the note was last updated';
