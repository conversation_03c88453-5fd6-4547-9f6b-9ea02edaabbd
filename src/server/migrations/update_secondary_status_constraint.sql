-- Migration to update secondary status constraint to include all import values
-- This allows all values from mappings.csv to be stored in the database

-- Drop the existing constraint
ALTER TABLE candidates DROP CONSTRAINT IF EXISTS candidates_secondary_status_check;

-- Add the new constraint with all import values
ALTER TABLE candidates ADD CONSTRAINT candidates_secondary_status_check
CHECK (secondary_status IS NULL OR secondary_status IN (
  -- Values from mappings.csv import table
  'Pending',
  'Message sent',
  'Initial message sent',
  'No WhatsApp - contact by email',
  'Form completed',
  'Form Finalized',
  'Appointment to be scheduled',
  'Appointment scheduled',
  'Scheduled appointment',
  'Interview finalized',
  'Interview completed',
  'Did not respond to form',
  'No show',
  'Challenge accepted',
  'Challenge to be sent',
  'Challenge sent',
  'Waiting for challenge',
  'Waiting for challenge / time expired',
  'Challenge received',
  'Presentation assigned',
  'Presentation Finalized',
  'Resume Sent to Client',
  'RTR to be send',
  'R<PERSON> sent',
  '<PERSON><PERSON> received',
  'Feedback and <PERSON><PERSON> sent',
  'Candidate ready',
  'Rejected by incubator',
  'Rejected incubator',
  'Accepted by incubator',
  'Accepted incubator',
  'Accepted by Customer',
  'Invitation to the incubator',
  'Hired',
  'Rejected / no rating',
  'I rejected / does not qualify',
  'Did not respond',
  'No response',
  'with reservations'
));
