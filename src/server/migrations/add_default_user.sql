-- Migration: Add default user
-- Description: Creates a default user for the candidate notes system

-- Insert default user if it doesn't exist
INSERT INTO users (id, email, first_name, last_name, role, avatar_url)
VALUES (
  '00000000-0000-0000-0000-000000000001',
  '<EMAIL>',
  '<PERSON>',
  '<PERSON><PERSON>',
  'Recruiting Manager',
  'https://randomuser.me/api/portraits/women/42.jpg'
)
ON CONFLICT (email) DO NOTHING;

-- Add comment
COMMENT ON TABLE users IS 'System users who can create notes and perform actions';
