-- Migration to enforce one-to-many relationship between clients and jobs
-- This migration ensures that every job must have a valid client association

-- Step 1: Assign existing jobs without client_id to available clients
-- This is a temporary solution for existing data - in production, this should be done manually
-- based on business logic and actual client relationships

DO $$
DECLARE
    job_record RECORD;
    client_ids UUID[];
    client_count INTEGER;
    random_client_id UUID;
BEGIN
    -- Get all available client IDs
    SELECT ARRAY(SELECT id FROM clients ORDER BY created_at) INTO client_ids;
    client_count := array_length(client_ids, 1);
    
    -- Only proceed if we have clients available
    IF client_count > 0 THEN
        -- Update jobs that don't have a client_id assigned
        FOR job_record IN 
            SELECT id, title FROM jobs WHERE client_id IS NULL
        LOOP
            -- Assign a random client (for demo purposes)
            -- In production, this should be done based on actual business relationships
            random_client_id := client_ids[1 + (random() * (client_count - 1))::INTEGER];
            
            UPDATE jobs 
            SET client_id = random_client_id, 
                updated_at = CURRENT_TIMESTAMP
            WHERE id = job_record.id;
            
            RAISE NOTICE 'Assigned job "%" to client %', job_record.title, random_client_id;
        END LOOP;
    ELSE
        RAISE EXCEPTION 'No clients found in database. Cannot assign jobs to clients.';
    END IF;
END $$;

-- Step 2: Verify that all jobs now have client associations
DO $$
DECLARE
    orphaned_jobs_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO orphaned_jobs_count FROM jobs WHERE client_id IS NULL;
    
    IF orphaned_jobs_count > 0 THEN
        RAISE EXCEPTION 'Found % jobs without client associations. Cannot proceed with NOT NULL constraint.', orphaned_jobs_count;
    END IF;
    
    RAISE NOTICE 'All jobs now have valid client associations.';
END $$;

-- Step 3: Make client_id NOT NULL to enforce the relationship
ALTER TABLE jobs ALTER COLUMN client_id SET NOT NULL;

-- Step 4: Ensure the foreign key constraint exists and is properly configured
-- The constraint should already exist from the original schema, but let's verify it
DO $$
BEGIN
    -- Check if the foreign key constraint exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'jobs_client_id_fkey' 
        AND table_name = 'jobs'
    ) THEN
        -- Add the foreign key constraint if it doesn't exist
        ALTER TABLE jobs 
        ADD CONSTRAINT jobs_client_id_fkey 
        FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Added foreign key constraint jobs_client_id_fkey';
    ELSE
        RAISE NOTICE 'Foreign key constraint jobs_client_id_fkey already exists';
    END IF;
END $$;

-- Step 5: Create an index on client_id if it doesn't exist for better performance
CREATE INDEX IF NOT EXISTS idx_jobs_client_id ON jobs(client_id);

-- Step 6: Add a comment to document the relationship
COMMENT ON COLUMN jobs.client_id IS 'Required foreign key reference to clients table. Every job must be associated with a client.';

-- Verification query to show the results
SELECT 
    'Migration completed successfully' as status,
    COUNT(*) as total_jobs,
    COUNT(client_id) as jobs_with_clients,
    COUNT(*) - COUNT(client_id) as jobs_without_clients
FROM jobs;
