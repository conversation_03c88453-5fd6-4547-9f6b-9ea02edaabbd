-- Migration to update existing secondary status values to match the official mapping
-- This script converts Spanish status values to English ones based on csv/mapping of statuses.csv

-- Update New status secondary values
UPDATE candidates SET secondary_status = 
  CASE 
    WHEN secondary_status = '1 Pendiente' OR secondary_status = 'Pendiente' THEN 'Pending'
    WHEN secondary_status = '1.5 se envía un mensaje' OR secondary_status = 'se envía un mensaje' THEN 'Message sent'
    WHEN secondary_status = 'no tiene WhatsApp contactar por correo' OR secondary_status = 'No tiene WhatsApp' THEN 'No WhatsApp - contact'
    ELSE secondary_status
  END
WHERE status = 'new';

-- Update Screening status secondary values
UPDATE candidates SET secondary_status = 
  CASE 
    WHEN secondary_status = '2 se envió formulario' OR secondary_status = 'se envió formulario' THEN 'Form completed'
    WHEN secondary_status = '3 formulario lleno' OR secondary_status = 'formulario lleno' THEN 'Form completed'
    WHEN secondary_status = '4 cita agendada' OR secondary_status = 'cita agendada' THEN 'Appointment scheduled'
    WHEN secondary_status = '5 entrevista finalizada' OR secondary_status = 'entrevista finalizada' THEN 'Interview finalized'
    WHEN secondary_status = 'no respondió formulario' THEN 'Did not respond to form'
    WHEN secondary_status = 'lo rechazó no califica' OR secondary_status = 'Rechazado - No califica' THEN 'Did not respond to form'
    ELSE secondary_status
  END
WHERE status = 'screening';

-- Update Interview status secondary values
UPDATE candidates SET secondary_status = 
  CASE 
    WHEN secondary_status = '4 cita agendada' OR secondary_status = 'cita agendada' THEN 'Appointment scheduled'
    WHEN secondary_status = '5 entrevista finalizada' OR secondary_status = 'entrevista finalizada' THEN 'Interview finalized'
    WHEN secondary_status = 'no show' THEN 'No show'
    ELSE secondary_status
  END
WHERE status = 'interview';

-- Update Challenge status secondary values
UPDATE candidates SET secondary_status = 
  CASE 
    WHEN secondary_status = '6 enviar reto' OR secondary_status = 'enviar reto' THEN 'Challenge to be sent'
    WHEN secondary_status = '7 reto enviado' OR secondary_status = 'reto enviado' THEN 'Challenge sent'
    WHEN secondary_status = '7.5 esperando reto' OR secondary_status = 'esperando reto' THEN 'Waiting for challenge'
    WHEN secondary_status = '8 reto aceptado' OR secondary_status = 'reto aceptado' THEN 'Challenge accepted'
    WHEN secondary_status = '9 reto entregado' OR secondary_status = 'reto entregado' THEN 'Challenge received'
    WHEN secondary_status = '10 presentación agendada' OR secondary_status = 'presentación agendada' THEN 'Presentation assigned'
    WHEN secondary_status = '11 presentación terminada' OR secondary_status = 'presentación terminada' THEN 'Presentation Finalized'
    ELSE secondary_status
  END
WHERE status = 'challenge';

-- Update Client Interview status secondary values
UPDATE candidates SET secondary_status = 
  CASE 
    WHEN secondary_status = '10 presentación agendada' OR secondary_status = 'presentación agendada' THEN 'Appointment scheduled'
    WHEN secondary_status = '11 presentación terminada' OR secondary_status = 'presentación terminada' THEN 'Interview finalized'
    WHEN secondary_status = 'no show' THEN 'No show'
    ELSE 'Resume Sent to Client'
  END
WHERE status = 'client_interview';

-- Update Client Feedback status secondary values
UPDATE candidates SET secondary_status = 
  CASE 
    WHEN secondary_status = '12 feedback IRTR enviado' OR secondary_status = 'feedback IRTR enviado' THEN 'RTR to be send'
    WHEN secondary_status = '13 RTR recibido' OR secondary_status = 'RTR recibido' THEN 'RTR received'
    WHEN secondary_status = 'feedback y rtr enviado' THEN 'RTR sent'
    WHEN secondary_status = 'rtr recibido' THEN 'RTR received'
    ELSE secondary_status
  END
WHERE status = 'client_feedback';

-- Update Offer status secondary values
UPDATE candidates SET secondary_status = 
  CASE 
    WHEN secondary_status = '14 candidato listo' OR secondary_status = 'candidato listo' THEN 'Candidate ready'
    WHEN secondary_status = 'rechazó la incubadora' THEN 'Rejected by incubator'
    WHEN secondary_status = 'aceptó incubadora' THEN 'Accepted by incubator'
    ELSE secondary_status
  END
WHERE status = 'offer';

-- Update Hired status secondary values
UPDATE candidates SET secondary_status = 
  CASE 
    WHEN secondary_status = 'contratado' THEN 'Hired'
    ELSE 'Hired'
  END
WHERE status = 'hired';

-- Update Rejected status secondary values
UPDATE candidates SET secondary_status = 
  CASE 
    WHEN secondary_status = 'no respondió' THEN 'Did not respond'
    WHEN secondary_status = 'rechazó la oferta' THEN 'Rejected / no rating'
    WHEN secondary_status = 'no califica' THEN 'Rejected / no rating'
    WHEN secondary_status = 'no show' THEN 'No show'
    ELSE 'Rejected / no rating'
  END
WHERE status = 'rejected';

-- Set default secondary status for any records that still have NULL
UPDATE candidates SET secondary_status = 
  CASE 
    WHEN status = 'new' THEN 'Pending'
    WHEN status = 'screening' THEN 'Form completed'
    WHEN status = 'interview' THEN 'Appointment to be scheduled'
    WHEN status = 'challenge' THEN 'Challenge to be sent'
    WHEN status = 'client_interview' THEN 'Resume Sent to Client'
    WHEN status = 'client_feedback' THEN 'RTR to be send'
    WHEN status = 'offer' THEN 'Candidate ready'
    WHEN status = 'hired' THEN 'Hired'
    WHEN status = 'rejected' THEN 'Rejected / no rating'
    ELSE 'Pending'
  END
WHERE secondary_status IS NULL OR secondary_status = '';
