-- Migration to add cover_letter_url field to candidates table
-- This field was missing from the original schema but is used by the frontend

-- Add the cover_letter_url column
ALTER TABLE candidates 
ADD COLUMN IF NOT EXISTS cover_letter_url TEXT;

-- Add a comment to document the field
COMMENT ON COLUMN candidates.cover_letter_url IS 'URL to the candidate cover letter document';

-- Update the API routes documentation
-- Note: This field should be included in the field mappings in candidates.ts
