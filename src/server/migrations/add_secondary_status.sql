-- Migration to add secondary_status field to candidates table
ALTER TABLE candidates ADD COLUMN IF NOT EXISTS secondary_status VARCHAR(100);

-- Update existing records to set a default secondary_status based on status
-- Using the official mapping from csv/mapping of statuses.csv
UPDATE candidates SET secondary_status =
  CASE
    WHEN status = 'new' THEN 'Pending'
    WHEN status = 'screening' THEN 'Form completed'
    WHEN status = 'interview' THEN 'Appointment to be scheduled'
    WHEN status = 'challenge' THEN 'Challenge to be sent'
    WHEN status = 'client_interview' THEN 'Resume Sent to Client'
    WHEN status = 'client_feedback' THEN 'RTR to be send'
    WHEN status = 'offer' THEN 'Candidate ready'
    WHEN status = 'hired' THEN 'Hired'
    WHEN status = 'rejected' THEN 'Rejected / no rating'
    ELSE NULL
  END
WHERE secondary_status IS NULL;
