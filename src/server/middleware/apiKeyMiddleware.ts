/**
 * Middleware para validar la API key en solicitudes a la API
 */

import { Request, Response, NextFunction } from 'express';
import apiService from '../../services/apiService';

/**
 * Middleware para validar la API key
 * @param req Solicitud
 * @param res Respuesta
 * @param next Siguiente middleware
 */
export const validateApiKey = (req: Request, res: Response, next: NextFunction) => {
  try {
    // Obtener API key del encabezado o del cuerpo de la solicitud
    const apiKey = req.headers['x-api-key'] as string || req.body.apiKey;
    
    // Validar API key
    if (!apiService.validateApiKey(apiKey)) {
      return res.status(401).json({
        success: false,
        message: 'Invalid API key',
      });
    }
    
    // Continuar con el siguiente middleware
    next();
  } catch (error) {
    console.error('Error validating API key:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
};

export default validateApiKey;
