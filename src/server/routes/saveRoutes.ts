/**
 * <PERSON><PERSON><PERSON> para la API de guardado
 */

import express from 'express';
import saveController from '../controllers/saveController';
import validateApi<PERSON>ey from '../middleware/apiKeyMiddleware';

const router = express.Router();

// Ruta para guardar datos
router.post('/save', validate<PERSON><PERSON><PERSON><PERSON>, saveController.saveData);

// Ruta para guardar múltiples datos
router.post('/save-multiple', validate<PERSON><PERSON><PERSON><PERSON>, saveController.saveMultipleData);

export default router;
