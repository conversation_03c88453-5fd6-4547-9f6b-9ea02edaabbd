/**
 * API routes for candidate notes
 */

import express from 'express';
import { query } from '../../utils/db-connection.cjs';
import QueryBuilder from '../../utils/queryBuilder.cjs';

const router = express.Router();

// Get all notes for a specific candidate
router.get('/candidate/:candidateId', async (req, res) => {
  try {
    const { candidateId } = req.params;

    // Query to get notes with user information
    const queryText = `
      SELECT 
        cn.id,
        cn.candidate_id,
        cn.user_id,
        cn.content,
        cn.created_at,
        cn.updated_at,
        u.first_name || ' ' || COALESCE(u.last_name, '') as user_name,
        u.email as user_email,
        u.avatar_url as user_avatar
      FROM candidate_notes cn
      LEFT JOIN users u ON cn.user_id = u.id
      WHERE cn.candidate_id = $1
      ORDER BY cn.created_at DESC
    `;

    const result = await query(queryText, [candidateId]);
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching candidate notes:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Create a new note for a candidate
router.post('/', async (req, res) => {
  try {
    const { candidate_id, content, user_id } = req.body;

    // Validate required fields
    if (!candidate_id || !content || !user_id) {
      return res.status(400).json({ 
        error: 'Missing required fields: candidate_id, content, and user_id are required' 
      });
    }

    // Verify candidate exists
    const candidateCheck = await query(
      'SELECT id FROM candidates WHERE id = $1',
      [candidate_id]
    );

    if (candidateCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Candidate not found' });
    }

    // Verify user exists
    const userCheck = await query(
      'SELECT id FROM users WHERE id = $1',
      [user_id]
    );

    if (userCheck.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Create the note
    const queryBuilder = new QueryBuilder('candidate_notes');
    const { text, params } = queryBuilder.buildInsert({
      candidate_id,
      user_id,
      content,
      created_at: new Date(),
      updated_at: new Date()
    });

    const result = await query(text, params);
    
    // Return the created note with user information
    const noteWithUser = await query(`
      SELECT 
        cn.id,
        cn.candidate_id,
        cn.user_id,
        cn.content,
        cn.created_at,
        cn.updated_at,
        u.first_name || ' ' || COALESCE(u.last_name, '') as user_name,
        u.email as user_email,
        u.avatar_url as user_avatar
      FROM candidate_notes cn
      LEFT JOIN users u ON cn.user_id = u.id
      WHERE cn.id = $1
    `, [result.rows[0].id]);

    res.status(201).json(noteWithUser.rows[0]);
  } catch (error) {
    console.error('Error creating candidate note:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Update a note (only content can be updated)
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { content, user_id } = req.body;

    if (!content) {
      return res.status(400).json({ error: 'Content is required' });
    }

    // Check if note exists and user has permission to edit
    const noteCheck = await query(
      'SELECT user_id FROM candidate_notes WHERE id = $1',
      [id]
    );

    if (noteCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Note not found' });
    }

    // Only allow the original author to edit the note
    if (noteCheck.rows[0].user_id !== user_id) {
      return res.status(403).json({ error: 'You can only edit your own notes' });
    }

    // Update the note
    const queryBuilder = new QueryBuilder('candidate_notes');
    queryBuilder.where('id', '=', id);

    const { text, params } = queryBuilder.buildUpdate({
      content,
      updated_at: new Date()
    });

    await query(text, params);

    // Return the updated note with user information
    const updatedNote = await query(`
      SELECT 
        cn.id,
        cn.candidate_id,
        cn.user_id,
        cn.content,
        cn.created_at,
        cn.updated_at,
        u.first_name || ' ' || COALESCE(u.last_name, '') as user_name,
        u.email as user_email,
        u.avatar_url as user_avatar
      FROM candidate_notes cn
      LEFT JOIN users u ON cn.user_id = u.id
      WHERE cn.id = $1
    `, [id]);

    res.json(updatedNote.rows[0]);
  } catch (error) {
    console.error('Error updating candidate note:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Delete a note
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { user_id } = req.body;

    // Check if note exists and user has permission to delete
    const noteCheck = await query(
      'SELECT user_id FROM candidate_notes WHERE id = $1',
      [id]
    );

    if (noteCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Note not found' });
    }

    // Only allow the original author to delete the note
    if (noteCheck.rows[0].user_id !== user_id) {
      return res.status(403).json({ error: 'You can only delete your own notes' });
    }

    // Delete the note
    await query('DELETE FROM candidate_notes WHERE id = $1', [id]);

    res.json({ message: 'Note deleted successfully' });
  } catch (error) {
    console.error('Error deleting candidate note:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

export default router;
