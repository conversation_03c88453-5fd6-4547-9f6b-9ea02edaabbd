import * as express from 'express';
import { query, withTransaction } from '../../services/postgresService';

const router = express.Router();

/**
 * Bulk delete candidates
 * POST /api/bulk/delete/candidates
 * 
 * Request body: { ids: string[] }
 * Response: { success: boolean, deletedCount: number }
 */
router.post('/delete/candidates', async (req, res) => {
  try {
    const { ids } = req.body;

    // Validate request
    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ 
        success: false, 
        error: 'Invalid request. Expected an array of candidate IDs.' 
      });
    }

    // Use transaction to ensure atomicity
    let deletedCount = 0;
    await withTransaction(async (client) => {
      // First delete related records (job applications, etc.)
      const jobApplicationsResult = await client.query(
        'DELETE FROM job_applications WHERE candidate_id = ANY($1) RETURNING id',
        [ids]
      );

      // Delete communications
      await client.query(
        'DELETE FROM communications WHERE candidate_id = ANY($1)',
        [ids]
      );

      // Then delete the candidates
      const result = await client.query(
        'DELETE FROM candidates WHERE id = ANY($1) RETURNING id',
        [ids]
      );

      deletedCount = result.rowCount;
    });

    res.json({ 
      success: true, 
      deletedCount,
      message: `Successfully deleted ${deletedCount} candidate(s).`
    });
  } catch (error) {
    console.error('Error performing bulk delete operation:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Database error during bulk delete operation.' 
    });
  }
});

export default router;
