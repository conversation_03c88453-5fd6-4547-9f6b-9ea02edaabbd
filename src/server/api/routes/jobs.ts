import * as express from 'express';
import { query, withTransaction } from '../../services/postgresService';
import { QueryBuilder } from '../../utils/queryBuilder';
import { validateJob } from '../validators/jobValidator';

const router = express.Router();

// Get all jobs
router.get('/', async (req, res) => {
  try {
    const { limit, offset, sort, status, client_id } = req.query;

    // Build query using QueryBuilder
    const queryBuilder = new QueryBuilder('jobs');

    // Add filters if provided
    if (status) {
      queryBuilder.where('status', '=', status);
    }

    if (client_id) {
      queryBuilder.where('client_id', '=', client_id);
    }

    // Add sorting
    if (sort === 'title_asc') {
      queryBuilder.orderBy('title', 'ASC');
    } else if (sort === 'title_desc') {
      queryBuilder.orderBy('title', 'DESC');
    } else if (sort === 'priority_high') {
      queryBuilder.orderBy('priority', 'ASC'); // Assuming 'high' is first alphabetically
    } else {
      queryBuilder.orderBy('created_at', 'DESC');
    }

    // Add pagination
    if (limit) {
      queryBuilder.limit(parseInt(limit as string));
    }

    if (offset) {
      queryBuilder.offset(parseInt(offset as string));
    }

    // Execute query
    const { text, params } = queryBuilder.buildSelect();
    const result = await query(text, params);

    // Get client information for each job
    const jobsWithClients = await Promise.all(
      result.rows.map(async (job) => {
        const clientResult = await query(
          'SELECT id, company_name FROM clients WHERE id = $1',
          [job.client_id]
        );

        return {
          ...job,
          client: clientResult.rows[0] || null
        };
      })
    );

    res.json(jobsWithClients);
  } catch (error) {
    console.error('Error fetching jobs:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Get job by ID
router.get('/:id', async (req, res) => {
  try {
    const result = await query(
      'SELECT * FROM jobs WHERE id = $1',
      [req.params.id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Job not found' });
    }

    // Get client information
    const clientResult = await query(
      'SELECT id, company_name FROM clients WHERE id = $1',
      [result.rows[0].client_id]
    );

    const job = {
      ...result.rows[0],
      client: clientResult.rows[0] || null
    };

    res.json(job);
  } catch (error) {
    console.error('Error fetching job:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Create new job
router.post('/', async (req, res) => {
  try {
    // Validate request body
    const validation = validateJob(req.body);
    if (!validation.success) {
      return res.status(400).json({ errors: validation.errors });
    }

    const {
      client_id, title, description, requirements, location,
      salary_min, salary_max, salary_currency, employment_type,
      remote_type, status, priority, created_by
    } = req.body;

    // Check if client exists
    const clientResult = await query(
      'SELECT id FROM clients WHERE id = $1',
      [client_id]
    );

    if (clientResult.rows.length === 0) {
      return res.status(404).json({ error: 'Client not found' });
    }

    // Create new job
    const queryBuilder = new QueryBuilder('jobs');
    const { text, params } = queryBuilder.buildInsert({
      client_id,
      title,
      description,
      requirements,
      location,
      salary_min,
      salary_max,
      salary_currency: salary_currency || 'USD',
      employment_type,
      remote_type,
      status: status || 'open',
      priority: priority || 'medium',
      created_by,
      created_at: new Date(),
      updated_at: new Date()
    });

    const result = await query(text, params);
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating job:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Update job
router.put('/:id', async (req, res) => {
  try {
    // Validate request body
    const validation = validateJob(req.body, true);
    if (!validation.success) {
      return res.status(400).json({ errors: validation.errors });
    }

    // Check if job exists
    const existingResult = await query(
      'SELECT id FROM jobs WHERE id = $1',
      [req.params.id]
    );

    if (existingResult.rows.length === 0) {
      return res.status(404).json({ error: 'Job not found' });
    }

    // If client_id is provided, check if client exists
    if (req.body.client_id) {
      const clientResult = await query(
        'SELECT id FROM clients WHERE id = $1',
        [req.body.client_id]
      );

      if (clientResult.rows.length === 0) {
        return res.status(404).json({ error: 'Client not found' });
      }
    }

    // Update job
    const queryBuilder = new QueryBuilder('jobs');
    queryBuilder.where('id', '=', req.params.id);

    const updateData: Record<string, any> = {
      updated_at: new Date()
    };

    // Only include fields that are provided
    const fields = [
      'client_id', 'title', 'description', 'requirements', 'location',
      'salary_min', 'salary_max', 'salary_currency', 'employment_type',
      'remote_type', 'status', 'priority', 'published_at', 'closed_at'
    ];

    fields.forEach(field => {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });

    const { text, params } = queryBuilder.buildUpdate(updateData);
    const result = await query(text, params);

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating job:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Delete job
router.delete('/:id', async (req, res) => {
  try {
    // Use transaction to ensure atomicity
    await withTransaction(async (client) => {
      // First delete related records (job applications, etc.)
      await client.query(
        'DELETE FROM job_applications WHERE job_id = $1',
        [req.params.id]
      );

      // Then delete the job
      const result = await client.query(
        'DELETE FROM jobs WHERE id = $1 RETURNING id',
        [req.params.id]
      );

      if (result.rows.length === 0) {
        throw new Error('Job not found');
      }
    });

    res.json({ success: true, id: req.params.id });
  } catch (error) {
    console.error('Error deleting job:', error);

    if (error.message === 'Job not found') {
      return res.status(404).json({ error: 'Job not found' });
    }

    res.status(500).json({ error: 'Database error' });
  }
});

export default router;
