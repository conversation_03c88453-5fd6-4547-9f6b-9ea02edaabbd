import * as express from 'express';
import { query, withTransaction } from '../../services/postgresService';
import { QueryBuilder } from '../../utils/queryBuilder';
import { validateClient } from '../validators/clientValidator';

const router = express.Router();

// Get all clients
router.get('/', async (req, res) => {
  try {
    const { limit, offset, sort, status } = req.query;

    // Build query using QueryBuilder
    const queryBuilder = new QueryBuilder('clients');

    // Add filters if provided
    if (status) {
      queryBuilder.where('status', '=', status);
    }

    // Add sorting
    if (sort === 'name_asc') {
      queryBuilder.orderBy('company_name', 'ASC');
    } else if (sort === 'name_desc') {
      queryBuilder.orderBy('company_name', 'DESC');
    } else {
      queryBuilder.orderBy('created_at', 'DESC');
    }

    // Add pagination
    if (limit) {
      queryBuilder.limit(parseInt(limit as string));
    }

    if (offset) {
      queryBuilder.offset(parseInt(offset as string));
    }

    // Execute query
    const { text, params } = queryBuilder.buildSelect();
    const result = await query(text, params);

    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching clients:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Get client by ID
router.get('/:id', async (req, res) => {
  try {
    const result = await query(
      'SELECT * FROM clients WHERE id = $1',
      [req.params.id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Client not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error fetching client:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Create new client
router.post('/', async (req, res) => {
  try {
    // Validate request body
    const validation = validateClient(req.body);
    if (!validation.success) {
      return res.status(400).json({ errors: validation.errors });
    }

    const {
      company_name, industry, website, logo_url, address, city,
      state, zip_code, country, phone, email, status, notes
    } = req.body;

    // Create new client
    const queryBuilder = new QueryBuilder('clients');
    const { text, params } = queryBuilder.buildInsert({
      company_name,
      industry,
      website,
      logo_url,
      address,
      city,
      state,
      zip_code,
      country,
      phone,
      email,
      status: status || 'active',
      notes,
      created_at: new Date(),
      updated_at: new Date()
    });

    const result = await query(text, params);
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating client:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Update client
router.put('/:id', async (req, res) => {
  try {
    // Validate request body
    const validation = validateClient(req.body, true);
    if (!validation.success) {
      return res.status(400).json({ errors: validation.errors });
    }

    // Check if client exists
    const existingResult = await query(
      'SELECT id FROM clients WHERE id = $1',
      [req.params.id]
    );

    if (existingResult.rows.length === 0) {
      return res.status(404).json({ error: 'Client not found' });
    }

    // Update client
    const queryBuilder = new QueryBuilder('clients');
    queryBuilder.where('id', '=', req.params.id);

    const updateData: Record<string, any> = {
      updated_at: new Date()
    };

    // Only include fields that are provided
    const fields = [
      'company_name', 'industry', 'website', 'logo_url', 'address', 'city',
      'state', 'zip_code', 'country', 'phone', 'email', 'status', 'notes'
    ];

    fields.forEach(field => {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });

    const { text, params } = queryBuilder.buildUpdate(updateData);
    const result = await query(text, params);

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating client:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Delete client
router.delete('/:id', async (req, res) => {
  try {
    // Use transaction to ensure atomicity
    await withTransaction(async (client) => {
      // Check if client has associated jobs
      const jobsResult = await client.query(
        'SELECT COUNT(*) FROM jobs WHERE client_id = $1',
        [req.params.id]
      );

      if (parseInt(jobsResult.rows[0].count) > 0) {
        throw new Error('Client has associated jobs');
      }

      // Delete the client
      const result = await client.query(
        'DELETE FROM clients WHERE id = $1 RETURNING id',
        [req.params.id]
      );

      if (result.rows.length === 0) {
        throw new Error('Client not found');
      }
    });

    res.json({ success: true, id: req.params.id });
  } catch (error) {
    console.error('Error deleting client:', error);

    if (error.message === 'Client not found') {
      return res.status(404).json({ error: 'Client not found' });
    } else if (error.message === 'Client has associated jobs') {
      return res.status(409).json({ error: 'Cannot delete client with associated jobs' });
    }

    res.status(500).json({ error: 'Database error' });
  }
});

export default router;
