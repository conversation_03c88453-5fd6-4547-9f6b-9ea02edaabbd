import * as express from 'express';
import { query, withTransaction } from '../../services/postgresService';
import { QueryBuilder } from '../../utils/queryBuilder';
import { validateJobApplication } from '../validators/jobApplicationValidator';

const router = express.Router();

// Get all job applications
router.get('/', async (req, res) => {
  try {
    const { limit, offset, sort, status, job_id, candidate_id } = req.query;

    // Build query using QueryBuilder
    const queryBuilder = new QueryBuilder('job_applications');

    // Add filters if provided
    if (status) {
      queryBuilder.where('status', '=', status);
    }

    if (job_id) {
      queryBuilder.where('job_id', '=', job_id);
    }

    if (candidate_id) {
      queryBuilder.where('candidate_id', '=', candidate_id);
    }

    // Add sorting
    if (sort === 'date_asc') {
      queryBuilder.orderBy('applied_date', 'ASC');
    } else if (sort === 'date_desc') {
      queryBuilder.orderBy('applied_date', 'DESC');
    } else {
      queryBuilder.orderBy('created_at', 'DESC');
    }

    // Add pagination
    if (limit) {
      queryBuilder.limit(parseInt(limit as string));
    }

    if (offset) {
      queryBuilder.offset(parseInt(offset as string));
    }

    // Execute query
    const { text, params } = queryBuilder.buildSelect();
    const result = await query(text, params);

    // Get job and candidate information for each application
    const applicationsWithDetails = await Promise.all(
      result.rows.map(async (application) => {
        const jobResult = await query(
          'SELECT id, title FROM jobs WHERE id = $1',
          [application.job_id]
        );

        const candidateResult = await query(
          'SELECT id, first_name, last_name FROM candidates WHERE id = $1',
          [application.candidate_id]
        );

        return {
          ...application,
          job: jobResult.rows[0] || null,
          candidate: candidateResult.rows[0] || null
        };
      })
    );

    res.json(applicationsWithDetails);
  } catch (error) {
    console.error('Error fetching job applications:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Get job application by ID
router.get('/:id', async (req, res) => {
  try {
    const result = await query(
      'SELECT * FROM job_applications WHERE id = $1',
      [req.params.id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Job application not found' });
    }

    // Get job and candidate information
    const jobResult = await query(
      'SELECT id, title FROM jobs WHERE id = $1',
      [result.rows[0].job_id]
    );

    const candidateResult = await query(
      'SELECT id, first_name, last_name FROM candidates WHERE id = $1',
      [result.rows[0].candidate_id]
    );

    const application = {
      ...result.rows[0],
      job: jobResult.rows[0] || null,
      candidate: candidateResult.rows[0] || null
    };

    res.json(application);
  } catch (error) {
    console.error('Error fetching job application:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Create new job application
router.post('/', async (req, res) => {
  try {
    // Validate request body
    const validation = validateJobApplication(req.body);
    if (!validation.success) {
      return res.status(400).json({ errors: validation.errors });
    }

    const {
      job_id, candidate_id, status, stage, applied_date, cover_letter,
      resume_url, notes, salary_expectation, salary_currency, source, referrer
    } = req.body;

    // Check if job exists
    const jobResult = await query(
      'SELECT id FROM jobs WHERE id = $1',
      [job_id]
    );

    if (jobResult.rows.length === 0) {
      return res.status(404).json({ error: 'Job not found' });
    }

    // Check if candidate exists
    const candidateResult = await query(
      'SELECT id FROM candidates WHERE id = $1',
      [candidate_id]
    );

    if (candidateResult.rows.length === 0) {
      return res.status(404).json({ error: 'Candidate not found' });
    }

    // Check if application already exists
    const existingResult = await query(
      'SELECT id FROM job_applications WHERE job_id = $1 AND candidate_id = $2',
      [job_id, candidate_id]
    );

    if (existingResult.rows.length > 0) {
      return res.status(409).json({ error: 'Application already exists' });
    }

    // Create new job application
    const queryBuilder = new QueryBuilder('job_applications');
    const { text, params } = queryBuilder.buildInsert({
      job_id,
      candidate_id,
      status: status || 'applied',
      stage: stage || 'screening',
      applied_date: applied_date || new Date().toISOString(),
      cover_letter,
      resume_url,
      notes,
      salary_expectation,
      salary_currency: salary_currency || 'USD',
      source,
      referrer,
      created_at: new Date(),
      updated_at: new Date()
    });

    const result = await query(text, params);
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating job application:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Update job application
router.put('/:id', async (req, res) => {
  try {
    // Validate request body
    const validation = validateJobApplication(req.body, true);
    if (!validation.success) {
      return res.status(400).json({ errors: validation.errors });
    }

    // Check if job application exists
    const existingResult = await query(
      'SELECT id FROM job_applications WHERE id = $1',
      [req.params.id]
    );

    if (existingResult.rows.length === 0) {
      return res.status(404).json({ error: 'Job application not found' });
    }

    // If job_id is provided, check if job exists
    if (req.body.job_id) {
      const jobResult = await query(
        'SELECT id FROM jobs WHERE id = $1',
        [req.body.job_id]
      );

      if (jobResult.rows.length === 0) {
        return res.status(404).json({ error: 'Job not found' });
      }
    }

    // If candidate_id is provided, check if candidate exists
    if (req.body.candidate_id) {
      const candidateResult = await query(
        'SELECT id FROM candidates WHERE id = $1',
        [req.body.candidate_id]
      );

      if (candidateResult.rows.length === 0) {
        return res.status(404).json({ error: 'Candidate not found' });
      }
    }

    // Update job application
    const queryBuilder = new QueryBuilder('job_applications');
    queryBuilder.where('id', '=', req.params.id);

    const updateData: Record<string, any> = {
      updated_at: new Date()
    };

    // Only include fields that are provided
    const fields = [
      'job_id', 'candidate_id', 'status', 'stage', 'applied_date', 'cover_letter',
      'resume_url', 'notes', 'salary_expectation', 'salary_currency', 'source', 'referrer'
    ];

    fields.forEach(field => {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });

    const { text, params } = queryBuilder.buildUpdate(updateData);
    const result = await query(text, params);

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating job application:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Delete job application
router.delete('/:id', async (req, res) => {
  try {
    // Use transaction to ensure atomicity
    await withTransaction(async (client) => {
      // First delete related records (interviews, etc.)
      await client.query(
        'DELETE FROM interviews WHERE job_application_id = $1',
        [req.params.id]
      );

      await client.query(
        'DELETE FROM assessments WHERE job_application_id = $1',
        [req.params.id]
      );

      // Then delete the job application
      const result = await client.query(
        'DELETE FROM job_applications WHERE id = $1 RETURNING id',
        [req.params.id]
      );

      if (result.rows.length === 0) {
        throw new Error('Job application not found');
      }
    });

    res.json({ success: true, id: req.params.id });
  } catch (error) {
    console.error('Error deleting job application:', error);

    if (error.message === 'Job application not found') {
      return res.status(404).json({ error: 'Job application not found' });
    }

    res.status(500).json({ error: 'Database error' });
  }
});

export default router;
