import * as express from 'express';
import { query, withTransaction } from '../../services/postgresService';
import { QueryBuilder } from '../../utils/queryBuilder';
import { validateCandidate } from '../validators/candidateValidator';

const router = express.Router();

// Get all candidates
router.get('/', async (req, res) => {
  try {
    const { limit, offset, sort, status } = req.query;

    // Build query using QueryBuilder
    const queryBuilder = new QueryBuilder('candidates');

    // Add filters if provided
    if (status) {
      queryBuilder.where('status', '=', status);
    }

    // Add sorting
    if (sort === 'name_asc') {
      queryBuilder.orderBy('first_name', 'ASC');
    } else if (sort === 'name_desc') {
      queryBuilder.orderBy('first_name', 'DESC');
    } else {
      queryBuilder.orderBy('created_at', 'DESC');
    }

    // Add pagination
    if (limit) {
      queryBuilder.limit(parseInt(limit as string));
    }

    if (offset) {
      queryBuilder.offset(parseInt(offset as string));
    }

    // Execute query
    const { text, params } = queryBuilder.buildSelect();
    const result = await query(text, params);

    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching candidates:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Get candidate by ID
router.get('/:id', async (req, res) => {
  try {
    const result = await query(
      'SELECT * FROM candidates WHERE id = $1',
      [req.params.id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Candidate not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error fetching candidate:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Create new candidate
router.post('/', async (req, res) => {
  try {
    // Validate request body
    const validation = validateCandidate(req.body);
    if (!validation.success) {
      return res.status(400).json({ errors: validation.errors });
    }

    const { first_name, last_name, email, phone, skills, experience_years, status } = req.body;

    // Check if email already exists
    const existingResult = await query(
      'SELECT id FROM candidates WHERE email = $1',
      [email]
    );

    if (existingResult.rows.length > 0) {
      return res.status(409).json({ error: 'Email already exists' });
    }

    // Create new candidate
    const queryBuilder = new QueryBuilder('candidates');
    const { text, params } = queryBuilder.buildInsert({
      first_name,
      last_name,
      email,
      phone,
      skills: Array.isArray(skills) ? skills : [],
      experience_years,
      status: status || 'new',
      created_at: new Date(),
      updated_at: new Date()
    });

    const result = await query(text, params);
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating candidate:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Update candidate
router.put('/:id', async (req, res) => {
  try {
    // Validate request body
    const validation = validateCandidate(req.body, true);
    if (!validation.success) {
      return res.status(400).json({ errors: validation.errors });
    }

    // Check if candidate exists
    const existingResult = await query(
      'SELECT id FROM candidates WHERE id = $1',
      [req.params.id]
    );

    if (existingResult.rows.length === 0) {
      return res.status(404).json({ error: 'Candidate not found' });
    }

    // Check if email is unique (excluding current candidate)
    if (req.body.email) {
      const emailCheckResult = await query(
        'SELECT id FROM candidates WHERE email = $1 AND id != $2',
        [req.body.email, req.params.id]
      );

      if (emailCheckResult.rows.length > 0) {
        return res.status(409).json({ error: 'Email already exists' });
      }
    }

    // Update candidate
    const queryBuilder = new QueryBuilder('candidates');
    queryBuilder.where('id', '=', req.params.id);

    const updateData: Record<string, any> = {
      updated_at: new Date()
    };

    // Map all possible fields from request body to database columns
    const fieldMappings = [
      // Basic Information
      'first_name', 'last_name', 'email', 'phone', 'location',

      // Professional Information
      'current_position', 'current_company', 'experience_years', 'education',
      'desired_salary', 'salary_currency', 'availability_date', 'source',

      // URLs and Links
      'resume_url', 'linkedin_url', 'github_url', 'portfolio_url', 'twitter_url', 'cover_letter_url',

      // Status Information
      'status', 'secondary_status',

      // Assessment & Interview Information
      'english_level', 'interview_score', 'interview_notes',
      'challenge', 'challenge_notes', 'challenge_feedback',
      'drive_score', 'resilience_score', 'collaboration_score',

      // Additional Fields
      'notes', 'stargety_id', 'is_duplicate', 'result',

      // Talent Incubator
      'is_in_incubator'
    ];

    // Only include fields that are provided and not undefined
    fieldMappings.forEach(field => {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });

    // Handle skills array specially
    if (req.body.skills !== undefined) {
      updateData.skills = Array.isArray(req.body.skills) ? req.body.skills : [];
    }

    const { text, params } = queryBuilder.buildUpdate(updateData);
    const result = await query(text, params);

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating candidate:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Delete candidate
router.delete('/:id', async (req, res) => {
  try {
    // Use transaction to ensure atomicity
    await withTransaction(async (client) => {
      // First delete related records (job applications, etc.)
      await client.query(
        'DELETE FROM job_applications WHERE candidate_id = $1',
        [req.params.id]
      );

      // Then delete the candidate
      const result = await client.query(
        'DELETE FROM candidates WHERE id = $1 RETURNING id',
        [req.params.id]
      );

      if (result.rows.length === 0) {
        throw new Error('Candidate not found');
      }
    });

    res.json({ success: true, id: req.params.id });
  } catch (error) {
    console.error('Error deleting candidate:', error);

    if (error.message === 'Candidate not found') {
      return res.status(404).json({ error: 'Candidate not found' });
    }

    res.status(500).json({ error: 'Database error' });
  }
});

export default router;
