import { z } from 'zod';
import { isValidUrl } from '@/utils/urlUtils';

// Custom URL validator
const urlSchema = z.string().refine(
  (val) => val === '' || val === null || isValidUrl(val),
  { message: 'Invalid URL format' }
).nullable().optional();

// Define the schema for candidate validation
const candidateSchema = z.object({
  first_name: z.string().min(1, 'First name is required'),
  last_name: z.string().optional(),
  email: z.string().email('Invalid email format'),
  phone: z.string().optional(),
  location: z.string().optional(),
  resume_url: urlSchema,
  linkedin_url: urlSchema,
  github_url: urlSchema,
  portfolio_url: urlSchema,
  skills: z.array(z.string()).optional(),
  experience_years: z.number().int().min(0).optional(),
  education: z.string().optional(),
  current_company: z.string().optional(),
  current_position: z.string().optional(),
  desired_salary: z.number().optional(),
  salary_currency: z.string().optional(),
  availability_date: z.string().optional(),
  status: z.string().optional(),
  secondary_status: z.string().optional(),
  source: z.string().optional(),
  notes: z.string().optional(),
  // Additional fields for CSV import support
  stargety_id: z.string().optional(),
  is_duplicate: z.string().optional(),
  english_level: z.string().optional(),
  interview_score: z.number().min(0).max(10).optional(),
  interview_notes: z.string().optional(),
  challenge: z.string().optional(),
  challenge_notes: z.string().optional(),
  challenge_feedback: z.string().optional(),
  drive_score: z.number().min(0).max(10).optional(),
  resilience_score: z.number().min(0).max(10).optional(),
  collaboration_score: z.number().min(0).max(10).optional(),
  result: z.string().optional(),
});

// Partial schema for updates
const partialCandidateSchema = candidateSchema.partial();

/**
 * Validates candidate data
 * @param data The candidate data to validate
 * @param isPartial Whether to use partial validation (for updates)
 * @returns Validation result
 */
export function validateCandidate(data: any, isPartial = false) {
  try {
    const schema = isPartial ? partialCandidateSchema : candidateSchema;
    schema.parse(data);
    return { success: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map(err => ({
          path: err.path.join('.'),
          message: err.message
        }))
      };
    }
    return {
      success: false,
      errors: [{ path: '', message: 'Validation failed' }]
    };
  }
}
