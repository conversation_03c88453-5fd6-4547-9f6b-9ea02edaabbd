import { z } from 'zod';

// Define the schema for job validation
const jobSchema = z.object({
  client_id: z.string().uuid('Invalid client ID format').min(1, 'Client selection is required'),
  title: z.string().min(1, 'Job title is required'),
  description: z.string().optional(),
  requirements: z.string().optional(),
  location: z.string().optional(),
  salary_min: z.number().optional(),
  salary_max: z.number().optional(),
  salary_currency: z.string().optional(),
  employment_type: z.string().optional(),
  remote_type: z.string().optional(),
  status: z.string().optional(),
  priority: z.string().optional(),
  created_by: z.string().uuid('Invalid user ID format').optional(),
  published_at: z.string().optional().nullable(),
  closed_at: z.string().optional().nullable(),
});

// Partial schema for updates - client_id remains required even for updates
const partialJobSchema = jobSchema.partial().extend({
  client_id: z.string().uuid('Invalid client ID format').min(1, 'Client selection is required')
});

/**
 * Validates job data
 * @param data The job data to validate
 * @param isPartial Whether to use partial validation (for updates)
 * @returns Validation result
 */
export function validateJob(data: any, isPartial = false) {
  try {
    const schema = isPartial ? partialJobSchema : jobSchema;
    schema.parse(data);
    return { success: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map(err => ({
          path: err.path.join('.'),
          message: err.message
        }))
      };
    }
    return {
      success: false,
      errors: [{ path: '', message: 'Validation failed' }]
    };
  }
}
