import { z } from 'zod';

// Define the schema for job application validation
const jobApplicationSchema = z.object({
  job_id: z.string().uuid('Invalid job ID format'),
  candidate_id: z.string().uuid('Invalid candidate ID format'),
  status: z.string().optional(),
  stage: z.string().optional(),
  applied_date: z.string().optional(),
  cover_letter: z.string().optional(),
  resume_url: z.string().url('Invalid resume URL').optional().nullable(),
  notes: z.string().optional(),
  salary_expectation: z.number().optional(),
  salary_currency: z.string().optional(),
  source: z.string().optional(),
  referrer: z.string().optional(),
});

// Partial schema for updates
const partialJobApplicationSchema = jobApplicationSchema.partial();

/**
 * Validates job application data
 * @param data The job application data to validate
 * @param isPartial Whether to use partial validation (for updates)
 * @returns Validation result
 */
export function validateJobApplication(data: any, isPartial = false) {
  try {
    const schema = isPartial ? partialJobApplicationSchema : jobApplicationSchema;
    schema.parse(data);
    return { success: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map(err => ({
          path: err.path.join('.'),
          message: err.message
        }))
      };
    }
    return {
      success: false,
      errors: [{ path: '', message: 'Validation failed' }]
    };
  }
}
