import { z } from 'zod';

// Define the schema for client validation
const clientSchema = z.object({
  company_name: z.string().min(1, 'Company name is required'),
  industry: z.string().optional(),
  website: z.string().url('Invalid website URL').optional().nullable(),
  logo_url: z.string().url('Invalid logo URL').optional().nullable(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zip_code: z.string().optional(),
  country: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email('Invalid email format').optional(),
  status: z.string().optional(),
  notes: z.string().optional(),
});

// Partial schema for updates
const partialClientSchema = clientSchema.partial();

/**
 * Validates client data
 * @param data The client data to validate
 * @param isPartial Whether to use partial validation (for updates)
 * @returns Validation result
 */
export function validateClient(data: any, isPartial = false) {
  try {
    const schema = isPartial ? partialClientSchema : clientSchema;
    schema.parse(data);
    return { success: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map(err => ({
          path: err.path.join('.'),
          message: err.message
        }))
      };
    }
    return {
      success: false,
      errors: [{ path: '', message: 'Validation failed' }]
    };
  }
}
