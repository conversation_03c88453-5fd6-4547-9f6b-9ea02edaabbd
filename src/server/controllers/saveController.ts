/**
 * Controlador para manejar las solicitudes de guardado
 */

import { Request, Response } from 'express';
import apiService, { SaveDataParams } from '../../services/apiService';

/**
 * Guarda datos en la aplicación
 * @param req Solicitud
 * @param res Respuesta
 */
export const saveData = async (req: Request, res: Response) => {
  try {
    // Obtener datos de la solicitud
    const { entity, data, id } = req.body;
    const apiKey = req.headers['x-api-key'] as string || req.body.apiKey;
    
    // Validar datos
    if (!entity || !data) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: entity, data',
      });
    }
    
    // Guardar datos
    const result = await apiService.saveData({
      entity,
      data,
      id,
      apiKey,
    });
    
    // Devolver resultado
    return res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    console.error('Error saving data:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Internal server error',
    });
  }
};

/**
 * Guarda múltiples datos en una transacción
 * @param req Solicitud
 * @param res Respuesta
 */
export const saveMultipleData = async (req: Request, res: Response) => {
  try {
    // Obtener datos de la solicitud
    const { items } = req.body;
    const apiKey = req.headers['x-api-key'] as string || req.body.apiKey;
    
    // Validar datos
    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Missing required field: items (array)',
      });
    }
    
    // Guardar datos
    const results = await apiService.saveMultipleData(items, apiKey);
    
    // Devolver resultado
    return res.status(200).json({
      success: true,
      data: results,
    });
  } catch (error: any) {
    console.error('Error saving multiple data:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Internal server error',
    });
  }
};

export default {
  saveData,
  saveMultipleData,
};
