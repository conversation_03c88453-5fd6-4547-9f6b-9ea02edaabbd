/**
 * API Service for interacting with the backend
 */

import axios from 'axios';
import { z } from 'zod';
import { apiUrl } from '@/lib/database/config';

// Types of available entities
export type EntityType = 'clients' | 'jobs' | 'candidates' | 'job_applications' | 'interviews' | 'assessments' | 'communications' | 'notifications';

// Create axios instance with base configuration
export const apiClient = axios.create({
  baseURL: apiUrl,
  headers: {
    'Content-Type': 'application/json'
  }
});



// Add request interceptor for authentication if needed
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor for better error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;

      switch (status) {
        case 404:
          error.message = data.error || 'Resource not found';
          break;
        case 400:
          error.message = data.error || 'Invalid request data';
          break;
        case 500:
          error.message = data.error || 'Internal server error';
          break;
        default:
          error.message = data.error || `Request failed with status ${status}`;
      }
    } else if (error.request) {
      // Request was made but no response received
      error.message = 'No response from server. Please check your connection.';
    } else {
      // Something else happened
      error.message = error.message || 'An unexpected error occurred';
    }

    return Promise.reject(error);
  }
);

// Schema for validating save data
const saveDataSchema = z.object({
  entity: z.enum(['clients', 'jobs', 'candidates', 'job_applications', 'interviews', 'assessments', 'communications', 'notifications']),
  data: z.record(z.any()),
  id: z.string().optional(),
});

// Interface for save data parameters
export interface SaveDataParams {
  entity: EntityType;
  data: Record<string, any>;
  id?: string;
}

/**
 * Save data to the application
 * @param params Parameters for saving data
 * @returns Saved data
 */
export const saveData = async (params: SaveDataParams): Promise<Record<string, any>> => {
  try {
    // Validate parameters
    const validatedParams = saveDataSchema.parse(params);

    // Prepare data for saving
    const now = new Date().toISOString();
    const dataToSave = {
      ...validatedParams.data,
      updated_at: now,
    };

    try {
      // If it's an update
      if (validatedParams.id) {
        // Update record
        const response = await apiClient.put(`/${validatedParams.entity}/${validatedParams.id}`, dataToSave);
        return response.data;
      } else {
        // Create new record
        const newData = {
          ...dataToSave,
          created_at: now,
        };

        const response = await apiClient.post(`/${validatedParams.entity}`, newData);
        return response.data;
      }
    } catch (serviceError: any) {
      console.error('Error in API service:', serviceError);
      throw serviceError;
    }
  } catch (error: any) {
    console.error('Error saving data:', error);
    throw new Error(error.message || 'Failed to save data');
  }
};

/**
 * Save multiple data items in a transaction
 * @param items Array of data to save
 * @returns Array of saved data
 */
export const saveMultipleData = async (
  items: SaveDataParams[]
): Promise<Record<string, any>[]> => {
  try {
    // Results
    const results: Record<string, any>[] = [];

    // Save each item
    for (const item of items) {
      const result = await saveData(item);
      results.push(result);
    }

    return results;
  } catch (error: any) {
    console.error('Error saving multiple data:', error);
    throw new Error(`Failed to save multiple data: ${error.message}`);
  }
};

// Generic API methods
export async function fetchAll(entity: string) {
  try {
    const response = await apiClient.get(`/${entity}`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching ${entity}:`, error);
    throw error;
  }
}

export async function fetchById(entity: string, id: string) {
  try {
    const response = await apiClient.get(`/${entity}/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching ${entity} with id ${id}:`, error);
    throw error;
  }
}

export async function create(entity: string, data: any) {
  try {
    const response = await apiClient.post(`/${entity}`, data);
    return response.data;
  } catch (error) {
    console.error(`Error creating ${entity}:`, error);
    throw error;
  }
}

export async function update(entity: string, id: string, data: any) {
  try {
    const response = await apiClient.put(`/${entity}/${id}`, data);
    return response.data;
  } catch (error) {
    console.error(`Error updating ${entity} with id ${id}:`, error);
    throw error;
  }
}

export async function remove(entity: string, id: string) {
  try {
    const response = await apiClient.delete(`/${entity}/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting ${entity} with id ${id}:`, error);
    throw error;
  }
}

export async function bulkDelete(entity: string, ids: string[]) {
  try {
    const response = await apiClient.post(`/bulk/delete/${entity}`, { ids });
    return response.data;
  } catch (error) {
    console.error(`Error bulk deleting ${entity}:`, error);
    throw error;
  }
}

// Entity-specific methods
export const candidatesApi = {
  getAll: () => fetchAll('candidates'),
  getById: (id: string) => fetchById('candidates', id),
  create: (data: any) => create('candidates', data),
  update: (id: string, data: any) => update('candidates', id, data),
  delete: (id: string) => remove('candidates', id),
  bulkDelete: (ids: string[]) => bulkDelete('candidates', ids)
};

export const jobsApi = {
  getAll: () => fetchAll('jobs'),
  getById: (id: string) => fetchById('jobs', id),
  create: (data: any) => create('jobs', data),
  update: (id: string, data: any) => update('jobs', id, data),
  delete: (id: string) => remove('jobs', id)
};

export const clientsApi = {
  getAll: () => fetchAll('clients'),
  getById: (id: string) => fetchById('clients', id),
  create: (data: any) => create('clients', data),
  update: (id: string, data: any) => update('clients', id, data),
  delete: (id: string) => remove('clients', id)
};

export const jobApplicationsApi = {
  getAll: () => fetchAll('job-applications'),
  getById: (id: string) => fetchById('job-applications', id),
  create: (data: any) => create('job-applications', data),
  update: (id: string, data: any) => update('job-applications', id, data),
  delete: (id: string) => remove('job-applications', id)
};

// Export the API client
export default apiClient;
