/**
 * Candidate Service for interacting with the PostgreSQL database via API
 */

import axios from 'axios';
import { Candidate } from '@/types/candidate';
import { generateCandidateId } from '@/utils/idGenerators';
import { splitFullName } from '@/utils/nameUtils';

// API base URL
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

// Create axios instance
const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

/**
 * Converts camelCase keys to snake_case
 * @param obj Object with camelCase keys
 * @returns Object with snake_case keys
 */
function camelToSnakeCase(obj: Record<string, any>): Record<string, any> {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => camelToSnakeCase(item));
  }

  return Object.keys(obj).reduce((acc, key) => {
    // If the key already contains underscores, assume it's already snake_case
    const snakeKey = key.includes('_') ? key : key.replace(/([A-Z])/g, '_$1').toLowerCase();

    // Handle nested objects and arrays
    const value = obj[key];
    acc[snakeKey] = camelToSnakeCase(value);

    return acc;
  }, {} as Record<string, any>);
}

// Candidate service
const candidateService = {
  /**
   * Get all candidates
   * @returns Promise with candidates array
   */
  async getCandidates(): Promise<Candidate[]> {
    try {
      console.log('Fetching candidates from API');
      const response = await apiClient.get('/candidates');
      return response.data;
    } catch (error) {
      console.error('Error fetching candidates:', error);
      return [];
    }
  },

  /**
   * Get a candidate by ID
   * @param id Candidate ID
   * @returns Promise with candidate data
   */
  async getCandidate(id: string): Promise<Candidate | null> {
    try {
      console.log(`Fetching candidate ${id} from API`);
      const response = await apiClient.get(`/candidates/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching candidate ${id}:`, error);
      return null;
    }
  },

  /**
   * Create a new candidate
   * @param candidate Candidate data
   * @returns Promise with created candidate
   */
  async createCandidate(candidate: Partial<Candidate>): Promise<Candidate> {
    try {
      console.log('Creating candidate in PostgreSQL mode');

      // Handle name splitting if we have name but not firstName/lastName
      let firstName = candidate.firstName;
      let lastName = candidate.lastName;

      if (candidate.name && (!firstName || !lastName)) {
        const nameParts = splitFullName(candidate.name);
        firstName = firstName || nameParts.firstName;
        lastName = lastName || nameParts.lastName;
      }

      // Generate ID if not provided
      if (!candidate.id) {
        const id = generateCandidateId(
          firstName || '',
          lastName || '',
          candidate.phone || ''
        );
        candidate.id = id;
      }

      // Prepare data with proper field mapping
      const candidateData = {
        // Basic Information
        firstName: firstName,
        lastName: lastName,
        email: candidate.email,
        phone: candidate.phone,
        location: candidate.location,

        // Professional Information
        currentPosition: candidate.position,
        currentCompany: candidate.currentCompany,
        experienceYears: candidate.experienceYears,
        education: candidate.education,
        desiredSalary: candidate.desiredSalary,
        salaryCurrency: candidate.salaryCurrency || 'USD',
        availabilityDate: candidate.availabilityDate,
        source: candidate.source,
        skills: candidate.skills || [],

        // Status Information
        status: candidate.status || 'new',
        secondaryStatus: candidate.secondaryStatus,
        englishLevel: candidate.englishLevel,

        // URLs and Links
        portfolioUrl: candidate.portfolio,
        resumeUrl: candidate.resumeUrl,
        coverLetterUrl: candidate.coverLetterUrl,
        linkedinUrl: candidate.socialLinks?.linkedin || candidate.linkedinUrl,
        githubUrl: candidate.socialLinks?.github || candidate.githubUrl,
        twitterUrl: candidate.socialLinks?.twitter || candidate.twitterUrl,

        // Interview & Assessment
        interviewScore: candidate.interviewScore,
        interviewNotes: candidate.interviewNotes,
        challenge: candidate.challenge,
        challengeNotes: candidate.challengeNotes,
        challengeFeedback: candidate.challengeFeedback,

        // Scoring
        driveScore: candidate.driveScore,
        resilienceScore: candidate.resilienceScore,
        collaborationScore: candidate.collaborationScore,

        // Additional Fields
        notes: candidate.notes,
        stargetyId: candidate.stargetyId,
        isDuplicate: candidate.isDuplicate || 'new',
        result: candidate.result
      };

      // Convert camelCase to snake_case for API
      const apiData = camelToSnakeCase(candidateData);

      console.log('Sending candidate data to API:', apiData);
      const response = await apiClient.post('/candidates', apiData);
      return response.data;
    } catch (error: any) {
      console.error('Error creating candidate:', error);

      // Handle specific error cases
      if (error.response?.status === 409) {
        throw new Error('This email address is already being used by another candidate. Please use a different email address.');
      } else if (error.response?.status === 400) {
        throw new Error('Invalid data provided. Please check all fields and try again.');
      }

      throw new Error('Failed to create candidate. Please try again.');
    }
  },

  /**
   * Update an existing candidate
   * @param id Candidate ID
   * @param candidate Updated candidate data
   * @returns Promise with updated candidate
   */
  async updateCandidate(id: string, candidate: Partial<Candidate>): Promise<Candidate> {
    try {
      console.log(`Updating candidate ${id} in PostgreSQL mode`);

      // Handle name splitting if we have name but not firstName/lastName
      let firstName = candidate.firstName;
      let lastName = candidate.lastName;

      if (candidate.name && (!firstName || !lastName)) {
        const nameParts = splitFullName(candidate.name);
        firstName = firstName || nameParts.firstName;
        lastName = lastName || nameParts.lastName;
      }

      // Prepare data with proper field mapping
      const candidateData = {
        // Basic Information
        firstName: firstName,
        lastName: lastName,
        email: candidate.email,
        phone: candidate.phone,
        location: candidate.location,

        // Professional Information
        currentPosition: candidate.position,
        currentCompany: candidate.currentCompany,
        experienceYears: candidate.experienceYears,
        education: candidate.education,
        desiredSalary: candidate.desiredSalary,
        salaryCurrency: candidate.salaryCurrency,
        availabilityDate: candidate.availabilityDate,
        source: candidate.source,
        skills: candidate.skills || [],

        // Status Information
        status: candidate.status,
        secondaryStatus: candidate.secondaryStatus,
        englishLevel: candidate.englishLevel,

        // URLs and Links
        portfolioUrl: candidate.portfolio,
        resumeUrl: candidate.resumeUrl,
        coverLetterUrl: candidate.coverLetterUrl,
        linkedinUrl: candidate.socialLinks?.linkedin || candidate.linkedinUrl,
        githubUrl: candidate.socialLinks?.github || candidate.githubUrl,
        twitterUrl: candidate.socialLinks?.twitter || candidate.twitterUrl,

        // Interview & Assessment
        interviewScore: candidate.interviewScore,
        interviewNotes: candidate.interviewNotes,
        challenge: candidate.challenge,
        challengeNotes: candidate.challengeNotes,
        challengeFeedback: candidate.challengeFeedback,

        // Scoring
        driveScore: candidate.driveScore,
        resilienceScore: candidate.resilienceScore,
        collaborationScore: candidate.collaborationScore,

        // Additional Fields
        notes: candidate.notes,
        stargetyId: candidate.stargetyId,
        isDuplicate: candidate.isDuplicate,
        result: candidate.result
      };

      // Convert camelCase to snake_case for API
      const apiData = camelToSnakeCase(candidateData);

      // Remove undefined values
      Object.keys(apiData).forEach(key => {
        if (apiData[key] === undefined) {
          delete apiData[key];
        }
      });

      console.log('Sending updated candidate data to API:', apiData);
      const response = await apiClient.put(`/candidates/${id}`, apiData);
      return response.data;
    } catch (error: any) {
      console.error(`Error updating candidate ${id}:`, error);

      // Handle specific error cases
      if (error.response?.status === 409) {
        throw new Error('This email address is already being used by another candidate. Please use a different email address.');
      } else if (error.response?.status === 400) {
        throw new Error('Invalid data provided. Please check all fields and try again.');
      } else if (error.response?.status === 404) {
        throw new Error('Candidate not found. It may have been deleted by another user.');
      }

      throw new Error('Failed to update candidate. Please try again.');
    }
  },

  /**
   * Delete a candidate
   * @param id Candidate ID
   * @returns Promise with success status
   */
  async deleteCandidate(id: string): Promise<boolean> {
    try {
      console.log(`Deleting candidate ${id} in PostgreSQL mode`);
      await apiClient.delete(`/candidates/${id}`);
      return true;
    } catch (error) {
      console.error(`Error deleting candidate ${id}:`, error);
      return false;
    }
  }
};

export default candidateService;
