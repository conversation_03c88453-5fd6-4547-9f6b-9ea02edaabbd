/**
 * Environment Service
 *
 * This service provides functions to detect and manage the application's
 * execution environment (development or production).
 */

/**
 * Supported environment types
 */
export type EnvironmentMode = 'development' | 'production';

/**
 * Determines if the application is running in a development environment
 *
 * Detection is based on:
 * 1. The VITE_APP_ENV environment variable (if available)
 * 2. The URL hostname (localhost or 127.0.0.1)
 * 3. Common development ports (5173, 5174, 3000)
 *
 * @returns {boolean} true if we're in a development environment, false otherwise
 */
export const isDevelopmentEnvironment = (): boolean => {
  // 1. Check environment variable (highest priority)
  const envMode = import.meta.env.VITE_APP_ENV;
  if (envMode) {
    return envMode.toLowerCase() === 'development';
  }

  // 2. Check current URL
  try {
    const url = new URL(window.location.href);

    // Check if we're on localhost or 127.0.0.1
    const isLocalhost = url.hostname === 'localhost' || url.hostname === '127.0.0.1';

    // Check if we're on a common development port
    const isDevelopmentPort = ['5173', '5174', '3000', '3001'].includes(url.port);

    // We're in development if it's localhost or a development port
    return isLocalhost || isDevelopmentPort;
  } catch (error) {
    // In case of error, assume production for safety
    console.error('Error detecting environment:', error);
    return false;
  }
};

/**
 * Gets the current environment mode as a string
 *
 * @returns {EnvironmentMode} 'development' or 'production'
 */
export const getEnvironmentMode = (): EnvironmentMode => {
  return isDevelopmentEnvironment() ? 'development' : 'production';
};

/**
 * Checks if a specific feature is enabled in the current environment
 *
 * @param {string} featureName - Name of the feature to check
 * @returns {boolean} true if the feature is enabled, false otherwise
 */
export const isFeatureEnabled = (featureName: string): boolean => {
  const featureFlag = import.meta.env[`VITE_FEATURE_${featureName.toUpperCase()}`];

  // If the variable exists and is 'true', the feature is enabled
  if (featureFlag && featureFlag.toLowerCase() === 'true') {
    return true;
  }

  // If we're in development, enable all features by default
  if (isDevelopmentEnvironment()) {
    return true;
  }

  return false;
};

/**
 * Environment service
 */
const environmentService = {
  isDevelopmentEnvironment,
  getEnvironmentMode,
  isFeatureEnabled
};

export default environmentService;
