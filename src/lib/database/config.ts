// Database configuration

// PostgreSQL configuration
export const postgresConfig = {
  schema: 'public',
  tables: {
    users: 'users',
    clients: 'clients',
    jobs: 'jobs',
    candidates: 'candidates',
    candidateNotes: 'candidate_notes',
    jobApplications: 'job_applications',
    interviews: 'interviews',
    assessments: 'assessments',
    communications: 'communications',
    notifications: 'notifications',
  },
};

// Database connection parameters
export const dbConnectionParams = {
  host: import.meta.env.VITE_POSTGRES_HOST || 'localhost',
  port: parseInt(import.meta.env.VITE_POSTGRES_PORT || '5432'),
  database: import.meta.env.VITE_POSTGRES_DATABASE || 'postgres',
  user: import.meta.env.VITE_POSTGRES_USER || 'postgres',
  password: import.meta.env.VITE_POSTGRES_PASSWORD || 'postgres',
  ssl: import.meta.env.VITE_POSTGRES_SSL === 'true',
};

// API URL
export const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

// Database type - always postgres now
export const databaseType = 'postgres';
