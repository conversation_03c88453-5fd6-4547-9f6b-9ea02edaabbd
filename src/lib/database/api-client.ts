// API client for database operations
// This client is compatible with browser environments
import { apiUrl } from '@/lib/database/config';

// Log initialization for debugging in development only
if (import.meta.env.DEV) {
  console.log('API client initialized with URL:', apiUrl);
}

// Helper function to handle API responses
async function handleResponse(response: Response) {
  if (!response.ok) {
    const errorText = await response.text();
    let errorMessage = `API error: ${response.status}`;

    // Customize messages based on status code
    if (response.status === 401) {
      errorMessage = `Unauthorized. Please check your credentials. (${response.status})`;
    } else if (response.status === 403) {
      errorMessage = `Forbidden. Please check your permissions. (${response.status})`;
    } else if (response.status === 404) {
      errorMessage = `Resource not found. (${response.status})`;
      return { data: [] }; // Return empty array to avoid errors
    }

    throw new Error(`${errorMessage} - ${errorText}`);
  }

  const contentType = response.headers.get('content-type');
  if (contentType && contentType.includes('application/json')) {
    return response.json();
  }

  return response.text();
}

// Generic function to fetch data from API
export async function fetchFromApi(endpoint: string, options: RequestInit = {}) {
  const url = `${apiUrl}/${endpoint}`;
  const response = await fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
  });
  return handleResponse(response);
}

// Get all records from a table
export async function getRecords(table: string, options: {
  filter?: Record<string, any>;
  sort?: string;
  order?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
} = {}) {
  try {
    const queryParams = new URLSearchParams();

    if (options.filter) {
      queryParams.append('filter', JSON.stringify(options.filter));
    }
    if (options.sort) {
      queryParams.append('sort', options.sort);
      if (options.order) {
        queryParams.append('order', options.order);
      }
    }
    if (options.limit) {
      queryParams.append('limit', options.limit.toString());
    }
    if (options.offset) {
      queryParams.append('offset', options.offset.toString());
    }

    const endpoint = `${table}?${queryParams.toString()}`;
    return await fetchFromApi(endpoint);
  } catch (error) {
    console.error(`Error getting records from ${table}:`, error);
    return { data: [] };
  }
}

// Get a single record by ID
export async function getRecord(table: string, id: string) {
  try {
    const endpoint = `${table}/${id}`;
    return fetchFromApi(endpoint);
  } catch (error) {
    console.error(`Error getting record ${id} from ${table}:`, error);
    throw error;
  }
}

// Create a new record
export async function createRecord(table: string, data: Record<string, any>) {
  try {
    const endpoint = table;
    return fetchFromApi(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  } catch (error) {
    console.error(`Error creating record in ${table}:`, error);
    throw error;
  }
}

// Update an existing record
export async function updateRecord(table: string, id: string, data: Record<string, any>) {
  try {
    const endpoint = `${table}/${id}`;
    return fetchFromApi(endpoint, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  } catch (error) {
    console.error(`Error updating record ${id} in ${table}:`, error);
    throw error;
  }
}

// Delete a record
export async function deleteRecord(table: string, id: string) {
  try {
    const endpoint = `${table}/${id}`;
    return fetchFromApi(endpoint, {
      method: 'DELETE',
    });
  } catch (error) {
    console.error(`Error deleting record ${id} from ${table}:`, error);
    throw error;
  }
}

// Check database connection status with enhanced error handling
export async function checkConnection() {
  try {
    // Try to connect to the enhanced health endpoint
    try {
      const result = await fetchFromApi('health');

      // Check if the health endpoint returned connection status
      if (result && typeof result === 'object') {
        // If the status is 'ok', we're connected
        const isConnected = result.status === 'ok';

        // Return the full status object for more detailed information
        return {
          isConnected,
          status: result
        };
      }

      // If we got a response but not in the expected format
      return {
        isConnected: true,
        status: {
          status: 'ok',
          database: 'PostgreSQL',
          warning: 'Health endpoint returned unexpected format'
        }
      };
    } catch (apiError) {
      console.error('API connection error:', apiError);

      // If it's a 404, the health endpoint might not exist but the server could be running
      if (apiError instanceof Error && apiError.message.includes('404')) {
        return {
          isConnected: true,
          status: {
            status: 'unknown',
            database: 'PostgreSQL',
            warning: 'Health endpoint not available, but server might be running'
          }
        };
      }

      // For network errors (like CORS or server not running)
      if (apiError instanceof Error &&
         (apiError.message.includes('NetworkError') ||
          apiError.message.includes('Failed to fetch'))) {
        return {
          isConnected: false,
          status: {
            status: 'error',
            error: 'Network error - API server may be down',
            details: apiError.message
          }
        };
      }

      // For all other API errors
      return {
        isConnected: false,
        status: {
          status: 'error',
          error: apiError instanceof Error ? apiError.message : 'Unknown API error',
          timestamp: new Date().toISOString()
        }
      };
    }
  } catch (error) {
    console.error('Database connection check error:', error);
    return {
      isConnected: false,
      status: {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }
    };
  }
}

// Export all functions as a single object
const apiClient = {
  fetchFromApi,
  getRecords,
  getRecord,
  createRecord,
  updateRecord,
  deleteRecord,
  checkConnection,
};

export default apiClient;
