// Export all database-related modules
export * from './config';

// Import API client (browser-compatible)
import apiClient from './api-client';

// Export the API client as the default database client
export default apiClient;

// Note: The following imports are commented out because they are not compatible with browser environments
// They are kept here for reference and for potential server-side usage
// export { default as postgresPool, query as postgresQuery } from './postgres-client';
// PostgreSQL-only database client
