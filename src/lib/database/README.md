# Database Integration

Este directorio contiene los archivos necesarios para la integración con PostgreSQL, que es la base de datos para la aplicación.

## Archivos

- `postgres-client.ts`: Cliente de PostgreSQL para interactuar directamente con la base de datos PostgreSQL (solo para uso en el servidor).
- `api-client.ts`: Cliente para interactuar con la API REST que se comunica con PostgreSQL (compatible con navegadores).
- `config.ts`: Configuración para la base de datos.
- `index.ts`: Exporta todos los módulos y proporciona un cliente unificado.

## Configuración

### PostgreSQL

Para configurar PostgreSQL, sigue estos pasos:

1. Asegúrate de que los contenedores de Docker estén corriendo:
   ```bash
   docker-compose up -d
   ```

2. Configura las siguientes variables de entorno en el archivo `.env`:
   ```
   VITE_POSTGRES_HOST=localhost
   VITE_POSTGRES_PORT=5432
   VITE_POSTGRES_DATABASE=postgres
   VITE_POSTGRES_USER=postgres
   VITE_POSTGRES_PASSWORD=postgres
   VITE_POSTGRES_SSL=false
   ```

3. Configura la URL de la API:
   ```
   VITE_API_URL=http://localhost:3001/api
   ```

## Uso

Para usar la base de datos en los componentes, importa el cliente de base de datos:

```typescript
import apiClient from '@/lib/database';

// Ejemplo de uso con la API REST
const { data: clients } = await apiClient.getRecords('clients');
const { data: client } = await apiClient.getRecord('clients', '123');
const result = await apiClient.createRecord('clients', { name: 'New Client' });
```

También puedes usar los hooks personalizados que se encuentran en el directorio `src/hooks`:

```typescript
import { useClients } from '@/hooks/useClients';

// En tu componente
const { clients, isLoading, error } = useClients();
```

## Estructura de la base de datos

La base de datos tiene las siguientes tablas principales:

- `users`: Usuarios del sistema
- `clients`: Clientes (empresas)
- `jobs`: Trabajos o posiciones abiertas
- `candidates`: Candidatos
- `job_applications`: Aplicaciones de candidatos a trabajos
- `interviews`: Entrevistas programadas
- `assessments`: Evaluaciones de candidatos
- `communications`: Comunicaciones con candidatos
- `notifications`: Notificaciones del sistema
