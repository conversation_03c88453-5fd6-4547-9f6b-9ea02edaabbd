import Typesense from 'typesense';

// Check if Typesense is enabled - default to true if not specified
const isTypesenseEnabled = import.meta.env.VITE_ENABLE_TYPESENSE !== 'false';

// Get environment variables
// Always use 'localhost' for browser connections, regardless of environment variable
const host = 'localhost';
const port = import.meta.env.VITE_TYPESENSE_PORT || '8108';
const protocol = import.meta.env.VITE_TYPESENSE_PROTOCOL || 'http';
const apiKey = import.meta.env.VITE_TYPESENSE_API_KEY || 'xyz123';

// Create a client with more robust configuration
const typesenseClient = isTypesenseEnabled
  ? new Typesense.Client({
      nodes: [
        {
          host,
          port: parseInt(port as string),
          protocol: protocol as 'http' | 'https',
        },
      ],
      apiKey,
      connectionTimeoutSeconds: 5, // Increased from 2 to 5 seconds
      retryIntervalSeconds: 1, // Increased from 0.5 to 1 second
      numRetries: 3, // Add explicit retry count
      logLevel: 'info', // Set log level to info
    })
  : null;

// Create a mock client for when Typesense is disabled
const mockClient = {
  collections: function(...args: any[]) {
    return {
      retrieve: async () => [],
      documents: () => ({
        search: async () => ({ found: 0, hits: [], search_time_ms: 0 }),
        create: async () => ({}),
        delete: async () => ({}),
        import: async () => ([]),
      }),
      delete: async () => ({}),
    };
  },
  multiSearch: {
    perform: async function(...args: any[]) {
      return { results: [{ found: 0, hits: [], search_time_ms: 0 }] };
    }
  }
};

// Export a wrapped client that handles connection issues gracefully
const wrappedClient = isTypesenseEnabled ? {
  ...typesenseClient,

  // Override collections method to handle connection issues
  collections: function(...args: any[]) {
    if (!typesenseClient) return mockClient.collections(...args);

    const collection = typesenseClient.collections(...args);

    // Wrap the retrieve method to handle connection issues
    const originalRetrieve = collection.retrieve;
    collection.retrieve = async function() {
      try {
        return await originalRetrieve.apply(this);
      } catch (error) {
        console.warn('Typesense connection issue:', error);
        // Return empty array instead of throwing
        return [];
      }
    };

    return collection;
  },

  // Override multiSearch to handle connection issues
  multiSearch: {
    ...typesenseClient?.multiSearch,
    perform: async function(...args: any[]) {
      if (!typesenseClient) return mockClient.multiSearch.perform(...args);

      try {
        const result = await typesenseClient.multiSearch.perform(...args);

        // Ensure the results array exists and has at least one element
        if (!result || !result.results || !Array.isArray(result.results) || result.results.length === 0) {
          console.warn('Typesense returned invalid results structure:', result);
          return { results: [{ found: 0, hits: [], search_time_ms: 0 }] };
        }

        return result;
      } catch (error) {
        console.warn('Typesense search error:', error);
        // Return empty results instead of throwing
        return { results: [{ found: 0, hits: [], search_time_ms: 0 }] };
      }
    }
  }
} : mockClient;

export default wrappedClient;
