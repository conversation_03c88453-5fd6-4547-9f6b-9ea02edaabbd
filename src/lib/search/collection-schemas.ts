import { CollectionCreateSchema } from 'typesense/lib/Typesense/Collections';

// Base schema for all searchable entities
const baseSchema: Partial<CollectionCreateSchema> = {
  default_sorting_field: 'created_at',
  fields: [
    { name: 'id', type: 'string' },
    { name: 'title', type: 'string' },
    { name: 'description', type: 'string', optional: true },
    { name: 'created_at', type: 'int64' },
    { name: 'updated_at', type: 'int64' },
    { name: 'entity_type', type: 'string', facet: true },
  ],
};

// Job collection schema
export const jobsSchema: CollectionCreateSchema = {
  name: 'jobs',
  ...baseSchema,
  fields: [
    ...(baseSchema.fields || []),
    { name: 'company', type: 'string', facet: true, optional: true },
    { name: 'location', type: 'string', facet: true, optional: true },
    { name: 'salary_range', type: 'string', facet: true, optional: true },
    { name: 'job_type', type: 'string', facet: true, optional: true },
    { name: 'status', type: 'string', facet: true },
    { name: 'skills', type: 'string[]', facet: true, optional: true },
  ],
};

// Candidate collection schema
export const candidatesSchema: CollectionCreateSchema = {
  name: 'candidates',
  ...baseSchema,
  fields: [
    ...(baseSchema.fields || []),
    { name: 'name', type: 'string' },
    { name: 'email', type: 'string' },
    { name: 'phone', type: 'string', optional: true },
    { name: 'location', type: 'string', facet: true, optional: true },
    { name: 'skills', type: 'string[]', facet: true, optional: true },
    { name: 'experience_years', type: 'int32', facet: true, optional: true },
    { name: 'status', type: 'string', facet: true },
    { name: 'secondary_status', type: 'string', facet: true, optional: true },
  ],
};

// Client collection schema
export const clientsSchema: CollectionCreateSchema = {
  name: 'clients',
  ...baseSchema,
  fields: [
    ...(baseSchema.fields || []),
    { name: 'company_name', type: 'string' },
    { name: 'contact_name', type: 'string' },
    { name: 'email', type: 'string' },
    { name: 'phone', type: 'string', optional: true },
    { name: 'industry', type: 'string', facet: true, optional: true },
    { name: 'location', type: 'string', facet: true, optional: true },
  ],
};

// All schemas
export const allSchemas = [jobsSchema, candidatesSchema, clientsSchema];

// Schema map for easy access
export const schemaMap = {
  jobs: jobsSchema,
  candidates: candidatesSchema,
  clients: clientsSchema,
};

export type EntityType = keyof typeof schemaMap;
