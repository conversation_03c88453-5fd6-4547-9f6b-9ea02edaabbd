import typesenseClient from './typesense-client';
import { EntityType } from './collection-schemas';

// Define search result types
export interface SearchResultItem {
  id: string;
  title: string;
  description?: string;
  entity_type: EntityType;
  [key: string]: any;
}

export interface GroupedSearchResults {
  [key: string]: {
    count: number;
    hits: SearchResultItem[];
  };
}

export interface SearchResults {
  found: number;
  grouped_hits?: GroupedSearchResults;
  hits?: SearchResultItem[];
  facet_counts?: any[];
  page: number;
  request_params?: any;
  search_time_ms?: number;
}

// Cache for search results
const searchCache = new Map<string, { timestamp: number; results: SearchResults }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

// Search across all collections
export const searchAll = async (
  query: string,
  options: {
    page?: number;
    perPage?: number;
    filterBy?: string;
    sortBy?: string;
    groupBy?: string;
    useCache?: boolean;
  } = {}
): Promise<SearchResults> => {
  const {
    page = 1,
    perPage = 10,
    filterBy,
    sortBy,
    groupBy = 'entity_type',
    useCache = true,
  } = options;

  // Create a cache key
  const cacheKey = JSON.stringify({ query, page, perPage, filterBy, sortBy, groupBy });

  // Check cache if enabled
  if (useCache) {
    const cached = searchCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      return cached.results;
    }
  }

  try {
    const searchParameters = {
      q: query,
      query_by: 'title,description,name,company_name,contact_name,skills',
      filter_by: filterBy || '',
      sort_by: sortBy || '_text_match:desc',
      page,
      per_page: perPage,
      group_by: groupBy,
      group_limit: 3, // Number of results per group
    };

    // Perform multi-collection search
    const searchResults = await typesenseClient
      .multiSearch.perform({
        searches: [
          {
            collection: 'jobs,candidates,clients',
            ...searchParameters,
          },
        ],
      });

    // Process and format results
    // Ensure results exist and have the expected structure
    const results = searchResults?.results?.[0] || { found: 0, hits: [], search_time_ms: 0 };
    const formattedResults: SearchResults = {
      found: results.found || 0,
      page,
      search_time_ms: results.search_time_ms || 0,
      request_params: searchParameters,
    };

    // Format grouped results if grouping is enabled
    if (groupBy && results.grouped_hits && Array.isArray(results.grouped_hits)) {
      const groupedHits: GroupedSearchResults = {};

      results.grouped_hits.forEach((group) => {
        try {
          if (group && group.group_key && Array.isArray(group.group_key) && group.group_key.length > 0) {
            const groupName = group.group_key[0];
            const hits = Array.isArray(group.hits)
              ? group.hits
                  .filter(hit => hit && hit.document)
                  .map((hit) => hit.document as SearchResultItem)
              : [];

            groupedHits[groupName] = {
              count: group.found || hits.length,
              hits,
            };
          }
        } catch (err) {
          console.warn('Error processing search group:', err);
        }
      });

      formattedResults.grouped_hits = groupedHits;
    } else if (results.hits && Array.isArray(results.hits)) {
      // Format regular results if no grouping
      formattedResults.hits = results.hits
        .filter(hit => hit && hit.document)
        .map((hit) => hit.document as SearchResultItem);
    }

    // Store in cache
    if (useCache) {
      searchCache.set(cacheKey, {
        timestamp: Date.now(),
        results: formattedResults,
      });
    }

    return formattedResults;
  } catch (error) {
    console.error('Error searching:', error);
    // Return empty results instead of throwing
    return {
      found: 0,
      hits: [],
      page,
      search_time_ms: 0,
      request_params: searchParameters,
    };
  }
};

// Clear the search cache
export const clearSearchCache = () => {
  searchCache.clear();
};

// Search within a specific collection
export const searchCollection = async (
  collectionName: EntityType,
  query: string,
  options: {
    page?: number;
    perPage?: number;
    filterBy?: string;
    sortBy?: string;
    useCache?: boolean;
  } = {}
): Promise<SearchResults> => {
  const {
    page = 1,
    perPage = 10,
    filterBy,
    sortBy,
    useCache = true,
  } = options;

  // Create a cache key
  const cacheKey = JSON.stringify({ collection: collectionName, query, page, perPage, filterBy, sortBy });

  // Check cache if enabled
  if (useCache) {
    const cached = searchCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      return cached.results;
    }
  }

  try {
    const searchParameters = {
      q: query,
      query_by: 'title,description,name,company_name,contact_name,skills',
      filter_by: filterBy || '',
      sort_by: sortBy || '_text_match:desc',
      page,
      per_page: perPage,
    };

    // Perform search on specific collection
    const results = await typesenseClient
      .collections(collectionName)
      .documents()
      .search(searchParameters);

    // Format results - ensure results have expected structure
    const formattedResults: SearchResults = {
      found: results?.found || 0,
      page,
      search_time_ms: results?.search_time_ms || 0,
      request_params: searchParameters,
    };

    if (results?.hits && Array.isArray(results.hits)) {
      formattedResults.hits = results.hits
        .filter(hit => hit && hit.document)
        .map((hit) => hit.document as SearchResultItem);
    } else {
      formattedResults.hits = [];
    }

    // Store in cache
    if (useCache) {
      searchCache.set(cacheKey, {
        timestamp: Date.now(),
        results: formattedResults,
      });
    }

    return formattedResults;
  } catch (error) {
    console.error(`Error searching in ${collectionName}:`, error);
    // Return empty results instead of throwing
    return {
      found: 0,
      hits: [],
      page,
      search_time_ms: 0,
      request_params: searchParameters,
    };
  }
};
