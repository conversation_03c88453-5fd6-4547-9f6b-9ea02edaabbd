import { initializeCollections, indexDocuments } from './indexing';
import { EntityType } from './collection-schemas';

// Sample data for testing
const sampleJobs = [
  {
    id: 'job-1',
    title: 'Frontend Developer',
    description: 'We are looking for a skilled Frontend Developer with React experience.',
    company: 'TechCorp',
    location: 'San Francisco, CA',
    salary_range: '$100,000 - $130,000',
    job_type: 'Full-time',
    status: 'Open',
    skills: ['React', 'TypeScript', 'CSS'],
    created_at: Date.now() - 7 * 24 * 60 * 60 * 1000,
    updated_at: Date.now(),
  },
  {
    id: 'job-2',
    title: 'Backend Engineer',
    description: 'Experienced Backend Engineer needed for our growing team.',
    company: 'DataSystems',
    location: 'Remote',
    salary_range: '$120,000 - $150,000',
    job_type: 'Full-time',
    status: 'Open',
    skills: ['Node.js', 'Python', 'PostgreSQL'],
    created_at: Date.now() - 14 * 24 * 60 * 60 * 1000,
    updated_at: Date.now(),
  },
];

const sampleCandidates = [
  {
    id: 'candidate-1',
    title: 'Senior Developer Profile',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '************',
    location: 'New York, NY',
    skills: ['JavaScript', 'React', 'Node.js'],
    experience_years: 5,
    status: 'Active',
    description: 'Experienced full-stack developer with 5+ years in web development.',
    created_at: Date.now() - 5 * 24 * 60 * 60 * 1000,
    updated_at: Date.now(),
  },
  {
    id: 'candidate-2',
    title: 'UX Designer Profile',
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '************',
    location: 'Chicago, IL',
    skills: ['UI Design', 'Figma', 'User Research'],
    experience_years: 3,
    status: 'Active',
    description: 'Creative UX designer with a focus on user-centered design principles.',
    created_at: Date.now() - 10 * 24 * 60 * 60 * 1000,
    updated_at: Date.now(),
  },
];

const sampleClients = [
  {
    id: 'client-1',
    title: 'TechCorp Inc.',
    company_name: 'TechCorp Inc.',
    contact_name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '************',
    industry: 'Technology',
    location: 'San Francisco, CA',
    description: 'Leading technology company specializing in enterprise software.',
    created_at: Date.now() - 30 * 24 * 60 * 60 * 1000,
    updated_at: Date.now(),
  },
  {
    id: 'client-2',
    title: 'Finance Partners LLC',
    company_name: 'Finance Partners LLC',
    contact_name: 'Michael Brown',
    email: '<EMAIL>',
    phone: '************',
    industry: 'Finance',
    location: 'New York, NY',
    description: 'Financial services firm providing investment and advisory services.',
    created_at: Date.now() - 45 * 24 * 60 * 60 * 1000,
    updated_at: Date.now(),
  },
];

// Function to index sample data
const indexSampleData = async () => {
  try {
    // Index jobs
    await indexDocuments('jobs', sampleJobs);
    if (import.meta.env.DEV) {
      console.log('Indexed sample jobs');
    }

    // Index candidates
    await indexDocuments('candidates', sampleCandidates);
    if (import.meta.env.DEV) {
      console.log('Indexed sample candidates');
    }

    // Index clients
    await indexDocuments('clients', sampleClients);
    if (import.meta.env.DEV) {
      console.log('Indexed sample clients');
    }

    return true;
  } catch (error) {
    console.error('Error indexing sample data:', error);
    return false;
  }
};

// Initialize search
export const initializeSearch = async () => {
  try {
    if (import.meta.env.DEV) {
      console.log('Initializing search...');
    }

    // Initialize collections - don't fail the entire initialization if this fails
    try {
      const collectionsInitialized = await initializeCollections();
      if (!collectionsInitialized) {
        if (import.meta.env.DEV) {
          console.warn('Failed to initialize search collections, but continuing with local search');
        }
      }
    } catch (collectionError) {
      if (import.meta.env.DEV) {
        console.warn('Error initializing collections, but continuing with local search:', collectionError);
      }
      // Don't return false here, continue with initialization
    }

    // Try to index sample data, but don't fail if it doesn't work
    try {
      const dataIndexed = await indexSampleData();
      if (!dataIndexed) {
        if (import.meta.env.DEV) {
          console.warn('Failed to index sample data, but search will still work with existing data');
        }
      }
    } catch (indexError) {
      if (import.meta.env.DEV) {
        console.warn('Error indexing sample data, but search will still work with existing data:', indexError);
      }
      // Don't return false here, continue with initialization
    }

    if (import.meta.env.DEV) {
      console.log('Search initialized (with possible limitations)');
    }
    return true;
  } catch (error) {
    console.error('Error initializing search:', error);
    if (import.meta.env.DEV) {
      console.warn('Search will operate in limited mode');
    }
    // Don't show error toast to users, just log it
    return true; // Return true anyway to not block the application
  }
};

// Function to index a single entity
export const indexEntity = async <T extends Record<string, any>>(
  entityType: EntityType,
  entity: T
) => {
  try {
    await indexDocuments(entityType, [entity]);
    return true;
  } catch (error) {
    console.error(`Error indexing ${entityType}:`, error);
    return false;
  }
};
