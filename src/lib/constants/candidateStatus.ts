/**
 * Candidate Status Management
 *
 * This module provides a centralized definition of candidate statuses
 * for use throughout the application, including both primary and secondary statuses.
 * Updated to match actual CSV import data and remove deprecated references.
 */

/**
 * Primary candidate status enum
 * Represents the main pipeline stages
 */
export enum PrimaryStatus {
  NEW = "new",
  SCREENING = "screening",
  INTERVIEWS = "interview",
  CHALLENGE = "challenge",
  CLIENT_INTERVIEW = "client_interview",
  CLIENT_FEEDBACK = "client_feedback",
  OFFER = "offer",
  HIRED = "hired",
  REJECTED = "rejected"
}

/**
 * Primary status configuration with display labels and order
 */
export const PRIMARY_STATUSES = [
  { value: PrimaryStatus.NEW, label: "New", order: 0 },
  { value: PrimaryStatus.SCREENING, label: "Screening", order: 1 },
  { value: PrimaryStatus.INTERVIEWS, label: "Interviews", order: 2 },
  { value: PrimaryStatus.CHALLENGE, label: "Challenge", order: 3 },
  { value: PrimaryStatus.CLIENT_INTERVIEW, label: "Client Interview", order: 4 },
  { value: PrimaryStatus.CLIENT_FEEDBACK, label: "Client Feedback", order: 5 },
  { value: PrimaryStatus.OFFER, label: "Offer", order: 6 },
  { value: PrimaryStatus.HIRED, label: "Hired", order: 7 },
  { value: PrimaryStatus.REJECTED, label: "Rejected", order: 8 }
];

/**
 * Secondary status options mapped to primary statuses
 * Updated to match actual CSV import data and improve fuzzy matching
 */
/**
 * Secondary status options mapped to primary statuses
 * Based on mappings.csv import table - all values now allowed in database
 */
export const SECONDARY_STATUS_OPTIONS: Record<PrimaryStatus, { value: string, label: string }[]> = {
  [PrimaryStatus.NEW]: [
    { value: "Pending", label: "Pending" },
    { value: "Message sent", label: "Message sent" },
    { value: "No WhatsApp - contact by email", label: "No WhatsApp - contact by email" }
  ],
  [PrimaryStatus.SCREENING]: [
    { value: "Form completed", label: "Form completed" },
    { value: "Appointment to be scheduled", label: "Appointment to be scheduled" },
    { value: "Appointment scheduled", label: "Appointment scheduled" },
    { value: "Interview finalized", label: "Interview finalized" },
    { value: "Did not respond to form", label: "Did not respond to form" }
  ],
  [PrimaryStatus.INTERVIEWS]: [
    { value: "Appointment to be scheduled", label: "Appointment to be scheduled" },
    { value: "Appointment scheduled", label: "Appointment scheduled" },
    { value: "Interview finalized", label: "Interview finalized" },
    { value: "No show", label: "No show" }
  ],
  [PrimaryStatus.CHALLENGE]: [
    { value: "Challenge accepted", label: "Challenge accepted" },
    { value: "Challenge to be sent", label: "Challenge to be sent" },
    { value: "Challenge sent", label: "Challenge sent" },
    { value: "Waiting for challenge", label: "Waiting for challenge" },
    { value: "Challenge received", label: "Challenge received" },
    { value: "Presentation assigned", label: "Presentation assigned" },
    { value: "Presentation Finalized", label: "Presentation Finalized" }
  ],
  [PrimaryStatus.CLIENT_INTERVIEW]: [
    { value: "Resume Sent to Client", label: "Resume Sent to Client" },
    { value: "Appointment to be scheduled", label: "Appointment to be scheduled" },
    { value: "Appointment scheduled", label: "Appointment scheduled" },
    { value: "Interview finalized", label: "Interview finalized" },
    { value: "No show", label: "No show" }
  ],
  [PrimaryStatus.CLIENT_FEEDBACK]: [
    { value: "RTR to be send", label: "RTR to be send" },
    { value: "RTR sent", label: "RTR sent" },
    { value: "RTR received", label: "RTR received" }
  ],
  [PrimaryStatus.OFFER]: [
    { value: "Candidate ready", label: "Candidate ready" },
    { value: "Rejected by incubator", label: "Rejected by incubator" },
    { value: "Accepted by incubator", label: "Accepted by incubator" },
    { value: "Accepted by Customer", label: "Accepted by Customer" },
    { value: "Invitation to the incubator", label: "Invitation to the incubator" }
  ],
  [PrimaryStatus.HIRED]: [
    { value: "Hired", label: "Hired" }
  ],
  [PrimaryStatus.REJECTED]: [
    { value: "Rejected / no rating", label: "Rejected / no rating" },
    { value: "No show", label: "No show" },
    { value: "Did not respond", label: "Did not respond" }
  ]
};

/**
 * Import mapping for legacy values - most values from mappings.csv are now directly supported
 * Only need to map a few legacy values that have slight variations
 */
export const IMPORT_SECONDARY_STATUS_MAPPING: Record<string, string> = {
  // Legacy mappings for slight variations
  "Initial message sent": "Message sent",
  "Form Finalized": "Form completed",
  "Scheduled appointment": "Appointment scheduled",
  "Waiting for challenge / time expired": "Waiting for challenge",
  "Feedback and RTR sent": "RTR sent",
  "Accepted incubator": "Accepted by incubator",
  "Rejected incubator": "Rejected by incubator",
  "I rejected / does not qualify": "Rejected / no rating",
  "No response": "Did not respond"
};

/**
 * Get all secondary status options as a flat array
 */
export const getAllSecondaryStatusOptions = () => {
  return Object.values(SECONDARY_STATUS_OPTIONS).flat();
};

/**
 * Find the primary status that corresponds to a given secondary status
 */
export const getPrimaryStatusFromSecondary = (secondaryStatus: string): PrimaryStatus | null => {
  if (!secondaryStatus) return null;

  const normalizedSecondary = secondaryStatus.trim().toLowerCase();

  for (const [primaryStatus, secondaryOptions] of Object.entries(SECONDARY_STATUS_OPTIONS)) {
    const hasMatch = secondaryOptions.some(option =>
      option.value.toLowerCase() === normalizedSecondary ||
      option.label.toLowerCase() === normalizedSecondary
    );

    if (hasMatch) {
      return primaryStatus as PrimaryStatus;
    }
  }

  return null;
};

/**
 * Get the display label for a primary status
 */
export const getPrimaryStatusLabel = (status: PrimaryStatus): string => {
  const statusConfig = PRIMARY_STATUSES.find(s => s.value === status);
  return statusConfig?.label || status;
};

/**
 * Get the display label for a secondary status
 */
export const getSecondaryStatusLabel = (secondaryStatus: string): string => {
  const allOptions = getAllSecondaryStatusOptions();
  const option = allOptions.find(opt => opt.value === secondaryStatus);
  return option?.label || secondaryStatus;
};


