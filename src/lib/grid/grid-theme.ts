import { themeQuartz, iconSetMaterial } from 'ag-grid-community';

/**
 * Dark theme configuration for AG Grid
 * This theme is used when the .dark class is present on the HTML element
 */
export const darkGridTheme = themeQuartz
  .withPart(iconSetMaterial)
  .withParams({
    accentColor: "#FFFFFF",
    backgroundColor: "#0C0C0D",
    borderColor: "#ffffff00",
    borderRadius: 20,
    browserColorScheme: "dark",
    cellHorizontalPaddingScale: 1,
    chromeBackgroundColor: {
      ref: "backgroundColor"
    },
    columnBorder: false,
    fontSize: 14,
    foregroundColor: "#BBBEC9",
    headerBackgroundColor: "#404040",
    headerFontSize: 14,
    headerFontWeight: 500,
    headerTextColor: "#FFFFFF",
    headerVerticalPaddingScale: 0.9,
    iconSize: 20,
    rowBorder: false,
    rowVerticalPaddingScale: 0.9,
    sidePanelBorder: false,
    spacing: 8,
    wrapperBorder: false,
    wrapperBorderRadius: 0
  });

/**
 * Light theme configuration for AG Grid
 * This theme is used when the .dark class is not present on the HTML element
 */
export const lightGridTheme = themeQuartz
  .withPart(iconSetMaterial)
  .withParams({
    accentColor: "#2563EB", // Primary blue color
    backgroundColor: "#FFFFFF",
    borderColor: "#E5E7EB",
    borderRadius: 20,
    browserColorScheme: "light",
    cellHorizontalPaddingScale: 1,
    chromeBackgroundColor: {
      ref: "backgroundColor"
    },
    columnBorder: false,
    fontSize: 14,
    foregroundColor: "#374151",
    headerBackgroundColor: "#F9FAFB",
    headerFontSize: 14,
    headerFontWeight: 500,
    headerTextColor: "#111827",
    headerVerticalPaddingScale: 0.9,
    iconSize: 20,
    rowBorder: false,
    rowVerticalPaddingScale: 0.9,
    sidePanelBorder: false,
    spacing: 8,
    wrapperBorder: false,
    wrapperBorderRadius: 0
  });

/**
 * Get the current grid theme based on whether dark mode is active
 * @returns The appropriate grid theme based on the current theme mode
 */
export function getCurrentGridTheme() {
  // Check if we're in a browser environment
  if (typeof document === 'undefined') {
    return lightGridTheme; // Default to light theme in non-browser environments
  }
  
  // Check if dark mode is active by looking for the .dark class on the HTML element
  const isDarkMode = document.documentElement.classList.contains('dark');
  
  return isDarkMode ? darkGridTheme : lightGridTheme;
}

/**
 * Hook to get the current grid theme and listen for theme changes
 * @param callback Function to call when the theme changes
 */
export function setupGridThemeListener(callback: () => void) {
  if (typeof window === 'undefined' || typeof MutationObserver === 'undefined') {
    return () => {}; // Return no-op cleanup function in non-browser environments
  }

  // Create a MutationObserver to watch for changes to the classList of the HTML element
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (
        mutation.type === 'attributes' && 
        mutation.attributeName === 'class'
      ) {
        // Theme has changed, call the callback
        callback();
      }
    });
  });

  // Start observing the HTML element for class changes
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
  });

  // Return a cleanup function
  return () => {
    observer.disconnect();
  };
}
