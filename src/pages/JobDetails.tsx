
import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  ArrowLeft, 
  Building, 
  MapPin, 
  Calendar, 
  DollarSign, 
  Clock, 
  Users, 
  FileText, 
  Briefcase,
  Share2
} from "lucide-react";

// Mock job data - would be replaced with real data fetching
const job = {
  id: "1",
  title: "Frontend Developer",
  company: "Tech Solutions Inc.",
  location: "San Francisco, CA",
  type: "Full-time",
  salary: "$120,000 - $150,000",
  posted: "January 15, 2024",
  applicants: 12,
  status: "active",
  description: `
    <p>We are looking for a skilled Frontend Developer to join our team. The ideal candidate should have experience with modern JavaScript frameworks, particularly React.</p>
    
    <h3>Responsibilities:</h3>
    <ul>
      <li>Develop user interface components using React.js</li>
      <li>Implement responsive designs that work across all devices</li>
      <li>Collaborate with backend developers to integrate REST APIs</li>
      <li>Write clean, maintainable, and efficient code</li>
      <li>Optimize applications for maximum performance</li>
    </ul>
    
    <h3>Requirements:</h3>
    <ul>
      <li>3+ years of experience in frontend development</li>
      <li>Strong proficiency in JavaScript and React.js</li>
      <li>Thorough understanding of responsive design principles</li>
      <li>Experience with modern frontend build pipelines and tools</li>
      <li>Familiarity with RESTful APIs and asynchronous request handling</li>
      <li>Understanding of Git for version control</li>
    </ul>
  `,
  candidates: [
    {
      id: "1",
      name: "Jane Cooper",
      email: "<EMAIL>",
      status: "interview",
      appliedDate: "Jan 18, 2024",
      imageUrl: "https://randomuser.me/api/portraits/women/10.jpg"
    },
    {
      id: "2",
      name: "Wade Warren",
      email: "<EMAIL>",
      status: "screening",
      appliedDate: "Jan 17, 2024",
    },
    {
      id: "3",
      name: "Esther Howard",
      email: "<EMAIL>",
      status: "new",
      appliedDate: "Jan 16, 2024",
      imageUrl: "https://randomuser.me/api/portraits/women/22.jpg"
    }
  ]
};

export default function JobDetails() {
  const { id } = useParams();
  const [activeTab, setActiveTab] = useState("details");

  return (
    <DashboardLayout>
      <div className="flex items-center gap-2 mb-6">
        <Button variant="outline" size="icon" asChild>
          <Link to="/jobs">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-2xl font-bold">Job Details</h1>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader className="flex flex-row items-start justify-between">
              <div>
                <CardTitle className="text-xl">{job.title}</CardTitle>
                <div className="flex items-center gap-2 mt-2">
                  <Building className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">{job.company}</span>
                </div>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Share2 className="h-4 w-4 mr-2" />
                  Share
                </Button>
                <Button size="sm">Edit Job</Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4 mb-6">
                <Badge variant="outline" className="flex items-center gap-1">
                  <MapPin className="h-3 w-3" />
                  {job.location}
                </Badge>
                <Badge variant="outline" className="flex items-center gap-1">
                  <Briefcase className="h-3 w-3" />
                  {job.type}
                </Badge>
                <Badge variant="outline" className="flex items-center gap-1">
                  <DollarSign className="h-3 w-3" />
                  {job.salary}
                </Badge>
                <Badge variant="outline" className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  Posted {job.posted}
                </Badge>
              </div>
              
              <Tabs defaultValue="details" value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="w-full justify-start">
                  <TabsTrigger value="details">Details</TabsTrigger>
                  <TabsTrigger value="applicants">
                    Applicants ({job.candidates.length})
                  </TabsTrigger>
                  <TabsTrigger value="matches">Matches</TabsTrigger>
                </TabsList>
                <TabsContent value="details" className="mt-6">
                  <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: job.description }} />
                </TabsContent>
                <TabsContent value="applicants" className="mt-6">
                  <div className="rounded-md border">
                    <div className="hidden md:grid grid-cols-6 gap-4 p-4 border-b bg-muted/50 text-sm font-medium">
                      <div className="col-span-2">Candidate</div>
                      <div className="col-span-1">Applied</div>
                      <div className="col-span-1">Status</div>
                      <div className="col-span-2 text-right">Actions</div>
                    </div>
                    <div className="divide-y">
                      {job.candidates.map(candidate => (
                        <div key={candidate.id} className="grid grid-cols-1 md:grid-cols-6 gap-4 p-4 items-center">
                          <div className="col-span-2 flex items-center gap-3">
                            <Avatar>
                              <AvatarImage src={candidate.imageUrl} />
                              <AvatarFallback>{candidate.name.split(" ").map(n => n[0]).join("")}</AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{candidate.name}</div>
                              <div className="text-sm text-muted-foreground">{candidate.email}</div>
                            </div>
                          </div>
                          <div className="col-span-1 hidden md:block">{candidate.appliedDate}</div>
                          <div className="col-span-1 hidden md:block">
                            <Badge variant="outline" className="capitalize">{candidate.status}</Badge>
                          </div>
                          <div className="col-span-2 flex justify-end gap-2">
                            <Button variant="outline" size="sm" asChild>
                              <Link to={`/candidates/${candidate.id}`}>View Profile</Link>
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </TabsContent>
                <TabsContent value="matches" className="mt-6">
                  <div className="flex flex-col items-center justify-center p-8 text-center">
                    <Users className="h-10 w-10 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium">No Matches Yet</h3>
                    <p className="text-sm text-muted-foreground mt-2 max-w-md">
                      Match this job with qualified candidates from your talent pool to streamline your hiring process.
                    </p>
                    <Button className="mt-4">Find Matches</Button>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
        
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Job Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="text-sm font-medium mb-1">Status</div>
                <Badge className="capitalize">{job.status}</Badge>
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Applicants</span>
                </div>
                <span className="font-medium">{job.applicants}</span>
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Posted Date</span>
                </div>
                <span className="font-medium">{job.posted}</span>
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Time in Pipeline</span>
                </div>
                <span className="font-medium">15 days</span>
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Applications</span>
                </div>
                <span className="font-medium">12 / 50</span>
              </div>
              <div className="pt-4">
                <Button className="w-full">Add Candidate</Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
