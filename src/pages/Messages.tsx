
import { useState } from "react";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Search, Send, Plus, MessageSquare, AtSign, Paperclip } from "lucide-react";
import MessageEnhancer from "@/components/messages/MessageEnhancer";

// Mock conversations data
const conversations = [
  {
    id: "1",
    contact: {
      name: "<PERSON>",
      role: "Frontend Developer Candidate",
      image: "https://randomuser.me/api/portraits/women/10.jpg",
      online: true
    },
    lastMessage: {
      text: "Thank you for considering my application. I'm excited about the opportunity to join your team.",
      time: "10:32 AM",
      unread: true
    }
  },
  {
    id: "2",
    contact: {
      name: "<PERSON>",
      role: "UX Designer Candidate",
      image: "",
      online: false
    },
    lastMessage: {
      text: "I've attached my portfolio as requested. Please let me know if you need any additional information.",
      time: "Yesterday",
      unread: false
    }
  },
  {
    id: "3",
    contact: {
      name: "Esther Howard",
      role: "Product Manager Candidate",
      image: "https://randomuser.me/api/portraits/women/22.jpg",
      online: true
    },
    lastMessage: {
      text: "I'm available for an interview next Tuesday afternoon. Would that work for you?",
      time: "Yesterday",
      unread: false
    }
  },
  {
    id: "4",
    contact: {
      name: "Cameron Williamson",
      role: "Backend Developer Candidate",
      image: "",
      online: false
    },
    lastMessage: {
      text: "I have a question about the technical challenge. Could you provide some clarity on the requirements?",
      time: "Jan 20",
      unread: false
    }
  }
];

// Mock messages for a conversation
const messages = [
  {
    id: "1",
    sender: "them",
    text: "Thank you for considering my application. I'm excited about the opportunity to join your team.",
    time: "10:32 AM",
    read: true
  },
  {
    id: "2",
    sender: "me",
    text: "Hi Jane, thanks for your interest in the Frontend Developer position. We were impressed with your resume and would like to schedule an initial screening call.",
    time: "10:45 AM",
    read: true
  },
  {
    id: "3",
    sender: "them",
    text: "That sounds great! I'm available most afternoons this week.",
    time: "11:02 AM",
    read: true
  },
  {
    id: "4",
    sender: "me",
    text: "Perfect. How about Thursday at 2:00 PM EST? We'll plan for about 30 minutes.",
    time: "11:10 AM",
    read: true
  },
  {
    id: "5",
    sender: "them",
    text: "Thursday at 2:00 PM EST works for me. Should I expect a call or a video meeting?",
    time: "11:15 AM",
    read: true
  },
  {
    id: "6",
    sender: "me",
    text: "It will be a video meeting. I'll send you a calendar invite with the meeting link shortly.",
    time: "11:20 AM",
    read: true
  },
  {
    id: "7",
    sender: "them",
    text: "Great, thank you! I look forward to speaking with you on Thursday.",
    time: "11:25 AM",
    read: false
  }
];

export default function Messages() {
  const [searchQuery, setSearchQuery] = useState("");
  const [newMessage, setNewMessage] = useState("");
  const [activeConversation, setActiveConversation] = useState(conversations[0]);

  const filteredConversations = conversations.filter(conv =>
    conv.contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    conv.contact.role.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      // In a real app, we would send the message to an API
      console.log("Sending message:", newMessage);
      // Reset the input field
      setNewMessage("");
    }
  };

  return (
    <DashboardLayout>
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold">Messages</h1>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          <span>New Message</span>
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 h-[calc(100vh-240px)]">
        {/* Conversations List */}
        <div className="lg:col-span-4 overflow-hidden flex flex-col">
          <Card className="flex-1 flex flex-col h-full">
            <CardHeader className="px-4 py-3 space-y-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search messages..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </CardHeader>
            <CardContent className="p-0 overflow-auto flex-1">
              {filteredConversations.length === 0 ? (
                <div className="flex flex-col items-center justify-center p-8 text-center h-full">
                  <MessageSquare className="h-10 w-10 text-muted-foreground mb-4" />
                  <p className="text-sm text-muted-foreground mt-2">
                    No conversations found matching your search.
                  </p>
                </div>
              ) : (
                <div className="divide-y">
                  {filteredConversations.map(conv => (
                    <div
                      key={conv.id}
                      className={`p-4 hover:bg-muted/50 transition-colors cursor-pointer ${activeConversation.id === conv.id ? 'bg-muted/50' : ''}`}
                      onClick={() => setActiveConversation(conv)}
                    >
                      <div className="flex items-start gap-3">
                        <div className="relative">
                          <Avatar>
                            <AvatarImage src={conv.contact.image} />
                            <AvatarFallback>{conv.contact.name.split(" ").map(n => n[0]).join("")}</AvatarFallback>
                          </Avatar>
                          {conv.contact.online && (
                            <span className="absolute bottom-0 right-0 h-3 w-3 rounded-full bg-green-500 ring-2 ring-background" />
                          )}
                        </div>
                        <div className="flex-1 overflow-hidden">
                          <div className="flex items-center justify-between">
                            <div className="font-medium">{conv.contact.name}</div>
                            <div className="text-xs text-muted-foreground">{conv.lastMessage.time}</div>
                          </div>
                          <div className="text-xs text-muted-foreground truncate mt-1">
                            {conv.contact.role}
                          </div>
                          <div className="text-sm truncate mt-1 flex items-center justify-between">
                            <span>{conv.lastMessage.text}</span>
                            {conv.lastMessage.unread && (
                              <Badge className="ml-2 h-5 w-5 rounded-full p-0 flex items-center justify-center">•</Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Conversation */}
        <div className="lg:col-span-8 overflow-hidden flex flex-col">
          <Card className="flex-1 flex flex-col h-full">
            <CardHeader className="px-6 py-4 border-b">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarImage src={activeConversation.contact.image} />
                    <AvatarFallback>{activeConversation.contact.name.split(" ").map(n => n[0]).join("")}</AvatarFallback>
                  </Avatar>
                  <div>
                    <CardTitle className="text-lg">{activeConversation.contact.name}</CardTitle>
                    <div className="text-sm text-muted-foreground">
                      {activeConversation.contact.role}
                    </div>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button variant="ghost" size="icon">
                    <AtSign className="h-4 w-4" />
                    <span className="sr-only">Mention</span>
                  </Button>
                  <Button variant="ghost" size="icon">
                    <Paperclip className="h-4 w-4" />
                    <span className="sr-only">Attach</span>
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-6 overflow-auto flex-1 flex flex-col-reverse">
              <div className="space-y-4">
                {messages.map(message => (
                  <div key={message.id} className={`flex ${message.sender === 'me' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[80%] px-4 py-2 rounded-lg ${
                      message.sender === 'me'
                        ? 'bg-primary text-primary-foreground ml-12'
                        : 'bg-muted mr-12'
                    }`}>
                      <div className="text-sm">{message.text}</div>
                      <div className={`text-xs mt-1 ${
                        message.sender === 'me'
                          ? 'text-primary-foreground/70'
                          : 'text-muted-foreground'
                      }`}>
                        {message.time}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <div className="p-4 border-t">
              <div className="flex gap-2">
                <Textarea
                  placeholder="Type a message..."
                  className="min-h-10 resize-none"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage();
                    }
                  }}
                />
                <div className="flex gap-1">
                  <MessageEnhancer
                    message={newMessage}
                    onApply={(enhancedMessage) => setNewMessage(enhancedMessage)}
                  />
                  <Button
                    className="shrink-0"
                    onClick={handleSendMessage}
                    disabled={!newMessage.trim()}
                  >
                    <Send className="h-4 w-4" />
                    <span className="sr-only">Send</span>
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
