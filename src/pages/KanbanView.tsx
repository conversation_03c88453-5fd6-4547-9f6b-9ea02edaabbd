import { useState } from "react";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Candidate, CandidateStatus } from "@/types/candidate";
import { Search, Filter, Briefcase, User, Users, Building } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import KanbanColumn from "@/components/kanban/KanbanColumn";
import KanbanCard from "@/components/kanban/KanbanCard";
import { useToast } from "@/hooks/use-toast";
import { CSVUploadExport } from "@/components/common/CSVUploadExport";
import { parseCSV, exportToCSV, mapCSVToLeads, mapCSVToCandidates, Lead } from "@/utils/csvUtils";

// Sample candidates data - would be replaced with real data from API/database
const mockCandidates: Candidate[] = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    position: "Frontend Developer",
    status: "new",
    appliedDate: "Jan 15, 2024",
    imageUrl: "https://randomuser.me/api/portraits/women/10.jpg",
    inTalentPool: false,
    communications: []
  },
  {
    id: "2",
    name: "Wade Warren",
    email: "<EMAIL>",
    position: "UX Designer",
    status: "screening",
    appliedDate: "Jan 14, 2024",
    inTalentPool: false,
    communications: []
  },
  {
    id: "3",
    name: "Esther Howard",
    email: "<EMAIL>",
    position: "Product Manager",
    status: "interview",
    appliedDate: "Jan 10, 2024",
    imageUrl: "https://randomuser.me/api/portraits/women/22.jpg",
    inTalentPool: false,
    communications: []
  },
  {
    id: "4",
    name: "Cameron Williamson",
    email: "<EMAIL>",
    position: "Backend Developer",
    status: "challenge",
    appliedDate: "Jan 8, 2024",
    inTalentPool: false,
    communications: []
  },
  {
    id: "5",
    name: "Brooklyn Simmons",
    email: "<EMAIL>",
    position: "Data Scientist",
    status: "offer",
    appliedDate: "Jan 5, 2024",
    inTalentPool: true,
    communications: []
  },
  {
    id: "6",
    name: "Jenny Wilson",
    email: "<EMAIL>",
    position: "Marketing Specialist",
    status: "hired",
    appliedDate: "Dec 28, 2023",
    inTalentPool: true,
    communications: []
  }
];

// Sample leads/clients data
const mockLeads = [
  {
    id: "l1",
    name: "Acme Corporation",
    contact: "John Smith",
    position: "Looking for developers",
    status: "new",
    date: "Jan 18, 2024",
    image: "https://ui-avatars.com/api/?name=Acme+Corp&background=0D8ABC&color=fff"
  },
  {
    id: "l2",
    name: "Tech Solutions Inc",
    contact: "Mary Johnson",
    position: "Needs UX designers",
    status: "contacted",
    date: "Jan 15, 2024",
    image: "https://ui-avatars.com/api/?name=Tech+Solutions&background=EC4899&color=fff"
  },
  {
    id: "l3",
    name: "Innovate LLC",
    contact: "Robert Brown",
    position: "Multiple openings",
    status: "proposal",
    date: "Jan 10, 2024",
    image: "https://ui-avatars.com/api/?name=Innovate+LLC&background=10B981&color=fff"
  }
];

// Define pipeline stages
const candidateStages: {status: CandidateStatus; label: string}[] = [
  { status: "new", label: "New Applications" },
  { status: "screening", label: "Screening" },
  { status: "interview", label: "Interview" },
  { status: "challenge", label: "Technical Challenge" },
  { status: "offer", label: "Offer" },
  { status: "hired", label: "Hired" },
  { status: "rejected", label: "Rejected" }
];

const leadStages = [
  { status: "new", label: "New Leads" },
  { status: "contacted", label: "Contacted" },
  { status: "meeting", label: "Meeting Scheduled" },
  { status: "proposal", label: "Proposal Sent" },
  { status: "negotiation", label: "Negotiation" },
  { status: "closed", label: "Closed Won" },
  { status: "lost", label: "Closed Lost" }
];

export default function KanbanView() {
  const [candidates, setCandidates] = useState<Candidate[]>(mockCandidates);
  const [leads, setLeads] = useState<Lead[]>(mockLeads);
  const [searchTerm, setSearchTerm] = useState("");
  const { toast } = useToast();
  
  const filteredCandidates = candidates.filter(candidate => 
    candidate.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    candidate.position.toLowerCase().includes(searchTerm.toLowerCase()) ||
    candidate.email.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const filteredLeads = leads.filter(lead => 
    lead.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    lead.contact.toLowerCase().includes(searchTerm.toLowerCase()) ||
    lead.position.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const handleCandidateMove = (candidateId: string, newStatus: CandidateStatus) => {
    setCandidates(prev => 
      prev.map(candidate => 
        candidate.id === candidateId 
          ? { ...candidate, status: newStatus } 
          : candidate
      )
    );
    
    toast({
      title: "Candidate moved",
      description: `Candidate has been moved to ${newStatus} stage`,
    });
  };
  
  const handleLeadMove = (leadId: string, newStatus: string) => {
    setLeads(prev => 
      prev.map(lead => 
        lead.id === leadId 
          ? { ...lead, status: newStatus } 
          : lead
      )
    );
    
    toast({
      title: "Lead moved",
      description: `Lead has been moved to ${newStatus} stage`,
    });
  };

  const handleLeadUpload = async (file: File) => {
    try {
      const csvData = await parseCSV<any>(file);
      const newLeads = mapCSVToLeads(csvData);
      
      setLeads(prev => [...prev, ...newLeads]);
      
      toast({
        title: "Import successful",
        description: `${newLeads.length} leads imported successfully`
      });
    } catch (error) {
      console.error("Error importing leads:", error);
      throw error;
    }
  };

  const handleLeadExport = () => {
    const dataToExport = leads.map(lead => ({
      id: lead.id,
      name: lead.name,
      contact: lead.contact,
      position: lead.position,
      status: lead.status,
      date: lead.date
    }));
    
    exportToCSV(dataToExport, "leads.csv");
    
    toast({
      title: "Export successful",
      description: `${dataToExport.length} leads exported successfully`
    });
  };
  
  return (
    <DashboardLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-4">Pipeline View</h1>
        
        <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
          <div className="relative w-full sm:w-96">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search candidates or leads..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
            <Button size="sm">
              Add New
            </Button>
          </div>
        </div>
      </div>
      
      <Tabs defaultValue="candidates">
        <TabsList>
          <TabsTrigger value="candidates" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Candidates
          </TabsTrigger>
          <TabsTrigger value="leads" className="flex items-center gap-2">
            <Building className="h-4 w-4" />
            Leads & Clients
          </TabsTrigger>
          <TabsTrigger value="matches" className="flex items-center gap-2">
            <Briefcase className="h-4 w-4" />
            Job Matches
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="candidates" className="mt-6">
          <div className="flex justify-end mb-4">
            <CSVUploadExport 
              onUpload={async (file) => {
                const csvData = await parseCSV<any>(file);
                const newCandidates = mapCSVToCandidates(csvData);
                setCandidates(prev => [...prev, ...newCandidates as Candidate[]]);
              }}
              onExport={() => {
                const dataToExport = candidates.map(c => ({
                  id: c.id,
                  name: c.name,
                  email: c.email,
                  position: c.position,
                  status: c.status,
                  appliedDate: c.appliedDate
                }));
                exportToCSV(dataToExport, "kanban-candidates.csv");
              }}
              entityName="Candidates"
            />
          </div>
          
          <div className="flex gap-4 overflow-x-auto pb-6">
            {candidateStages.map(stage => (
              <KanbanColumn 
                key={stage.status} 
                title={stage.label} 
                count={filteredCandidates.filter(c => c.status === stage.status).length}
              >
                {filteredCandidates
                  .filter(candidate => candidate.status === stage.status)
                  .map(candidate => (
                    <KanbanCard 
                      key={candidate.id}
                      id={candidate.id}
                      title={candidate.name}
                      subtitle={candidate.position}
                      imageUrl={candidate.imageUrl}
                      metadata={[
                        { label: "Applied", value: candidate.appliedDate },
                        { label: "Email", value: candidate.email }
                      ]}
                      onMove={(newStatus) => handleCandidateMove(candidate.id, newStatus as CandidateStatus)}
                      moveOptions={candidateStages}
                      linkTo={`/candidates/${candidate.id}`}
                    />
                  ))}
              </KanbanColumn>
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="leads" className="mt-6">
          <div className="flex justify-end mb-4">
            <CSVUploadExport 
              onUpload={handleLeadUpload}
              onExport={handleLeadExport}
              entityName="Leads"
            />
          </div>
          
          <div className="flex gap-4 overflow-x-auto pb-6">
            {leadStages.map(stage => (
              <KanbanColumn 
                key={stage.status} 
                title={stage.label} 
                count={filteredLeads.filter(l => l.status === stage.status).length}
              >
                {filteredLeads
                  .filter(lead => lead.status === stage.status)
                  .map(lead => (
                    <KanbanCard 
                      key={lead.id}
                      id={lead.id}
                      title={lead.name}
                      subtitle={lead.position}
                      imageUrl={lead.image}
                      metadata={[
                        { label: "Contact", value: lead.contact },
                        { label: "Date", value: lead.date }
                      ]}
                      onMove={(newStatus) => handleLeadMove(lead.id, newStatus)}
                      moveOptions={leadStages}
                      linkTo={`/leads/${lead.id}`}
                    />
                  ))}
              </KanbanColumn>
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="matches" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Job Matches</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center justify-center p-8 text-center">
                <User className="h-10 w-10 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium">No Active Matches</h3>
                <p className="text-sm text-muted-foreground mt-2 max-w-md">
                  When you match candidates with job opportunities, they will appear here. Start by adding job openings and matching candidates.
                </p>
                <Button className="mt-4">Create Job Match</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </DashboardLayout>
  );
}
