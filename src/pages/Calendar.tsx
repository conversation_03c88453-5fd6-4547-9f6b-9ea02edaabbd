
import { useState } from "react";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { 
  CalendarDays, 
  Clock, 
  Users, 
  Video,
  MapPin,
  Plus,
  ChevronLeft,
  ChevronRight
} from "lucide-react";

// Mock events data
const events = [
  {
    id: "1",
    title: "Interview with <PERSON>",
    type: "interview",
    date: "2025-04-09",
    time: "10:00 AM - 11:00 AM",
    with: {
      name: "<PERSON>",
      role: "Frontend Developer Candidate",
      image: "https://randomuser.me/api/portraits/women/10.jpg"
    },
    location: "Zoom Meeting",
    description: "First-round technical interview for the Frontend Developer position."
  },
  {
    id: "2",
    title: "Technical Challenge Review",
    type: "review",
    date: "2025-04-09",
    time: "2:00 PM - 3:00 PM",
    with: {
      name: "<PERSON> Warren",
      role: "UX Designer Candidate",
      image: ""
    },
    location: "Conference Room B",
    description: "Review the completed design challenge and discuss implementation details."
  },
  {
    id: "3",
    title: "Team Sync",
    type: "meeting",
    date: "2025-04-10",
    time: "9:30 AM - 10:30 AM",
    with: {
      name: "Recruiting Team",
      role: "Weekly Sync",
      image: ""
    },
    location: "Conference Room A",
    description: "Weekly team meeting to discuss recruiting progress and priorities."
  },
  {
    id: "4",
    title: "Final Interview with Esther Howard",
    type: "interview",
    date: "2025-04-11",
    time: "11:00 AM - 12:00 PM",
    with: {
      name: "Esther Howard",
      role: "Product Manager Candidate",
      image: "https://randomuser.me/api/portraits/women/22.jpg"
    },
    location: "Zoom Meeting",
    description: "Final round interview with the hiring manager."
  }
];

export default function Calendar() {
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [viewMode, setViewMode] = useState<"calendar" | "list">("calendar");
  
  // Filter events for the selected date
  const selectedDateStr = date ? date.toISOString().split('T')[0] : '';
  const filteredEvents = events.filter(event => event.date === selectedDateStr);
  
  return (
    <DashboardLayout>
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold">Calendar</h1>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          <span>Schedule Event</span>
        </Button>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1">
          <Card>
            <CardHeader className="px-4 pt-4 pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Calendar</CardTitle>
                <div className="flex space-x-1">
                  <Button variant="outline" size="icon" onClick={() => setDate(new Date())}>
                    <span className="sr-only">Go to today</span>
                    <CalendarDays className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-2">
              <CalendarComponent
                mode="single"
                selected={date}
                onSelect={setDate}
                className="rounded-md border"
              />
              
              <div className="mt-4 space-y-2 px-2">
                <div className="text-sm font-medium">Event Types</div>
                <div className="flex flex-col gap-2">
                  <div className="flex items-center gap-2">
                    <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100 px-2">
                      Interview
                    </Badge>
                    <span className="text-sm text-muted-foreground">Candidate interviews</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className="bg-green-100 text-green-800 hover:bg-green-100 px-2">
                      Meeting
                    </Badge>
                    <span className="text-sm text-muted-foreground">Team meetings</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-100 px-2">
                      Review
                    </Badge>
                    <span className="text-sm text-muted-foreground">Candidate reviews</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="lg:col-span-2">
          <Card>
            <CardHeader className="px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="icon">
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <div className="font-medium">{date ? date.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' }) : ''}</div>
                  <Button variant="outline" size="icon">
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
                <Tabs defaultValue="calendar" value={viewMode} onValueChange={(val) => setViewMode(val as "calendar" | "list")}>
                  <TabsList>
                    <TabsTrigger value="calendar">Calendar</TabsTrigger>
                    <TabsTrigger value="list">List</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              {filteredEvents.length === 0 ? (
                <div className="flex flex-col items-center justify-center p-12 text-center">
                  <CalendarDays className="h-10 w-10 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No Events Scheduled</h3>
                  <p className="text-sm text-muted-foreground mt-2 max-w-md">
                    There are no events scheduled for this date. Click the "Schedule Event" button to add a new event.
                  </p>
                </div>
              ) : (
                <div className="divide-y">
                  {filteredEvents.map(event => (
                    <div key={event.id} className="p-4 hover:bg-muted/50 transition-colors">
                      <div className="flex items-start gap-4">
                        <div className="hidden md:block">
                          <div className="text-sm font-medium text-center">{event.time.split(' - ')[0]}</div>
                        </div>
                        <div className={`w-1 self-stretch rounded-full ${
                          event.type === 'interview' ? 'bg-blue-500' : 
                          event.type === 'meeting' ? 'bg-green-500' : 
                          'bg-orange-500'
                        }`} />
                        <div className="flex-1">
                          <div className="flex items-start justify-between">
                            <div>
                              <h3 className="font-medium">{event.title}</h3>
                              <div className="text-sm text-muted-foreground mt-1">{event.time}</div>
                            </div>
                            <Badge className="capitalize">{event.type}</Badge>
                          </div>
                          <div className="flex items-center gap-2 mt-2">
                            <div className="flex items-center gap-1 text-sm text-muted-foreground">
                              <Video className="h-3.5 w-3.5" />
                              <span>{event.location}</span>
                            </div>
                          </div>
                          <div className="mt-3 flex items-center gap-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={event.with.image} />
                              <AvatarFallback>{event.with.name.split(" ").map(n => n[0]).join("")}</AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="text-sm font-medium">{event.with.name}</div>
                              <div className="text-xs text-muted-foreground">{event.with.role}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
