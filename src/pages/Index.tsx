
import { useState, useEffect } from "react";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Users,
  Calendar,
  CheckCircle,
  Clock,
  Plus
} from "lucide-react";
import ErrorBoundary from "@/components/common/ErrorBoundary";


import RecentActivity from "@/components/dashboard/RecentActivity";
import CandidateItem from "@/components/dashboard/CandidateItem";
import { CandidateStatus } from "@/types/candidate";

// Sample data - would be replaced with real data from API/database
const candidates = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    position: "Frontend Developer",
    status: "new" as CandidateStatus,
    appliedDate: "Today",
    imageUrl: "https://randomuser.me/api/portraits/women/10.jpg"
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    position: "UX Designer",
    status: "screening" as Candidate<PERSON>tatus,
    appliedDate: "Yesterday",
  },
  {
    id: "3",
    name: "<PERSON>",
    email: "<EMAIL>",
    position: "Product Manager",
    status: "interview" as CandidateStatus,
    appliedDate: "Jan 21",
    imageUrl: "https://randomuser.me/api/portraits/women/22.jpg"
  },
  {
    id: "4",
    name: "Cameron Williamson",
    email: "<EMAIL>",
    position: "Backend Developer",
    status: "offer" as CandidateStatus,
    appliedDate: "Jan 18",
  },
];

const Index = () => {
  const [searchQuery, setSearchQuery] = useState("");



  return (
    <DashboardLayout>
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold">Applicant Tracking System</h1>
        <Button className="gap-2" asChild>
          <a href="/candidates/new">
            <Plus className="h-4 w-4" />
            <span>Add Candidate</span>
          </a>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Total Applicants</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">127</div>
            <p className="text-xs text-muted-foreground">+5 this week</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Interviews Scheduled</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">24</div>
            <p className="text-xs text-muted-foreground">12 this week</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Offers Sent</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">18</div>
            <p className="text-xs text-muted-foreground">+2 this month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Time to Fill</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">21 days</div>
            <p className="text-xs text-muted-foreground">-3 days from last month</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <ErrorBoundary name="Candidates Section">
          <div className="lg:col-span-2">
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle>Recent Applicants</CardTitle>
                  <div className="relative w-full max-w-sm">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search candidates..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <ErrorBoundary name="Candidate Items">
                    {candidates.map((candidate) => (
                      <CandidateItem
                        key={candidate.id}
                        id={candidate.id}
                        name={candidate.name}
                        email={candidate.email}
                        position={candidate.position}
                        status={candidate.status}
                        appliedDate={candidate.appliedDate}
                        imageUrl={candidate.imageUrl}
                      />
                    ))}
                  </ErrorBoundary>
                </div>
                <div className="mt-4 text-center">
                  <Button variant="outline" asChild>
                    <a href="/candidates">View All Candidates</a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </ErrorBoundary>
        <ErrorBoundary name="Recent Activity">
          <div>
            <RecentActivity />
          </div>
        </ErrorBoundary>
      </div>
    </DashboardLayout>
  );
};

export default Index;
