
import { useState, useEffect } from "react";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Candidate } from "@/types/candidate";
import ListView from "@/components/candidates/ListView";
import TableView from "@/components/candidates/TableView";
import KanbanViewWrapper from "@/components/candidates/KanbanViewWrapper";
import { PageHeader } from "@/components/layout/PageHeader";
import { Plus } from "lucide-react";
import { exportToCSV } from "@/utils/csvUtils";
import { useToast } from "@/hooks/use-toast";
import { candidatesApi } from "@/services/apiService";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import SimplifiedCSVImport, { ImportResult } from "@/components/import/SimplifiedCSVImport";

export default function Candidates() {
  const [viewMode, setViewMode] = useState(() =>
    localStorage.getItem("candidatesViewMode") || "list"
  )

  useEffect(() => {
    localStorage.setItem("candidatesViewMode", viewMode)
  }, [viewMode])

  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState<string>("all");
  const { toast } = useToast();

  // Function to load candidates from API
  const loadCandidates = async () => {
    try {
      setIsLoading(true);

      console.log('Fetching candidates from API...');
      console.log('API URL:', import.meta.env.VITE_API_URL);

      // Get candidates from API
      const data = await candidatesApi.getAll();
      console.log('Candidates data received:', data);

      // Transform the data to match the Candidate interface
      const formattedCandidates = data.map((candidate: any) => {
        console.log('Processing candidate:', candidate);
        return {
          // Basic Information
          id: candidate.id,
          name: `${candidate.first_name} ${candidate.last_name || ''}`.trim(),
          email: candidate.email,
          phone: candidate.phone || '',
          location: candidate.location || '',

          // Professional Information
          position: candidate.current_position || '',
          currentCompany: candidate.current_company || '',
          currentPosition: candidate.current_position || '',
          experienceYears: candidate.experience_years ? Number(candidate.experience_years) : null,
          education: candidate.education || '',
          skills: candidate.skills || [],

          // URLs and Links
          resumeUrl: candidate.resume_url || '',
          linkedinUrl: candidate.linkedin_url || '',
          githubUrl: candidate.github_url || '',
          portfolioUrl: candidate.portfolio_url || '',

          // Financial Information
          desiredSalary: candidate.desired_salary ? Number(candidate.desired_salary) : null,
          salaryCurrency: candidate.salary_currency || 'USD',
          availabilityDate: candidate.availability_date || '',

          // Status Information
          status: candidate.status || 'new',
          secondaryStatus: candidate.secondary_status || '',
          source: candidate.source || '',

          // Assessment & Interview Information
          englishLevel: candidate.english_level || '',
          interviewScore: candidate.interview_score ? Number(candidate.interview_score) : null,
          interviewNotes: candidate.interview_notes || '',
          challenge: candidate.challenge || '',
          challengeNotes: candidate.challenge_notes || '',
          challengeFeedback: candidate.challenge_feedback || '',
          driveScore: candidate.drive_score ? Number(candidate.drive_score) : null,
          resilienceScore: candidate.resilience_score ? Number(candidate.resilience_score) : null,
          collaborationScore: candidate.collaboration_score ? Number(candidate.collaboration_score) : null,
          result: candidate.result || '',

          // Process Information
          stargetyId: candidate.stargety_id || '',
          isDuplicate: candidate.is_duplicate || 'new',
          notes: candidate.notes || '',

          // Dates
          appliedDate: candidate.created_at ? new Date(candidate.created_at).toLocaleDateString() : new Date().toLocaleDateString(),
          createdAt: candidate.created_at || '',
          updatedAt: candidate.updated_at || '',

          // Legacy/Additional Fields
          inTalentPool: false,
          isInIncubator: candidate.is_in_incubator || false,
          communications: [],
          strategyId: candidate.stargety_id || '' // Keep for backward compatibility
        };
      });

      console.log('Formatted candidates:', formattedCandidates);
      setCandidates(formattedCandidates);
    } catch (error) {
      console.error('Error loading candidates:', error);
      console.error('Error details:', error);
      toast({
        title: 'Error',
        description: 'Failed to load candidates. Please try again later.',
        variant: 'destructive',
      });
      setCandidates([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Load candidates on component mount
  useEffect(() => {
    loadCandidates();
  }, [toast]);

  const handleCandidateExport = () => {
    const dataToExport = candidates.map(candidate => ({
      id: candidate.id,
      name: candidate.name,
      email: candidate.email,
      phone: candidate.phone || '',
      position: candidate.position,
      status: candidate.status,
      appliedDate: candidate.appliedDate,
      inTalentPool: candidate.inTalentPool.toString()
    }));

    exportToCSV(dataToExport, 'candidates.csv');

    toast({
      title: 'Export successful',
      description: `${dataToExport.length} candidates exported successfully`
    });
  };

  // CSV Import Modal
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);

  const handleCandidateImport = async (_file: File) => {
    // Open the import modal instead of directly processing the file
    setIsImportModalOpen(true);
  };

  const handleImportComplete = async (result: ImportResult) => {
    try {
      console.log(`Import completed: ${result.imported} successful, ${result.skipped} skipped`);

      // Show success message
      toast({
        title: 'Import completed',
        description: `${result.imported} candidates imported successfully. ${result.skipped} records were skipped.`,
        variant: result.imported > 0 ? "default" : "destructive"
      });

      // After import is complete, refresh the candidates list from the API
      await loadCandidates();

      // Close the import modal
      setIsImportModalOpen(false);
    } catch (error) {
      console.error('Error handling import completion:', error);
      toast({
        title: 'Error',
        description: 'Failed to complete import process',
        variant: 'destructive'
      });
    }
  };

  // Filter candidates based on active tab and search query
  const filteredCandidates = candidates.filter(candidate => {
    // First filter by status
    if (activeTab !== 'all' && candidate.status !== activeTab) {
      return false;
    }

    // Then filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        candidate.name.toLowerCase().includes(query) ||
        candidate.email.toLowerCase().includes(query) ||
        candidate.position.toLowerCase().includes(query) ||
        (candidate.phone && candidate.phone.toLowerCase().includes(query))
      );
    }

    return true;
  });

  // Only show search and tabs in list view
  const shouldShowFilters = viewMode === "list";

  return (
    <DashboardLayout>
      <PageHeader
        title="Candidates"
        description="Manage and track all candidate applications"
        searchPlaceholder="Search candidates..."
        searchValue={searchQuery}
        onSearchChange={shouldShowFilters ? setSearchQuery : undefined}
        primaryAction={{
          label: "Add Candidate",
          icon: <Plus className="h-4 w-4" />,
          href: "/candidates/new"
        }}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        showViewToggle={true}
        onExport={handleCandidateExport}
        onImport={handleCandidateImport}
        entityName="Candidates"
        tabs={shouldShowFilters ? [
          { value: 'all', label: 'All' },
          { value: 'new', label: 'New' },
          { value: 'screening', label: 'Screening' },
          { value: 'interview', label: 'Interview' },
          { value: 'challenge', label: 'Challenge' },
          { value: 'client_interview', label: 'Client Interview' },
          { value: 'client_feedback', label: 'Candidate Feedback' },
          { value: 'offer', label: 'Offer' },
          { value: 'hired', label: 'Hired' },
          { value: 'rejected', label: 'Rejected' }
        ] : undefined}
        activeTab={activeTab}
        onTabChange={shouldShowFilters ? setActiveTab : undefined}
      />

      <div className="mt-6">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <p className="text-muted-foreground">Loading candidates...</p>
          </div>
        ) : viewMode === "kanban" ? (
          <KanbanViewWrapper
            candidates={filteredCandidates}
            viewMode={viewMode}
            onViewModeChange={setViewMode}
          />
        ) : viewMode === "table" ? (
          <TableView
            candidates={filteredCandidates}
            viewMode={viewMode}
            onViewModeChange={setViewMode}
            onCandidatesChange={loadCandidates}
          />
        ) : (
          <ListView
            candidates={filteredCandidates}
            viewMode={viewMode}
            onViewModeChange={setViewMode}
          />
        )}
      </div>

      {/* CSV Import Modal */}
      <Dialog open={isImportModalOpen} onOpenChange={setIsImportModalOpen}>
        <DialogContent className="max-w-[95vw] max-h-[95vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Import Candidates from CSV</DialogTitle>
          </DialogHeader>
          <SimplifiedCSVImport
            onImportComplete={handleImportComplete}
            onClose={() => setIsImportModalOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </DashboardLayout>
  );
}
