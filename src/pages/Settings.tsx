
import { useState } from "react";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Card, CardContent } from "@/components/ui/card";
import ProfileSettings from "@/components/settings/ProfileSettings";
import UserSettings from "@/components/settings/UserSettings";
import NotificationSettings from "@/components/settings/NotificationSettings";
import SmtpSettings from "@/components/settings/SmtpSettings";
import PhoneServiceSettings from "@/components/settings/PhoneServiceSettings";
import IntegrationsSettings from "@/components/settings/IntegrationsSettings";
import PlaceholderSettings from "@/components/settings/PlaceholderSettings";
import SettingsMenu from "@/components/settings/SettingsMenu";

export default function Settings() {
  const [activeTab, setActiveTab] = useState("profile");

  return (
    <DashboardLayout>
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Settings</h1>
        <p className="text-muted-foreground mt-2">Manage your account settings and preferences.</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="md:col-span-1">
          <CardContent className="p-4">
            <SettingsMenu activeTab={activeTab} setActiveTab={setActiveTab} />
          </CardContent>
        </Card>

        <div className="md:col-span-3">
          {activeTab === "profile" && <ProfileSettings />}
          {activeTab === "user" && <UserSettings />}
          {activeTab === "preferences" && <UserSettings />} {/* Redirigir a UserSettings */}
          {activeTab === "notifications" && <NotificationSettings />}
          {activeTab === "smtp" && <SmtpSettings />}
          {activeTab === "phone" && <PhoneServiceSettings />}
          {activeTab === "integrations" && <IntegrationsSettings />}

          {/* Placeholder tabs */}
          {activeTab === "emails" && <PlaceholderSettings title="Email Templates" />}
          {activeTab === "security" && <PlaceholderSettings title="Security" />}
          {activeTab === "company" && <PlaceholderSettings title="Company" />}
          {activeTab === "team" && <PlaceholderSettings title="Team Members" />}
        </div>
      </div>
    </DashboardLayout>
  );
}
