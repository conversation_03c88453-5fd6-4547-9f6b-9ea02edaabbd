import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import DashboardLayout from '@/components/layout/DashboardLayout';
import CandidateFormComponent from '@/components/candidates/CandidateForm';
import { Candidate } from '@/types/candidate';
import candidateService from '@/services/candidateService';

export default function CandidateForm() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [candidate, setCandidate] = useState<Candidate | undefined>(undefined);
  const [isLoadingCandidate, setIsLoadingCandidate] = useState<boolean>(false);

  const isEditMode = !!id;
  const [isSaving, setIsSaving] = useState(false);

  // Load candidate data for editing
  useEffect(() => {
    if (isEditMode && id) {
      setIsLoadingCandidate(true);

      // Load candidate data from PostgreSQL
      candidateService.getCandidate(id)
        .then(candidateData => {
          if (candidateData) {
            setCandidate(candidateData);
          } else {
            toast({
              title: "Candidate not found",
              description: "The requested candidate could not be found.",
              variant: "destructive"
            });
            navigate('/candidates');
          }
        })
        .catch(error => {
          console.error('Error loading candidate:', error);
          toast({
            title: "Error",
            description: "Failed to load candidate data. Please try again.",
            variant: "destructive"
          });
        })
        .finally(() => {
          setIsLoadingCandidate(false);
        });
    }
  }, [id, isEditMode, navigate, toast]);

  // Handle form submission
  const handleSubmit = async (data: any) => {
    try {
      setIsSaving(true);

      // Prepare candidate data
      const candidateData = {
        firstName: data.firstName,
        lastName: data.lastName,
        name: `${data.firstName} ${data.lastName}`,
        email: data.email,
        phone: data.phone,
        location: data.location,
        position: data.position,
        status: data.status,
        portfolio: data.portfolio,
        englishLevel: data.englishLevel,
        notes: data.notes,
        socialLinks: data.socialLinks
      };

      if (isEditMode && id) {
        // Update existing candidate
        await candidateService.updateCandidate(id, candidateData);

        toast({
          title: "Success",
          description: "Candidate updated successfully",
        });
      } else {
        // Create new candidate
        await candidateService.createCandidate(candidateData);

        toast({
          title: "Success",
          description: "Candidate created successfully",
        });
      }

      // Navigate back to candidates list
      navigate('/candidates');
    } catch (error) {
      console.error('Error saving candidate:', error);
      toast({
        title: "Error",
        description: "Failed to save candidate. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="flex items-center gap-2 mb-6">
        <Button variant="outline" size="icon" onClick={() => navigate(-1)}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold">
          {isEditMode ? 'Edit Candidate' : 'Add New Candidate'}
        </h1>
      </div>

      <CandidateFormComponent
        candidate={candidate}
        onSubmit={handleSubmit}
        isLoading={isLoadingCandidate || isSaving}
      />
    </DashboardLayout>
  );
}
