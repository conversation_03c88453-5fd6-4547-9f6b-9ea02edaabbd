import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Plus, AlertTriangle, Database, RefreshCw } from 'lucide-react';
import ClientList from '@/components/clients/ClientList';
import { ClientDataAlert } from '@/components/clients/ClientDataAlert';
import { useClients } from '@/hooks/useClients';
import { useDatabaseStatus } from '@/hooks/useDatabaseStatus';
import { PageHeader } from '@/components/layout/PageHeader';
import { exportToCSV } from '@/utils/csvUtils';
import { useToast } from '@/hooks/use-toast';

export default function Clients() {
  const { clients, isLoading, refresh, error } = useClients();
  const { isConnected, isChecking, databaseType, connectionStatus } = useDatabaseStatus();
  const [searchQuery, setSearchQuery] = useState('');
  const { toast } = useToast();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showDataAlert, setShowDataAlert] = useState(false);
  const [dataAlertType, setDataAlertType] = useState<'stale-data' | 'sync-error' | 'not-found'>('stale-data');

  // Log para verificar la conexión con la base de datos
  if (import.meta.env.DEV) {
    console.log(`${databaseType} connection status:`, { isConnected, isChecking });
    console.log(`Loaded ${clients.length} clients from ${databaseType}`);
  }

  // Refrescar datos cuando se monte el componente
  useEffect(() => {
    refresh();
  }, [refresh]);

  // Show alert if there are errors
  useEffect(() => {
    if (error) {
      setDataAlertType('sync-error');
      setShowDataAlert(true);
    }
  }, [error]);

  const handleClientNotFound = (clientId: string) => {
    setDataAlertType('not-found');
    setShowDataAlert(true);
    toast({
      title: 'Client Not Found',
      description: `Client ${clientId} could not be found. It may have been deleted.`,
      variant: 'destructive'
    });
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refresh();
      toast({
        title: 'Refresh successful',
        description: 'Client data has been refreshed'
      });
    } catch (error) {
      toast({
        title: 'Refresh failed',
        description: 'Failed to refresh client data',
        variant: 'destructive'
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleClientExport = () => {
    const dataToExport = clients.map(client => ({
      id: client.id,
      company_name: client.company_name,
      contact_name: client.contact_name,
      email: client.email,
      phone: client.phone || '',
      industry: client.industry || '',
      location: client.location || '',
      status: client.status
    }));

    exportToCSV(dataToExport, 'clients.csv');

    toast({
      title: 'Export successful',
      description: `${dataToExport.length} clients exported successfully`
    });
  };

  return (
    <DashboardLayout>
      <PageHeader
        title="Clients"
        description="Manage your client relationships"
        searchPlaceholder="Search clients..."
        searchValue={searchQuery}
        onSearchChange={setSearchQuery}
        primaryAction={{
          label: "Add Client",
          icon: <Plus className="h-4 w-4" />,
          href: "/clients/new"
        }}
        onExport={handleClientExport}
        entityName="Clients"
        onRefresh={handleRefresh}
        isRefreshing={isRefreshing}
      />

      <div className="space-y-4 mt-6">
        {showDataAlert && (
          <ClientDataAlert
            type={dataAlertType}
            onRefresh={handleRefresh}
            onDismiss={() => setShowDataAlert(false)}
          />
        )}

        <ClientList
          clients={clients}
          isLoading={isLoading || isRefreshing}
          onClientNotFound={handleClientNotFound}
        />
      </div>
    </DashboardLayout>
  );
}
