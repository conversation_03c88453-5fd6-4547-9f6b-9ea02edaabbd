.theme-stargety {
  --background: #faf9f5;
  --foreground: #3d3929;
  --card: #ffffff;
  --card-foreground: #141413;
  --popover: #ffffff;
  --popover-foreground: #28261b;
  --primary: #8caf3c;
  --primary-foreground: #ffffff;
  --secondary: #e8e8e8;
  --secondary-foreground: #4f4f4f;
  --muted: #dedede;
  --muted-foreground: #83827d;
  --accent: #d9d9d9;
  --accent-foreground: #28261b;
  --destructive: #141413;
  --destructive-foreground: #ffffff;
  --border: #d6d6d6;
  --input: #b3b3b3;
  --ring: #b2dd4c;
  --chart-1: #b05730;
  --chart-2: #9c87f5;
  --chart-3: #ded8c4;
  --chart-4: #dbd3f0;
  --chart-5: #b4552d;
  --sidebar: #f2f2f2;
  --sidebar-foreground: #3d3d3a;
  --sidebar-primary: #c96442;
  --sidebar-primary-foreground: #fbfbfb;
  --sidebar-accent: #dedede;
  --sidebar-accent-foreground: #343434;
  --sidebar-border: #ebebeb;
  --sidebar-ring: #b5b5b5;
  --font-sans: Montserrat, sans-serif;
  --font-serif: Playfair Display, serif;
  --font-mono: Fira Code, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0.025em;
}

.theme-stargety.dark {
  --background: #292929;
  --foreground: #bfbfbf;
  --card: #242424;
  --card-foreground: #faf9f5;
  --popover: #30302e;
  --popover-foreground: #dbdbdb;
  --primary: #91b738;
  --primary-foreground: #ffffff;
  --secondary: #faf9f5;
  --secondary-foreground: #30302e;
  --muted: #1c1c1c;
  --muted-foreground: #bdbdbd;
  --accent: #1f1f1f;
  --accent-foreground: #f5f4ee;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #404040;
  --input: #545454;
  --ring: #b2dd4c;
  --chart-1: #b2dd4c;
  --chart-2: #9c87f5;
  --chart-3: #1a1915;
  --chart-4: #2f2b48;
  --chart-5: #b2dd4c;
  --sidebar: #1c1c1c;
  --sidebar-foreground: #c7c7c7;
  --sidebar-primary: #343434;
  --sidebar-primary-foreground: #fbfbfb;
  --sidebar-accent: #0f0f0e;
  --sidebar-accent-foreground: #c3c0b6;
  --sidebar-border: #ebebeb;
  --sidebar-ring: #b5b5b5;
  --font-sans: Montserrat, sans-serif;
  --font-serif: Playfair Display, serif;
  --font-mono: Fira Code, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}



body {
  letter-spacing: var(--tracking-normal);
}