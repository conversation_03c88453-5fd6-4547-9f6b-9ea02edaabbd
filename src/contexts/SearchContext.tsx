import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { initializeSearch } from '@/lib/search/search-init';

interface SearchContextType {
  isSearchInitialized: boolean;
  isSearchInitializing: boolean;
  searchError: Error | null;
}

const SearchContext = createContext<SearchContextType>({
  isSearchInitialized: false,
  isSearchInitializing: false,
  searchError: null,
});

export const useSearchContext = () => useContext(SearchContext);

interface SearchProviderProps {
  children: ReactNode;
}

export const SearchProvider = ({ children }: SearchProviderProps) => {
  const [isSearchInitialized, setIsSearchInitialized] = useState(false);
  const [isSearchInitializing, setIsSearchInitializing] = useState(false);
  const [searchError, setSearchError] = useState<Error | null>(null);

  useEffect(() => {
    const initialize = async () => {
      try {
        setIsSearchInitializing(true);
        const success = await initializeSearch();
        setIsSearchInitialized(success);
      } catch (error) {
        console.error('Error initializing search:', error);
        setSearchError(error instanceof Error ? error : new Error('Failed to initialize search'));
      } finally {
        setIsSearchInitializing(false);
      }
    };

    initialize();
  }, []);

  return (
    <SearchContext.Provider
      value={{
        isSearchInitialized,
        isSearchInitializing,
        searchError,
      }}
    >
      {children}
    </SearchContext.Provider>
  );
};
