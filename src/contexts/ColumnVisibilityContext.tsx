import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { ColDef } from 'ag-grid-community';

// Types for column visibility management
export interface ColumnVisibilityState {
  [columnId: string]: boolean;
}

export interface ColumnInfo {
  field: string;
  headerName: string;
  visible: boolean;
  pinned?: 'left' | 'right' | null;
  suppressColumnsToolPanel?: boolean;
}

interface ColumnVisibilityContextType {
  columnVisibility: ColumnVisibilityState;
  setColumnVisibility: (columnId: string, visible: boolean) => void;
  toggleColumnVisibility: (columnId: string) => void;
  resetToDefaults: () => void;
  getVisibleColumns: (columns: ColDef[]) => ColumnInfo[];
  saveColumnState: (tableId: string, state: ColumnVisibilityState) => void;
  loadColumnState: (tableId: string) => ColumnVisibilityState;
}

const ColumnVisibilityContext = createContext<ColumnVisibilityContextType | undefined>(undefined);

// LocalStorage utilities
const STORAGE_PREFIX = 'columnVisibility_';

const getStorageKey = (tableId: string): string => `${STORAGE_PREFIX}${tableId}`;

const getStorageItem = <T,>(key: string, defaultValue: T): T => {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.error(`Error reading ${key} from localStorage:`, error);
    return defaultValue;
  }
};

const setStorageItem = (key: string, value: any): boolean => {
  try {
    localStorage.setItem(key, JSON.stringify(value));
    return true;
  } catch (error) {
    console.error(`Error saving ${key} to localStorage:`, error);
    return false;
  }
};

// Default column visibility state
const getDefaultColumnVisibility = (columns: ColDef[]): ColumnVisibilityState => {
  const defaultState: ColumnVisibilityState = {};
  columns.forEach(col => {
    if (col.field) {
      // Default to visible unless explicitly hidden
      defaultState[col.field] = col.hide !== true;
    }
  });
  return defaultState;
};

export const useColumnVisibility = () => {
  const context = useContext(ColumnVisibilityContext);
  if (!context) {
    throw new Error('useColumnVisibility must be used within a ColumnVisibilityProvider');
  }
  return context;
};

interface ColumnVisibilityProviderProps {
  children: React.ReactNode;
  tableId: string;
  columns: ColDef[];
}

export const ColumnVisibilityProvider: React.FC<ColumnVisibilityProviderProps> = ({
  children,
  tableId,
  columns
}) => {
  // Initialize state from localStorage or defaults
  const [columnVisibility, setColumnVisibilityState] = useState<ColumnVisibilityState>(() => {
    const savedState = getStorageItem(getStorageKey(tableId), {});
    const defaultState = getDefaultColumnVisibility(columns);
    return { ...defaultState, ...savedState };
  });

  // Update column visibility for a specific column
  const setColumnVisibility = useCallback((columnId: string, visible: boolean) => {
    setColumnVisibilityState(prev => {
      const newState = { ...prev, [columnId]: visible };
      setStorageItem(getStorageKey(tableId), newState);
      return newState;
    });
  }, [tableId]);

  // Toggle column visibility
  const toggleColumnVisibility = useCallback((columnId: string) => {
    setColumnVisibilityState(prev => {
      const newState = { ...prev, [columnId]: !prev[columnId] };
      setStorageItem(getStorageKey(tableId), newState);
      return newState;
    });
  }, [tableId]);

  // Reset to default visibility
  const resetToDefaults = useCallback(() => {
    const defaultState = getDefaultColumnVisibility(columns);
    setColumnVisibilityState(defaultState);
    setStorageItem(getStorageKey(tableId), defaultState);
  }, [columns, tableId]);

  // Get visible columns with metadata
  const getVisibleColumns = useCallback((cols: ColDef[]): ColumnInfo[] => {
    return cols
      .filter(col => col.field && !col.suppressColumnsToolPanel)
      .map(col => ({
        field: col.field!,
        headerName: col.headerName || col.field!,
        visible: columnVisibility[col.field!] ?? true,
        pinned: col.pinned || null,
        suppressColumnsToolPanel: col.suppressColumnsToolPanel
      }));
  }, [columnVisibility]);

  // Save column state to localStorage
  const saveColumnState = useCallback((tblId: string, state: ColumnVisibilityState) => {
    setStorageItem(getStorageKey(tblId), state);
  }, []);

  // Load column state from localStorage
  const loadColumnState = useCallback((tblId: string): ColumnVisibilityState => {
    return getStorageItem(getStorageKey(tblId), {});
  }, []);

  // Update state when columns change
  useEffect(() => {
    const savedState = getStorageItem(getStorageKey(tableId), {});
    const defaultState = getDefaultColumnVisibility(columns);
    const mergedState = { ...defaultState, ...savedState };
    setColumnVisibilityState(mergedState);
  }, [columns, tableId]);

  const contextValue = React.useMemo(() => ({
    columnVisibility,
    setColumnVisibility,
    toggleColumnVisibility,
    resetToDefaults,
    getVisibleColumns,
    saveColumnState,
    loadColumnState,
  }), [
    columnVisibility,
    setColumnVisibility,
    toggleColumnVisibility,
    resetToDefaults,
    getVisibleColumns,
    saveColumnState,
    loadColumnState,
  ]);

  return (
    <ColumnVisibilityContext.Provider value={contextValue}>
      {children}
    </ColumnVisibilityContext.Provider>
  );
};
