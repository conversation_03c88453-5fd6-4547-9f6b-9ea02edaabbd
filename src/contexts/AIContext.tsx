import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Define the AI model types
export type AIModelType = 'openai' | 'anthropic' | 'gemini';

// Define the AI settings interface
interface AISettings {
  enabled: boolean;
  model: AIModelType;
  apiKey: string;
  rateLimited: boolean;
}

// Define the AI context interface
interface AIContextType {
  settings: AISettings;
  updateSettings: (newSettings: Partial<AISettings>) => void;
  resetSettings: () => void;
  enhanceMessage: (message: string) => Promise<string>;
  isEnhancing: boolean;
}

// Default AI settings
const defaultAISettings: AISettings = {
  enabled: true,
  model: 'openai',
  apiKey: '',
  rateLimited: true,
};

// Create the AI context
const AIContext = createContext<AIContextType>({
  settings: defaultAISettings,
  updateSettings: () => {},
  resetSettings: () => {},
  enhanceMessage: async () => '',
  isEnhancing: false,
});

// Hook to use the AI context
export const useAI = () => useContext(AIContext);

// AI provider component
interface AIProviderProps {
  children: ReactNode;
}

export const AIProvider = ({ children }: AIProviderProps) => {
  // Load settings from localStorage or use defaults
  const [settings, setSettings] = useState<AISettings>(() => {
    const savedSettings = localStorage.getItem('ai-settings');
    return savedSettings ? JSON.parse(savedSettings) : defaultAISettings;
  });

  const [isEnhancing, setIsEnhancing] = useState(false);

  // Save settings to localStorage when they change
  useEffect(() => {
    localStorage.setItem('ai-settings', JSON.stringify(settings));
  }, [settings]);

  // Update settings
  const updateSettings = (newSettings: Partial<AISettings>) => {
    setSettings((prev) => ({ ...prev, ...newSettings }));
  };

  // Reset settings to defaults
  const resetSettings = () => {
    setSettings(defaultAISettings);
  };

  // Enhance a message using the selected AI model
  const enhanceMessage = async (message: string): Promise<string> => {
    if (!settings.enabled || !settings.apiKey) {
      return message;
    }

    setIsEnhancing(true);

    try {
      // In a real implementation, this would call a server-side API
      // to avoid exposing API keys client-side
      
      // Simulate API call with a delay
      await new Promise((resolve) => setTimeout(resolve, 1000));
      
      // For demo purposes, just add some enhancements to the message
      let enhancedMessage = message;
      
      switch (settings.model) {
        case 'openai':
          enhancedMessage = `${message}\n\nI'm looking forward to discussing this further with you. Please let me know if you have any questions.`;
          break;
        case 'anthropic':
          enhancedMessage = `${message}\n\nI appreciate your time and consideration. I'm excited about the opportunity to connect.`;
          break;
        case 'gemini':
          enhancedMessage = `${message}\n\nThank you for your attention to this matter. I'm available to provide any additional information you might need.`;
          break;
        default:
          enhancedMessage = message;
      }
      
      return enhancedMessage;
    } catch (error) {
      console.error('Error enhancing message:', error);
      return message;
    } finally {
      setIsEnhancing(false);
    }
  };

  return (
    <AIContext.Provider
      value={{
        settings,
        updateSettings,
        resetSettings,
        enhanceMessage,
        isEnhancing,
      }}
    >
      {children}
    </AIContext.Provider>
  );
};
