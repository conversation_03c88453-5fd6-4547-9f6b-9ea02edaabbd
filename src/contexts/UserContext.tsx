import React, { createContext, useContext, useState, ReactNode } from 'react';

/**
 * User context for managing user profile and preferences
 * This context provides access to the user's profile and preferences
 * and allows updating user preferences
 */

/**
 * User preferences interface
 * Defines the structure of user preferences including theme, language, notifications, and accessibility settings
 */
export interface UserPreferences {
  theme: string; // El tema seleccionado (theme-default, theme-tangerine, etc.)
  mode: string;  // El modo seleccionado (light o dark)
  language?: string;
  notifications?: {
    email: boolean;
    browser: boolean;
    mobile?: boolean;
  };
  accessibility?: {
    highContrast: boolean;
    largeText: boolean;
    reducedMotion: boolean;
  };
}

/**
 * User profile interface
 * Defines the structure of a user profile including personal information and preferences
 */
export interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: string;
  avatar?: string;
  preferences: UserPreferences;
}

/**
 * User context type
 * Defines the shape of the user context including the user profile and methods to update it
 */
interface UserContextType {
  user: UserProfile;
  updateUserPreferences: (preferences: Partial<UserPreferences>) => void;
}

/**
 * User context
 * React context for storing and accessing user information throughout the application
 */
const UserContext = createContext<UserContextType | undefined>(undefined);

/**
 * Mock user data
 * Used as a fallback when no user data is available in localStorage
 */
const mockUser: UserProfile = {
  id: '00000000-0000-0000-0000-000000000001',
  name: 'Jane Doe',
  email: '<EMAIL>',
  role: 'Recruiting Manager',
  avatar: 'https://randomuser.me/api/portraits/women/42.jpg',
  preferences: {
    theme: 'theme-default', // Tema por defecto
    mode: 'light',          // Modo por defecto
    language: 'en',
    notifications: {
      email: true,
      browser: true,
      mobile: false,
    },
    accessibility: {
      highContrast: false,
      largeText: false,
      reducedMotion: false,
    },
  },
};

/**
 * Custom hook for accessing the user context
 * @returns The user context containing the user profile and methods to update it
 * @throws Error if used outside of a UserProvider
 */
export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

/**
 * Props for the UserProvider component
 */
interface UserProviderProps {
  children: ReactNode;
}

/**
 * UserProvider component
 * Provides the UserContext to its children and manages user state
 * @param props - The component props
 */
export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {

  /**
   * Initialize user state from localStorage or use mock data
   */
  const [user, setUser] = useState<UserProfile>(() => {
    const savedUser = localStorage.getItem('user');
    return savedUser ? JSON.parse(savedUser) : mockUser;
  });

  // Ya no necesitamos este efecto porque guardamos directamente en updateUserPreferences

  // Ya no necesitamos sincronizar el tema y el modo desde UserContext
  // Next-themes se encargará de manejar esto a través de themePreferences en localStorage

  /**
   * Updates the user preferences
   * @param preferences - The partial preferences to update
   */
  const updateUserPreferences = (preferences: Partial<UserPreferences>): void => {
    // Actualizar el estado del usuario
    setUser(prevUser => {
      // Filtrar las propiedades theme y mode ya que ahora las maneja next-themes
      const { theme, mode, ...otherPreferences } = preferences;

      const updatedUser = {
        ...prevUser,
        preferences: {
          ...prevUser.preferences,
          ...otherPreferences, // Solo actualizamos otras preferencias, no theme/mode
        },
      };

      // Guardar inmediatamente en localStorage para asegurar persistencia
      try {
        localStorage.setItem('user', JSON.stringify(updatedUser));
        console.log('User preferences updated and saved to localStorage:', updatedUser.preferences);
      } catch (error) {
        console.error('Error saving user preferences to localStorage:', error);
      }

      return updatedUser;
    });
  };

  return (
    <UserContext.Provider value={{ user, updateUserPreferences }}>
      {children}
    </UserContext.Provider>
  );
};
