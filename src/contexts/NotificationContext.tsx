import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Notification } from '@/types/notification';

/**
 * Notification context for managing notifications throughout the application
 * This context provides access to notifications and methods to manipulate them
 */

/**
 * Notification context type
 * Defines the shape of the notification context including notifications and methods to manipulate them
 */
interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  archiveNotification: (id: string) => void;
  deleteNotification: (id: string) => void;
  bulkMarkAsRead: (ids: string[]) => void;
  bulkArchive: (ids: string[]) => void;
  bulkDelete: (ids: string[]) => void;
}

/**
 * Notification context
 * React context for storing and accessing notifications throughout the application
 */
const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

/**
 * Custom hook for accessing the notification context
 * @returns The notification context containing notifications and methods to manipulate them
 * @throws Error if used outside of a NotificationProvider
 */
export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

/**
 * Props for the NotificationProvider component
 */
interface NotificationProviderProps {
  children: ReactNode;
}

/**
 * NotificationProvider component
 * Provides the NotificationContext to its children and manages notification state
 * @param props - The component props
 */
export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);

  // Initialize with empty notifications - in production this would load from API
  useEffect(() => {
    setNotifications([]);
    updateUnreadCount([]);
  }, []);

  /**
   * Updates the unread count based on the current notifications
   * @param notifs - The notifications to count unread from
   */
  const updateUnreadCount = (notifs: Notification[]): void => {
    setUnreadCount(notifs.filter(n => !n.read).length);
  };

  /**
   * Marks a single notification as read
   * @param id - The ID of the notification to mark as read
   */
  const markAsRead = (id: string): void => {
    const updatedNotifications = notifications.map(notification =>
      notification.id === id ? { ...notification, read: true } : notification
    );
    setNotifications(updatedNotifications);
    updateUnreadCount(updatedNotifications);
  };

  /**
   * Marks all notifications as read
   */
  const markAllAsRead = (): void => {
    const updatedNotifications = notifications.map(notification => ({
      ...notification,
      read: true
    }));
    setNotifications(updatedNotifications);
    updateUnreadCount(updatedNotifications);
  };

  /**
   * Archives a notification
   * @param id - The ID of the notification to archive
   */
  const archiveNotification = (id: string): void => {
    const updatedNotifications = notifications.map(notification =>
      notification.id === id ? { ...notification, archived: true } : notification
    );
    setNotifications(updatedNotifications);
    updateUnreadCount(updatedNotifications);
  };

  /**
   * Deletes a notification
   * @param id - The ID of the notification to delete
   */
  const deleteNotification = (id: string): void => {
    const updatedNotifications = notifications.filter(notification => notification.id !== id);
    setNotifications(updatedNotifications);
    updateUnreadCount(updatedNotifications);
  };

  /**
   * Marks multiple notifications as read
   * @param ids - The IDs of the notifications to mark as read
   */
  const bulkMarkAsRead = (ids: string[]): void => {
    const updatedNotifications = notifications.map(notification =>
      ids.includes(notification.id) ? { ...notification, read: true } : notification
    );
    setNotifications(updatedNotifications);
    updateUnreadCount(updatedNotifications);
  };

  /**
   * Archives multiple notifications
   * @param ids - The IDs of the notifications to archive
   */
  const bulkArchive = (ids: string[]): void => {
    const updatedNotifications = notifications.map(notification =>
      ids.includes(notification.id) ? { ...notification, archived: true } : notification
    );
    setNotifications(updatedNotifications);
    updateUnreadCount(updatedNotifications);
  };

  /**
   * Deletes multiple notifications
   * @param ids - The IDs of the notifications to delete
   */
  const bulkDelete = (ids: string[]): void => {
    const updatedNotifications = notifications.filter(notification => !ids.includes(notification.id));
    setNotifications(updatedNotifications);
    updateUnreadCount(updatedNotifications);
  };

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        markAsRead,
        markAllAsRead,
        archiveNotification,
        deleteNotification,
        bulkMarkAsRead,
        bulkArchive,
        bulkDelete
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};
