import React, { createContext, useContext, useState, useEffect } from 'react';

// Define theme types
export type ThemeMode = 'light' | 'dark';
export type ThemeStyle = 'theme-default' | 'theme-cosmic' | 'theme-modern' | 'theme-stargety' | 'theme-tangerine';

// Default values
const DEFAULT_THEME_MODE: ThemeMode = 'light';
const DEFAULT_THEME_STYLE: ThemeStyle = 'theme-default';

// LocalStorage keys
const STORAGE_KEY_MODE = 'themeMode';
const STORAGE_KEY_STYLE = 'themeStyle';

// Helper functions for localStorage
const getStorageItem = <T extends string>(key: string, defaultValue: T): T => {
  try {
    const item = localStorage.getItem(key);
    return (item as T) || defaultValue;
  } catch (error) {
    console.error(`Error reading ${key} from localStorage:`, error);
    return defaultValue;
  }
};

const setStorageItem = (key: string, value: string): boolean => {
  try {
    localStorage.setItem(key, value);
    const savedValue = localStorage.getItem(key);
    if (savedValue !== value) {
      console.warn(`Failed to save ${key} to localStorage. Expected: ${value}, Got: ${savedValue}`);
      return false;
    }
    return true;
  } catch (error) {
    console.error(`Error saving ${key} to localStorage:`, error);
    return false;
  }
};

// Context type definition
interface ThemeContextType {
  themeMode: ThemeMode;
  themeStyle: ThemeStyle;
  setThemeMode: (mode: ThemeMode) => void;
  setThemeStyle: (style: ThemeStyle) => void;
  setTheme: (style: ThemeStyle, mode: ThemeMode) => void;
  toggleThemeMode: () => void;
}

// Create context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Add script to document head to prevent theme flicker
const addThemeInitScript = () => {
  if (typeof document !== 'undefined' && !document.getElementById('theme-init-script')) {
    const script = document.createElement('script');
    script.id = 'theme-init-script';
    script.innerHTML = `
      (function() {
        try {
          var mode = localStorage.getItem('${STORAGE_KEY_MODE}') || '${DEFAULT_THEME_MODE}';
          var style = localStorage.getItem('${STORAGE_KEY_STYLE}') || '${DEFAULT_THEME_STYLE}';

          var html = document.documentElement;

          // Apply theme style
          var themeClasses = Array.from(html.classList).filter(function(cls) {
            return cls.startsWith('theme-');
          });
          themeClasses.forEach(function(cls) {
            html.classList.remove(cls);
          });
          html.classList.add(style);

          // Apply theme mode
          if (mode === 'dark') {
            html.classList.add('dark');
          } else {
            html.classList.remove('dark');
          }
        } catch (e) {
          console.error('Theme initialization error:', e);
        }
      })();
    `;
    document.head.appendChild(script);
  }
};

// Theme Provider component
export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Add theme init script to prevent flicker
  useEffect(() => {
    addThemeInitScript();
  }, []);

  // State for theme mode and style
  const [themeMode, setThemeModeState] = useState<ThemeMode>(() =>
    getStorageItem<ThemeMode>(STORAGE_KEY_MODE, DEFAULT_THEME_MODE)
  );

  const [themeStyle, setThemeStyleState] = useState<ThemeStyle>(() =>
    getStorageItem<ThemeStyle>(STORAGE_KEY_STYLE, DEFAULT_THEME_STYLE)
  );

  /**
   * Initialize theme from localStorage on component mount
   */
  useEffect(() => {
    // Get stored values
    const storedMode = getStorageItem<ThemeMode>(STORAGE_KEY_MODE, DEFAULT_THEME_MODE);
    const storedStyle = getStorageItem<ThemeStyle>(STORAGE_KEY_STYLE, DEFAULT_THEME_STYLE);

    // Update state if needed
    if (storedMode !== themeMode) setThemeModeState(storedMode);
    if (storedStyle !== themeStyle) setThemeStyleState(storedStyle);

    // Apply theme to document
    applyTheme(storedStyle, storedMode);
  }, []);

  /**
   * Apply theme changes to DOM and localStorage
   * @param style The theme style to apply
   * @param mode The theme mode to apply (light or dark)
   */
  const applyTheme = (style: ThemeStyle, mode: ThemeMode) => {
    try {
      if (typeof document === 'undefined') return;

      const html = document.documentElement;

      // Apply theme style
      const themeClasses = Array.from(html.classList).filter(cls => cls.startsWith('theme-'));
      themeClasses.forEach(cls => html.classList.remove(cls));
      html.classList.add(style);

      // Apply theme mode
      if (mode === 'dark') {
        html.classList.add('dark');
      } else {
        html.classList.remove('dark');
      }

      // Save to localStorage with verification
      const modeSuccess = setStorageItem(STORAGE_KEY_MODE, mode);
      const styleSuccess = setStorageItem(STORAGE_KEY_STYLE, style);

      if (!modeSuccess || !styleSuccess) {
        console.warn('Theme was applied to DOM but could not be saved to localStorage');
      }
    } catch (error) {
      console.error('Error applying theme:', error);
    }
  };

  /**
   * Handle theme mode changes
   * @param mode The new theme mode to apply
   */
  const handleSetThemeMode = (mode: ThemeMode) => {
    if (mode === themeMode) return; // No change needed


    setThemeModeState(mode);
    applyTheme(themeStyle, mode);
  };

  /**
   * Handle theme style changes
   * @param style The new theme style to apply
   */
  const handleSetThemeStyle = (style: ThemeStyle) => {
    if (style === themeStyle) return; // No change needed


    setThemeStyleState(style);
    applyTheme(style, themeMode);
  };

  /**
   * Set both theme style and mode at once
   * This is the preferred method to update the theme as it avoids race conditions
   * @param style The new theme style to apply
   * @param mode The new theme mode to apply
   */
  const handleSetTheme = (style: ThemeStyle, mode: ThemeMode) => {
    if (style === themeStyle && mode === themeMode) return; // No change needed



    // Update state
    setThemeStyleState(style);
    setThemeModeState(mode);

    // Apply theme in a single operation
    applyTheme(style, mode);
  };

  /**
   * Toggle between light and dark mode
   */
  const toggleThemeMode = () => {
    const newMode = themeMode === 'light' ? 'dark' : 'light';

    handleSetThemeMode(newMode);
  };

  /**
   * Listen for storage events (for cross-tab synchronization)
   * This ensures that theme changes made in one tab are reflected in all open tabs
   */
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      try {
        // Only process relevant storage events
        if (!e.key || (e.key !== STORAGE_KEY_MODE && e.key !== STORAGE_KEY_STYLE)) {
          return;
        }



        if (e.key === STORAGE_KEY_MODE) {
          // Handle theme mode change
          const newMode = e.newValue as ThemeMode || DEFAULT_THEME_MODE;

          // Only update if different from current state
          if (newMode !== themeMode) {

            setThemeModeState(newMode);

            // Apply the dark/light class directly
            if (typeof document !== 'undefined') {
              if (newMode === 'dark') {
                document.documentElement.classList.add('dark');
              } else {
                document.documentElement.classList.remove('dark');
              }
            }
          }
        } else if (e.key === STORAGE_KEY_STYLE) {
          // Handle theme style change
          const newStyle = e.newValue as ThemeStyle || DEFAULT_THEME_STYLE;

          // Only update if different from current state
          if (newStyle !== themeStyle) {

            setThemeStyleState(newStyle);

            // Apply the theme style class directly
            if (typeof document !== 'undefined') {
              const html = document.documentElement;
              const themeClasses = Array.from(html.classList).filter(cls => cls.startsWith('theme-'));
              themeClasses.forEach(cls => html.classList.remove(cls));
              html.classList.add(newStyle);
            }
          }
        }
      } catch (error) {
        console.error('Error handling storage event:', error);
      }
    };

    // Add event listener for storage events
    if (typeof window !== 'undefined') {
      window.addEventListener('storage', handleStorageChange);
      return () => window.removeEventListener('storage', handleStorageChange);
    }

    return undefined;
  }, [themeMode, themeStyle]);

  // Memoize context value to prevent unnecessary re-renders
  const contextValue = React.useMemo(() => ({
    themeMode,
    themeStyle,
    setThemeMode: handleSetThemeMode,
    setThemeStyle: handleSetThemeStyle,
    setTheme: handleSetTheme,
    toggleThemeMode,
  }), [themeMode, themeStyle]);

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

/**
 * Custom hook to use the theme context
 * @returns ThemeContextType object with theme state and methods
 * @throws Error if used outside of ThemeProvider
 */
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
