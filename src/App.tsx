
import { Suspense, lazy } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { NotificationProvider } from "@/contexts/NotificationContext";
import { UserProvider } from "@/contexts/UserContext";
import { SearchProvider } from "@/contexts/SearchContext";
import { AIProvider } from "@/contexts/AIContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import ThemeInitializer from "./components/theme/ThemeInitializer";
import { HelmetProvider } from "react-helmet-async";



// Theme initialization is now handled completely in ThemeInitializer

// Import essential components immediately
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import PageLoader from "./components/common/PageLoader";

// Import secondary components with lazy loading
const CandidateDetails = lazy(() => import("./pages/CandidateDetails"));
const KanbanView = lazy(() => import("./pages/KanbanView"));
const Jobs = lazy(() => import("./pages/Jobs"));
const JobDetails = lazy(() => import("./pages/JobDetails"));
const Candidates = lazy(() => import("./pages/Candidates"));
const Calendar = lazy(() => import("./pages/Calendar"));
const Messages = lazy(() => import("./pages/Messages"));
const Settings = lazy(() => import("./pages/Settings"));
const Notifications = lazy(() => import("./pages/Notifications"));
const Clients = lazy(() => import("./pages/Clients"));
const ClientDetails = lazy(() => import("./pages/ClientDetails"));
const ClientForm = lazy(() => import("./pages/ClientForm"));
const CandidateForm = lazy(() => import("./pages/CandidateForm"));
const JobForm = lazy(() => import("./pages/JobForm"));


const queryClient = new QueryClient();

const App = () => {


  return (
    <QueryClientProvider client={queryClient}>
      <UserProvider>
        <NotificationProvider>
          <AIProvider>
            <SearchProvider>
              <ThemeProvider>
                <ThemeInitializer />
                <TooltipProvider>
                  <Toaster />
                  <Sonner />
                  <HelmetProvider>
                    <BrowserRouter
                      future={{
                        v7_startTransition: true,
                        v7_relativeSplatPath: true
                      }}
                    >
                      <Routes>
                        <Route path="/" element={<Index />} />
                      <Route path="/candidates" element={
                        <Suspense fallback={<PageLoader />}>
                          <Candidates />
                        </Suspense>
                      } />
                      <Route path="/candidates/new" element={
                        <Suspense fallback={<PageLoader />}>
                          <CandidateForm />
                        </Suspense>
                      } />
                      <Route path="/candidates/:id" element={
                        <Suspense fallback={<PageLoader />}>
                          <CandidateDetails />
                        </Suspense>
                      } />
                      <Route path="/candidates/:id/edit" element={
                        <Suspense fallback={<PageLoader />}>
                          <CandidateForm />
                        </Suspense>
                      } />
                      <Route path="/jobs" element={
                        <Suspense fallback={<PageLoader />}>
                          <Jobs />
                        </Suspense>
                      } />
                      <Route path="/jobs/new" element={
                        <Suspense fallback={<PageLoader />}>
                          <JobForm />
                        </Suspense>
                      } />
                      <Route path="/jobs/:id" element={
                        <Suspense fallback={<PageLoader />}>
                          <JobDetails />
                        </Suspense>
                      } />
                      <Route path="/jobs/:id/edit" element={
                        <Suspense fallback={<PageLoader />}>
                          <JobForm />
                        </Suspense>
                      } />
                      <Route path="/kanban" element={
                        <Suspense fallback={<PageLoader />}>
                          <KanbanView />
                        </Suspense>
                      } />
                      <Route path="/calendar" element={
                        <Suspense fallback={<PageLoader />}>
                          <Calendar />
                        </Suspense>
                      } />
                      <Route path="/messages" element={
                        <Suspense fallback={<PageLoader />}>
                          <Messages />
                        </Suspense>
                      } />
                      <Route path="/notifications" element={
                        <Suspense fallback={<PageLoader />}>
                          <Notifications />
                        </Suspense>
                      } />
                      <Route path="/settings" element={
                        <Suspense fallback={<PageLoader />}>
                          <Settings />
                        </Suspense>
                      } />
                      <Route path="/clients" element={
                        <Suspense fallback={<PageLoader />}>
                          <Clients />
                        </Suspense>
                      } />
                      <Route path="/clients/:id" element={
                        <Suspense fallback={<PageLoader />}>
                          <ClientDetails />
                        </Suspense>
                      } />
                      <Route path="/clients/new" element={
                        <Suspense fallback={<PageLoader />}>
                          <ClientForm />
                        </Suspense>
                      } />
                      <Route path="/clients/:id/edit" element={
                        <Suspense fallback={<PageLoader />}>
                          <ClientForm />
                        </Suspense>
                      } />
                      {/* API routes handled by backend server */}
                      {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                      <Route path="*" element={<NotFound />} />
                    </Routes>
                  </BrowserRouter>
                </HelmetProvider>
              </TooltipProvider>
            </ThemeProvider>
          </SearchProvider>
        </AIProvider>
      </NotificationProvider>
    </UserProvider>
  </QueryClientProvider>
  );
};

export default App;
