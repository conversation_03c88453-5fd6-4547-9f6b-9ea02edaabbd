import { useState, useEffect } from 'react';
import { Search, Plus, Building } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Client } from '@/types/client';
import { useClients } from '@/hooks/useClients';
import { useToast } from '@/hooks/use-toast';

interface ClientSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onClientSelect: (clientId: string) => void;
  selectedClientId?: string;
}

export function ClientSelectionModal({
  isOpen,
  onClose,
  onClientSelect,
  selectedClientId
}: ClientSelectionModalProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const { clients, isLoading, createClient } = useClients();
  const { toast } = useToast();

  // New client form state
  const [newClient, setNewClient] = useState({
    company_name: '',
    contact_name: '',
    email: '',
    phone: '',
    industry: '',
    location: '',
    website: '',
    notes: '',
    status: 'active' as const
  });

  // Filter clients based on search query
  const filteredClients = clients.filter(client =>
    client.company_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (client.contact_name || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
    (client.industry || '').toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      setSearchQuery('');
      setShowCreateForm(false);
      setNewClient({
        company_name: '',
        contact_name: '',
        email: '',
        phone: '',
        industry: '',
        location: '',
        website: '',
        notes: '',
        status: 'active'
      });
    }
  }, [isOpen]);

  const handleCreateClient = async () => {
    if (!newClient.company_name || !newClient.contact_name || !newClient.email) {
      toast({
        title: "Validation Error",
        description: "Company name, contact name, and email are required.",
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);
    try {
      const createdClient = await createClient(newClient);
      onClientSelect(createdClient.id);
      onClose();
      toast({
        title: "Success",
        description: `Client "${newClient.company_name}" created and selected.`,
      });
    } catch (error) {
      console.error('Error creating client:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleClientSelect = (clientId: string) => {
    onClientSelect(clientId);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {showCreateForm ? 'Create New Client' : 'Select Client'}
          </DialogTitle>
          <DialogDescription>
            {showCreateForm 
              ? 'Fill in the client information below.'
              : 'Choose a client for this job or create a new one.'
            }
          </DialogDescription>
        </DialogHeader>

        {!showCreateForm ? (
          <div className="space-y-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search clients..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            {/* Client List */}
            <div className="border rounded-md max-h-60 overflow-y-auto">
              {isLoading ? (
                <div className="p-4 text-center text-muted-foreground">
                  Loading clients...
                </div>
              ) : filteredClients.length === 0 ? (
                <div className="p-4 text-center text-muted-foreground">
                  No clients found
                </div>
              ) : (
                <div className="divide-y">
                  {filteredClients.map((client) => (
                    <div
                      key={client.id}
                      className={`p-3 cursor-pointer hover:bg-muted/50 flex items-center gap-3 ${
                        selectedClientId === client.id ? 'bg-muted' : ''
                      }`}
                      onClick={() => handleClientSelect(client.id)}
                    >
                      <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center">
                        <Building className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <div className="flex-1">
                        <div className="font-medium">{client.company_name}</div>
                        <div className="text-sm text-muted-foreground">
                          {client.contact_name} • {client.industry || 'No industry'}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <DialogFooter className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => setShowCreateForm(true)}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Create New Client
              </Button>
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
            </DialogFooter>
          </div>
        ) : (
          /* Create Client Form */
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="company_name">Company Name *</Label>
                <Input
                  id="company_name"
                  value={newClient.company_name}
                  onChange={(e) => setNewClient(prev => ({ ...prev, company_name: e.target.value }))}
                  placeholder="Acme Corporation"
                />
              </div>
              <div>
                <Label htmlFor="contact_name">Contact Name *</Label>
                <Input
                  id="contact_name"
                  value={newClient.contact_name}
                  onChange={(e) => setNewClient(prev => ({ ...prev, contact_name: e.target.value }))}
                  placeholder="John Doe"
                />
              </div>
              <div>
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  type="email"
                  value={newClient.email}
                  onChange={(e) => setNewClient(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  value={newClient.phone}
                  onChange={(e) => setNewClient(prev => ({ ...prev, phone: e.target.value }))}
                  placeholder="+****************"
                />
              </div>
              <div>
                <Label htmlFor="industry">Industry</Label>
                <Input
                  id="industry"
                  value={newClient.industry}
                  onChange={(e) => setNewClient(prev => ({ ...prev, industry: e.target.value }))}
                  placeholder="Technology"
                />
              </div>
              <div>
                <Label htmlFor="location">Location</Label>
                <Input
                  id="location"
                  value={newClient.location}
                  onChange={(e) => setNewClient(prev => ({ ...prev, location: e.target.value }))}
                  placeholder="New York, NY"
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="website">Website</Label>
              <Input
                id="website"
                value={newClient.website}
                onChange={(e) => setNewClient(prev => ({ ...prev, website: e.target.value }))}
                placeholder="https://acme.com"
              />
            </div>

            <div>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={newClient.notes}
                onChange={(e) => setNewClient(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Additional information about this client..."
                className="min-h-[80px]"
              />
            </div>

            <DialogFooter className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => setShowCreateForm(false)}
              >
                Back to Selection
              </Button>
              <div className="flex gap-2">
                <Button variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleCreateClient}
                  disabled={isCreating}
                >
                  {isCreating ? 'Creating...' : 'Create & Select'}
                </Button>
              </div>
            </DialogFooter>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
