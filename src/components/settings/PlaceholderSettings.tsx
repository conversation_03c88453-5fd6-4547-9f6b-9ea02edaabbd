
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, Construction, Settings } from "lucide-react";
import { cn } from "@/lib/utils";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";

/**
 * Props for the PlaceholderSettings component
 */
interface PlaceholderSettingsProps {
  /** Title of the settings section */
  title: string;
  /** Optional description text */
  description?: string;
  /** Optional icon to display */
  icon?: React.ReactNode;
  /** Optional status: 'coming-soon', 'in-development', or 'planned' */
  status?: 'coming-soon' | 'in-development' | 'planned';
  /** Optional action button text */
  actionText?: string;
  /** Optional action button handler */
  onAction?: () => void;
  /** Optional additional content */
  children?: React.ReactNode;
}

/**
 * PlaceholderSettings Component
 *
 * A standardized placeholder for settings sections that are not yet implemented.
 * Can be customized with different statuses, icons, and actions.
 */
export default function PlaceholderSettings({
  title,
  description,
  icon = <Settings className="h-8 w-8 text-muted-foreground" />,
  status = 'coming-soon',
  actionText,
  onAction,
  children
}: PlaceholderSettingsProps) {
  // Status-specific content
  const statusContent = {
    'coming-soon': {
      title: 'Coming Soon',
      description: 'This feature is coming soon. Check back for updates.',
      icon: <AlertCircle className="h-5 w-5" />,
      color: 'text-blue-500'
    },
    'in-development': {
      title: 'In Development',
      description: 'This feature is currently being developed.',
      icon: <Construction className="h-5 w-5" />,
      color: 'text-yellow-500'
    },
    'planned': {
      title: 'Planned',
      description: 'This feature is planned for a future release.',
      icon: <Settings className="h-5 w-5" />,
      color: 'text-gray-500'
    }
  };

  const statusInfo = statusContent[status];

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>{title} Settings</CardTitle>
          <CardDescription>
            {description || `Settings for ${title.toLowerCase()}`}
          </CardDescription>
        </div>
        <div className="flex-shrink-0">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <Alert className={cn("mb-4", statusInfo.color)}>
          {statusInfo.icon}
          <AlertTitle>{statusInfo.title}</AlertTitle>
          <AlertDescription>
            {statusInfo.description}
          </AlertDescription>
        </Alert>

        {children && (
          <div className="mt-4">
            {children}
          </div>
        )}

        {!children && (
          <div className="flex flex-col items-center justify-center p-8 text-center">
            <p className="text-sm text-muted-foreground">
              This section is not yet available. We're working on it!
            </p>
          </div>
        )}

        {actionText && onAction && (
          <div className="mt-4 flex justify-end">
            <Button onClick={onAction}>
              {actionText}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
