
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useState, useEffect } from "react";
import { Eye, EyeOff } from "lucide-react";
import { useAI, AIModelType } from "@/contexts/AIContext";

export default function IntegrationsSettings() {
  const { settings, updateSettings, resetSettings } = useAI();
  const [showApiKey, setShowApiKey] = useState(false);
  const [aiModel, setAiModel] = useState<AIModelType>(settings.model);
  const [apiKey, setApiKey] = useState(settings.apiKey);
  const [enabled, setEnabled] = useState(settings.enabled);
  const [rateLimited, setRateLimited] = useState(settings.rateLimited);

  // Update local state when settings change
  useEffect(() => {
    setAiModel(settings.model);
    setApiKey(settings.apiKey);
    setEnabled(settings.enabled);
    setRateLimited(settings.rateLimited);
  }, [settings]);

  // Handle saving AI settings
  const handleSaveAISettings = () => {
    updateSettings({
      model: aiModel,
      apiKey,
      enabled,
      rateLimited,
    });
  };

  // Handle resetting AI settings
  const handleResetAISettings = () => {
    resetSettings();
  };

  return (
    <div className="space-y-6">

      {/* AI Settings */}
      <Card>
        <CardHeader>
          <CardTitle>AI Message Assistance</CardTitle>
          <CardDescription>
            Configure AI settings for message enhancement and suggestions.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="ai-model">AI Model</Label>
              <Select
                value={aiModel}
                onValueChange={(value) => setAiModel(value as AIModelType)}
              >
                <SelectTrigger id="ai-model">
                  <SelectValue placeholder="Select AI model" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="openai">OpenAI</SelectItem>
                  <SelectItem value="anthropic">Anthropic Claude</SelectItem>
                  <SelectItem value="gemini">Google Gemini</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground">Choose your preferred AI model for message suggestions.</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="api-key">API Key</Label>
              <div className="flex">
                <Input
                  id="api-key"
                  type={showApiKey ? "text" : "password"}
                  value={apiKey}
                  onChange={(e) => setApiKey(e.target.value)}
                  placeholder="Enter your API key"
                  className="flex-1"
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setShowApiKey(!showApiKey)}
                  className="ml-2"
                >
                  {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">Your API key is encrypted and stored securely.</p>
            </div>

            <div className="flex justify-between items-center">
              <div>
                <h3 className="font-medium">Enable AI Suggestions</h3>
                <p className="text-sm text-muted-foreground">Show AI enhancement button in messages</p>
              </div>
              <Switch id="enable-ai" checked={enabled} onCheckedChange={setEnabled} />
            </div>

            <div className="flex justify-between items-center">
              <div>
                <h3 className="font-medium">Rate Limiting</h3>
                <p className="text-sm text-muted-foreground">Limit API calls to prevent excessive usage</p>
              </div>
              <Switch id="rate-limit" checked={rateLimited} onCheckedChange={setRateLimited} />
            </div>
          </div>

          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={handleResetAISettings}>Reset</Button>
            <Button onClick={handleSaveAISettings}>Save AI Settings</Button>
          </div>
        </CardContent>
      </Card>

      {/* Calendar Integrations */}
      <Card>
        <CardHeader>
          <CardTitle>Calendar Integrations</CardTitle>
          <CardDescription>
            Connect your Google Calendar or Calendly to schedule interviews and meetings.
          </CardDescription>
        </CardHeader>
      <CardContent className="space-y-6">
        <div className="border rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="h-10 w-10 flex items-center justify-center rounded-full bg-blue-100">
                <svg className="h-6 w-6 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M21.56 10.738c0-.655-.057-1.284-.164-1.886H12v3.572h5.358a4.584 4.584 0 01-1.987 3.008v2.5h3.217c1.88-1.732 2.972-4.284 2.972-7.194z" />
                  <path d="M12 22c2.693 0 4.95-.886 6.601-2.407l-3.218-2.5c-.893.598-2.036.95-3.383.95-2.601 0-4.8-1.758-5.585-4.118H3.064v2.578A9.998 9.998 0 0012 22z" />
                  <path d="M6.415 13.924a6.008 6.008 0 010-3.848V7.5H3.064a10.002 10.002 0 000 9l3.35-2.576z" />
                  <path d="M12 5.932c1.467 0 2.784.503 3.822 1.491l2.855-2.855C16.946 2.97 14.689 2 12 2a10 10 0 00-8.936 5.5l3.35 2.576c.79-2.361 2.988-4.144 5.587-4.144z" />
                </svg>
              </div>
              <div>
                <h3 className="font-medium">Google Calendar</h3>
                <p className="text-sm text-muted-foreground">Connect to create and manage interview events</p>
              </div>
            </div>
            <Button variant="outline">Connect</Button>
          </div>
        </div>

        <div className="border rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="h-10 w-10 flex items-center justify-center rounded-full bg-blue-100">
                <svg className="h-6 w-6 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8z" />
                  <path d="M13 7h-2v6h6v-2h-4z" />
                </svg>
              </div>
              <div>
                <h3 className="font-medium">Calendly</h3>
                <p className="text-sm text-muted-foreground">Let candidates book time slots directly</p>
              </div>
            </div>
            <Button variant="outline">Connect</Button>
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="font-medium">Two-way Calendar Sync</h3>
              <p className="text-sm text-muted-foreground">Keep your connected calendars in sync</p>
            </div>
            <Switch id="two-way-sync" />
          </div>

          <div className="flex justify-between items-center">
            <div>
              <h3 className="font-medium">Email Notifications</h3>
              <p className="text-sm text-muted-foreground">Send notifications for calendar events</p>
            </div>
            <Switch id="calendar-email-notifications" defaultChecked />
          </div>

          <div className="flex justify-between items-center">
            <div>
              <h3 className="font-medium">SMS Reminders</h3>
              <p className="text-sm text-muted-foreground">Send SMS reminders before events</p>
            </div>
            <Switch id="sms-reminders" />
          </div>
        </div>

        <div className="flex justify-end gap-2">
          <Button>Save Settings</Button>
        </div>
      </CardContent>
    </Card>
    </div>
  );
}
