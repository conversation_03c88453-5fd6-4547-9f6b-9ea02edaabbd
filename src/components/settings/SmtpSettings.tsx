
import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export interface SmtpSettingsType {
  host: string;
  port: string;
  username: string;
  password: string;
  fromEmail: string;
  fromName: string;
  encryption: string;
  authentication: boolean;
}

export default function SmtpSettings() {
  const [smtpSettings, setSmtpSettings] = useState<SmtpSettingsType>({
    host: "",
    port: "587",
    username: "",
    password: "",
    fromEmail: "",
    fromName: "",
    encryption: "tls",
    authentication: true,
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>Email SMTP Settings</CardTitle>
        <CardDescription>
          Configure your SMTP server settings for sending emails to candidates, clients, and team members.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="smtp-host">SMTP Server Host</Label>
            <Input 
              id="smtp-host" 
              placeholder="e.g. smtp.gmail.com" 
              value={smtpSettings.host}
              onChange={(e) => setSmtpSettings({...smtpSettings, host: e.target.value})}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="smtp-port">SMTP Port</Label>
            <Input 
              id="smtp-port" 
              placeholder="587" 
              value={smtpSettings.port}
              onChange={(e) => setSmtpSettings({...smtpSettings, port: e.target.value})}
            />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="smtp-username">Username</Label>
            <Input 
              id="smtp-username" 
              placeholder="<EMAIL>" 
              value={smtpSettings.username}
              onChange={(e) => setSmtpSettings({...smtpSettings, username: e.target.value})}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="smtp-password">Password</Label>
            <Input 
              id="smtp-password" 
              type="password" 
              placeholder="••••••••" 
              value={smtpSettings.password}
              onChange={(e) => setSmtpSettings({...smtpSettings, password: e.target.value})}
            />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="from-email">From Email</Label>
            <Input 
              id="from-email" 
              placeholder="<EMAIL>" 
              value={smtpSettings.fromEmail}
              onChange={(e) => setSmtpSettings({...smtpSettings, fromEmail: e.target.value})}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="from-name">From Name</Label>
            <Input 
              id="from-name" 
              placeholder="Your Company Recruiting" 
              value={smtpSettings.fromName}
              onChange={(e) => setSmtpSettings({...smtpSettings, fromName: e.target.value})}
            />
          </div>
        </div>
        
        <Separator />
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="encryption">Encryption</Label>
            <Select 
              value={smtpSettings.encryption} 
              onValueChange={(value) => setSmtpSettings({...smtpSettings, encryption: value})}
            >
              <SelectTrigger id="encryption">
                <SelectValue placeholder="Select encryption" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="tls">TLS</SelectItem>
                <SelectItem value="ssl">SSL</SelectItem>
                <SelectItem value="none">None</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-center space-x-2 pt-8">
            <Switch 
              id="require-auth" 
              checked={smtpSettings.authentication}
              onCheckedChange={(checked) => setSmtpSettings({...smtpSettings, authentication: checked})}
            />
            <Label htmlFor="require-auth">Require authentication</Label>
          </div>
        </div>
        
        <div className="flex justify-end gap-2">
          <Button variant="outline">Test Connection</Button>
          <Button>Save Settings</Button>
        </div>
      </CardContent>
    </Card>
  );
}
