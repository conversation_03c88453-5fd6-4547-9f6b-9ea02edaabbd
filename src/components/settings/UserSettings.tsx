import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { useUser } from '@/contexts/UserContext';
import { useTheme } from '@/contexts/ThemeContext';
import { ThemeSelector } from '@/components/layout/ThemeSelector';

/**
 * UserSettings component
 * Provides user-specific settings including theme selection and accessibility options
 */
export default function UserSettings() {
  const { user, updateUserPreferences } = useUser();
  const { themeStyle, themeMode } = useTheme();

  // Format theme name for display
  const formatThemeName = React.useCallback((style: string): string => {
    return style ? style.charAt(0).toUpperCase() + style.slice(1).replace('theme-', '') : 'Default';
  }, []);

  // Handle accessibility toggles
  const handleAccessibilityChange = React.useCallback((
    key: keyof typeof user.preferences.accessibility,
    value: boolean
  ) => {
    updateUserPreferences({
      accessibility: {
        ...user.preferences.accessibility,
        [key]: value,
      },
    });
  }, [user.preferences.accessibility, updateUserPreferences]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>User Settings</CardTitle>
        <CardDescription>
          Customize your experience with personalized settings.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Theme Settings */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium">Theme</h3>
              <p className="text-sm text-muted-foreground">
                Choose a theme that matches your style and preferences.
              </p>
            </div>
            <div className="text-sm font-medium">
              Current: <span className="text-primary">
                {formatThemeName(themeStyle)}
              </span>
              {themeMode === 'dark' && <span className="ml-1">(Dark)</span>}
            </div>
          </div>

          {/* Theme Selector Dialog Button */}
          <div className="mt-4 flex">
            <ThemeSelector showTrigger={true} buttonText="Theme Options" />
          </div>
        </div>

        <Separator />

        {/* Accessibility Settings */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium">Accessibility</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="high-contrast" className="flex-1">
                <div>High Contrast</div>
                <div className="text-sm font-normal text-muted-foreground">
                  Increase contrast for better readability
                </div>
              </Label>
              <Switch
                id="high-contrast"
                checked={user.preferences.accessibility?.highContrast || false}
                onCheckedChange={(checked) => handleAccessibilityChange('highContrast', checked)}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="large-text" className="flex-1">
                <div>Large Text</div>
                <div className="text-sm font-normal text-muted-foreground">
                  Increase text size for better readability
                </div>
              </Label>
              <Switch
                id="large-text"
                checked={user.preferences.accessibility?.largeText || false}
                onCheckedChange={(checked) => handleAccessibilityChange('largeText', checked)}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="reduced-motion" className="flex-1">
                <div>Reduced Motion</div>
                <div className="text-sm font-normal text-muted-foreground">
                  Minimize animations and transitions
                </div>
              </Label>
              <Switch
                id="reduced-motion"
                checked={user.preferences.accessibility?.reducedMotion || false}
                onCheckedChange={(checked) => handleAccessibilityChange('reducedMotion', checked)}
              />
            </div>
          </div>
        </div>

        <Separator />

        {/* Language Settings */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium">Language</h3>
          <p className="text-sm text-muted-foreground">
            Currently set to: <span className="font-medium">English</span>
          </p>
          <p className="text-xs text-muted-foreground">
            Additional language options will be available in a future update.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
