
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MessageCircle, Phone } from "lucide-react";

export interface PhoneServiceType {
  provider: string;
  apiKey: string;
  apiSecret: string;
  fromNumber: string;
  defaultCountryCode: string;
  whatsappEnabled: boolean;
  smsEnabled: boolean;
  callEnabled: boolean;
}

export default function PhoneServiceSettings() {
  const [phoneService, setPhoneService] = useState<PhoneServiceType>({
    provider: "twilio",
    apiKey: "",
    apiSecret: "",
    fromNumber: "",
    defaultCountryCode: "+1",
    whatsappEnabled: true,
    smsEnabled: true,
    callEnabled: false,
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>Phone Communication Services</CardTitle>
        <CardDescription>
          Configure services for SMS, WhatsApp, and voice communications with candidates and clients.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <Label htmlFor="phone-provider">Service Provider</Label>
          <Select 
            value={phoneService.provider} 
            onValueChange={(value) => setPhoneService({...phoneService, provider: value})}
          >
            <SelectTrigger id="phone-provider" className="w-full">
              <SelectValue placeholder="Select provider" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="twilio">Twilio</SelectItem>
              <SelectItem value="vapi">VAPI</SelectItem>
              <SelectItem value="callhippo">CallHippo</SelectItem>
              <SelectItem value="custom">Custom API</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="api-key">API Key/Account SID</Label>
            <Input 
              id="api-key" 
              placeholder="Enter API key" 
              value={phoneService.apiKey}
              onChange={(e) => setPhoneService({...phoneService, apiKey: e.target.value})}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="api-secret">API Secret/Auth Token</Label>
            <Input 
              id="api-secret" 
              type="password" 
              placeholder="••••••••" 
              value={phoneService.apiSecret}
              onChange={(e) => setPhoneService({...phoneService, apiSecret: e.target.value})}
            />
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="from-number">From Phone Number</Label>
            <Input 
              id="from-number" 
              placeholder="+***********" 
              value={phoneService.fromNumber}
              onChange={(e) => setPhoneService({...phoneService, fromNumber: e.target.value})}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="country-code">Default Country Code</Label>
            <Input 
              id="country-code" 
              placeholder="+1" 
              value={phoneService.defaultCountryCode}
              onChange={(e) => setPhoneService({...phoneService, defaultCountryCode: e.target.value})}
            />
          </div>
        </div>
        
        <Separator />
        
        <div className="space-y-4">
          <h3 className="text-sm font-medium">Communication Channels</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <MessageCircle className="h-4 w-4 text-muted-foreground" />
                <Label htmlFor="sms-enabled" className="flex-1">SMS Messages</Label>
              </div>
              <Switch 
                id="sms-enabled" 
                checked={phoneService.smsEnabled}
                onCheckedChange={(checked) => setPhoneService({...phoneService, smsEnabled: checked})}
              />
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <MessageCircle className="h-4 w-4 text-green-600" />
                <Label htmlFor="whatsapp-enabled" className="flex-1">WhatsApp Messages</Label>
              </div>
              <Switch 
                id="whatsapp-enabled" 
                checked={phoneService.whatsappEnabled}
                onCheckedChange={(checked) => setPhoneService({...phoneService, whatsappEnabled: checked})}
              />
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <Label htmlFor="call-enabled" className="flex-1">Voice Calls</Label>
              </div>
              <Switch 
                id="call-enabled" 
                checked={phoneService.callEnabled}
                onCheckedChange={(checked) => setPhoneService({...phoneService, callEnabled: checked})}
              />
            </div>
          </div>
        </div>
        
        <div className="flex justify-end gap-2">
          <Button variant="outline">Test Connection</Button>
          <Button>Save Settings</Button>
        </div>
      </CardContent>
    </Card>
  );
}
