
import { But<PERSON> } from "@/components/ui/button";
import {
  User,
  Bell,
  Mail,
  Lock,
  Building,
  Users,
  Phone,
  Settings,
  Palette
} from "lucide-react";

interface SettingsMenuProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

export default function SettingsMenu({ activeTab, setActiveTab }: SettingsMenuProps) {
  const menuItems = [
    { id: "profile", label: "Profile", icon: User },
    { id: "user", label: "User Settings", icon: Palette }, // Cambiado a Palette para incluir la funcionalidad de temas
    { id: "notifications", label: "Notifications", icon: Bell },
    { id: "emails", label: "Email Templates", icon: Mail },
    { id: "security", label: "Security", icon: Lock },
    { id: "company", label: "Company", icon: Building },
    { id: "team", label: "Team Members", icon: Users },
    { id: "smtp", label: "SMTP Settings", icon: Mail },
    { id: "phone", label: "Phone Services", icon: Phone },
    { id: "integrations", label: "Integrations", icon: Settings },
  ];

  return (
    <div className="space-y-1">
      {menuItems.map((item) => (
        <Button
          key={item.id}
          variant={activeTab === item.id ? "secondary" : "ghost"}
          className="w-full justify-start"
          onClick={() => setActiveTab(item.id)}
        >
          <item.icon className="h-4 w-4 mr-2" />
          {item.label}
        </Button>
      ))}
    </div>
  );
}
