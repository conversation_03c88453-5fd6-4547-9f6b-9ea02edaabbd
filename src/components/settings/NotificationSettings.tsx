
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { But<PERSON> } from "@/components/ui/button";

export default function NotificationSettings() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification Settings</CardTitle>
        <CardDescription>
          Manage how you receive notifications and alerts.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-sm font-medium">Email Notifications</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="notify-applications" className="flex-1">New applications</Label>
              <Switch id="notify-applications" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="notify-interviews" className="flex-1">Interview reminders</Label>
              <Switch id="notify-interviews" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="notify-messages" className="flex-1">New messages</Label>
              <Switch id="notify-messages" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="notify-status" className="flex-1">Candidate status changes</Label>
              <Switch id="notify-status" defaultChecked />
            </div>
          </div>
        </div>
        
        <Separator />
        
        <div className="space-y-4">
          <h3 className="text-sm font-medium">In-App Notifications</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="app-applications" className="flex-1">New applications</Label>
              <Switch id="app-applications" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="app-messages" className="flex-1">New messages</Label>
              <Switch id="app-messages" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="app-reminders" className="flex-1">Task reminders</Label>
              <Switch id="app-reminders" defaultChecked />
            </div>
          </div>
        </div>
        
        <div className="flex justify-end gap-2">
          <Button>Save Changes</Button>
        </div>
      </CardContent>
    </Card>
  );
}
