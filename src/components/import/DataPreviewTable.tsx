import React, { useState, useMemo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { AlertCircle, Edit2, Check, X, ChevronLeft, ChevronRight } from 'lucide-react';
import { CSVRecord, MappedColumn, ValidationError } from './SimplifiedCSVImport';

interface DataPreviewTableProps {
  data: CSVRecord[];
  columnMappings: MappedColumn[];
  validationErrors: ValidationError[];
  onDataChange: (data: CSVRecord[]) => void;
}

export default function DataPreviewTable({
  data,
  columnMappings,
  validationErrors,
  onDataChange
}: DataPreviewTableProps) {
  const [editingCell, setEditingCell] = useState<{ row: number; column: string } | null>(null);
  const [editValue, setEditValue] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  // Get mapped columns only
  const mappedColumns = useMemo(() =>
    columnMappings.filter(m => m.targetField),
    [columnMappings]
  );

  // Get error map for quick lookup
  const errorMap = useMemo(() => {
    const map = new Map<string, ValidationError>();
    validationErrors.forEach(error => {
      map.set(`${error.row}-${error.field}`, error);
    });
    return map;
  }, [validationErrors]);

  // Paginated data
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return data.slice(startIndex, startIndex + pageSize);
  }, [data, currentPage, pageSize]);

  const totalPages = Math.ceil(data.length / pageSize);

  // Handle cell edit
  const handleCellEdit = (rowIndex: number, column: string, value: string) => {
    setEditingCell({ row: rowIndex, column });
    setEditValue(value);
  };

  // Save cell edit
  const handleSaveEdit = () => {
    if (!editingCell) return;

    const actualRowIndex = (currentPage - 1) * pageSize + editingCell.row;
    const newData = [...data];
    newData[actualRowIndex] = {
      ...newData[actualRowIndex],
      [editingCell.column]: editValue
    };

    onDataChange(newData);
    setEditingCell(null);
    setEditValue('');
  };

  // Cancel cell edit
  const handleCancelEdit = () => {
    setEditingCell(null);
    setEditValue('');
  };

  // Get cell error
  const getCellError = (rowIndex: number, targetField: string) => {
    const actualRowIndex = (currentPage - 1) * pageSize + rowIndex + 1; // +1 because errors are 1-indexed
    return errorMap.get(`${actualRowIndex}-${targetField}`);
  };

  // Get field label
  const getFieldLabel = (targetField: string) => {
    const fieldLabels: Record<string, string> = {
      first_name: 'First Name',
      last_name: 'Last Name',
      email: 'Email',
      phone: 'Phone',
      location: 'Location',
      portfolio_url: 'Portfolio',
      status: 'Status',
      secondary_status: 'Secondary Status',
      english_level: 'English Level',
      interview_score: 'Interview Score',
      interview_notes: 'Interview Notes',
      challenge: 'Challenge',
      stargety_id: 'Stargety ID',
      is_duplicate: 'Duplicate Status'
    };
    return fieldLabels[targetField] || targetField;
  };

  if (mappedColumns.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <AlertCircle className="h-8 w-8 mx-auto mb-2" />
            <p>No columns mapped. Please map at least one column to preview data.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Data Preview</CardTitle>
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">
              Showing {((currentPage - 1) * pageSize) + 1}-{Math.min(currentPage * pageSize, data.length)} of {data.length}
            </span>
            <div className="flex gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                disabled={currentPage === totalPages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">#</TableHead>
                {mappedColumns.map(mapping => (
                  <TableHead key={mapping.csvColumn} className="min-w-[150px]">
                    <div className="space-y-1">
                      <div className="font-medium">{getFieldLabel(mapping.targetField)}</div>
                      <div className="text-xs text-muted-foreground">
                        from: {mapping.csvColumn}
                      </div>
                      {mapping.confidence < 0.8 && (
                        <Badge variant="outline" className="text-xs">
                          Low confidence
                        </Badge>
                      )}
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedData.map((row, rowIndex) => (
                <TableRow key={rowIndex}>
                  <TableCell className="font-mono text-sm">
                    {(currentPage - 1) * pageSize + rowIndex + 1}
                  </TableCell>
                  {mappedColumns.map(mapping => {
                    const value = row[mapping.csvColumn] || '';
                    const error = getCellError(rowIndex, mapping.targetField);
                    const isEditing = editingCell?.row === rowIndex && editingCell?.column === mapping.csvColumn;

                    return (
                      <TableCell key={mapping.csvColumn} className="relative">
                        {isEditing ? (
                          <div className="flex items-center gap-2">
                            <Input
                              value={editValue}
                              onChange={(e) => setEditValue(e.target.value)}
                              className="h-8"
                              autoFocus
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') handleSaveEdit();
                                if (e.key === 'Escape') handleCancelEdit();
                              }}
                            />
                            <Button size="sm" variant="ghost" onClick={handleSaveEdit}>
                              <Check className="h-3 w-3" />
                            </Button>
                            <Button size="sm" variant="ghost" onClick={handleCancelEdit}>
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ) : (
                          <div className="group flex items-center gap-2">
                            <span className={`flex-1 ${error ? 'text-red-600' : ''}`}>
                              {value || <span className="text-muted-foreground italic">empty</span>}
                            </span>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="opacity-0 group-hover:opacity-100 h-6 w-6 p-0"
                              onClick={() => handleCellEdit(rowIndex, mapping.csvColumn, value)}
                            >
                              <Edit2 className="h-3 w-3" />
                            </Button>
                            {error && (
                              <div className="flex items-center">
                                <AlertCircle className="h-4 w-4 text-red-500" title={error.message} />
                              </div>
                            )}
                          </div>
                        )}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Validation Summary */}
        {validationErrors.length > 0 && (
          <div className="mt-4 p-4 bg-red-50 dark:bg-red-950/20 rounded-lg">
            <h4 className="font-medium text-red-800 dark:text-red-200 mb-2">
              Validation Errors ({validationErrors.length})
            </h4>
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {validationErrors.slice(0, 10).map((error, index) => (
                <div key={index} className="text-sm text-red-700 dark:text-red-300">
                  Row {error.row}, {getFieldLabel(error.field)}: {error.message}
                </div>
              ))}
              {validationErrors.length > 10 && (
                <div className="text-sm text-red-600 dark:text-red-400">
                  ... and {validationErrors.length - 10} more errors
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
