import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ArrowRight, RefreshCw, CheckCircle } from 'lucide-react';
import { MappedColumn } from './SimplifiedCSVImport';

interface ColumnMappingBarProps {
  columnMappings: MappedColumn[];
  onMappingChange: (csvColumn: string, targetField: string) => void;
}

// Available target fields for candidates
const CANDIDATE_FIELDS = [
  { value: 'skip', label: 'Skip this column' },
  { value: 'first_name', label: 'First Name', required: true },
  { value: 'last_name', label: 'Last Name' },
  { value: 'email', label: 'Email' },
  { value: 'phone', label: 'Phone' },
  { value: 'location', label: 'Location' },
  { value: 'portfolio_url', label: 'Portfolio URL' },
  { value: 'linkedin_url', label: 'LinkedIn URL' },
  { value: 'github_url', label: 'GitHub URL' },
  { value: 'resume_url', label: 'Resume URL' },
  { value: 'current_position', label: 'Current Position' },
  { value: 'current_company', label: 'Current Company' },
  { value: 'experience_years', label: 'Years of Experience' },
  { value: 'desired_salary', label: 'Desired Salary' },
  { value: 'status', label: 'Primary Status' },
  { value: 'secondary_status', label: 'Secondary Status' },
  { value: 'english_level', label: 'English Level' },
  { value: 'interview_score', label: 'Interview Score' },
  { value: 'interview_notes', label: 'Interview Notes' },
  { value: 'challenge', label: 'Challenge' },
  { value: 'challenge_notes', label: 'Challenge Notes' },
  { value: 'challenge_feedback', label: 'Challenge Feedback' },
  { value: 'stargety_id', label: 'Stargety ID' },
  { value: 'is_duplicate', label: 'Duplicate Status' },
  { value: 'source', label: 'Source' },
  { value: 'notes', label: 'Notes' },
  { value: 'drive_score', label: 'Drive Score' },
  { value: 'resilience_score', label: 'Resilience Score' },
  { value: 'collaboration_score', label: 'Collaboration Score' },
  { value: 'result', label: 'Result' }
];

export default function ColumnMappingBar({ columnMappings, onMappingChange }: ColumnMappingBarProps) {
  // Get used target fields to prevent duplicates
  const usedFields = new Set(
    columnMappings
      .filter(m => m.targetField && m.targetField !== 'skip')
      .map(m => m.targetField)
  );

  // Auto-map remaining unmapped columns
  const handleAutoMap = () => {
    columnMappings.forEach(mapping => {
      if (!mapping.targetField || mapping.targetField === 'skip') {
        // Find best available field based on column name similarity
        const availableFields = CANDIDATE_FIELDS.filter(
          field => field.value && field.value !== 'skip' && !usedFields.has(field.value)
        );

        const columnName = mapping.csvColumn.toLowerCase();
        let bestMatch = '';
        let bestScore = 0;

        availableFields.forEach(field => {
          const fieldName = field.label.toLowerCase();
          const score = calculateSimilarity(columnName, fieldName);
          if (score > bestScore && score > 0.3) {
            bestScore = score;
            bestMatch = field.value;
          }
        });

        if (bestMatch) {
          onMappingChange(mapping.csvColumn, bestMatch);
        }
      }
    });
  };

  // Calculate string similarity
  const calculateSimilarity = (str1: string, str2: string): number => {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) return 1.0;

    const distance = levenshteinDistance(longer, shorter);
    return (longer.length - distance) / longer.length;
  };

  // Levenshtein distance calculation
  const levenshteinDistance = (str1: string, str2: string): number => {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  };

  // Clear all mappings
  const handleClearAll = () => {
    columnMappings.forEach(mapping => {
      onMappingChange(mapping.csvColumn, 'skip');
    });
  };

  const mappedCount = columnMappings.filter(m => m.targetField && m.targetField !== 'skip').length;
  const requiredFields = CANDIDATE_FIELDS.filter(f => f.required);
  const mappedRequiredFields = requiredFields.filter(field =>
    columnMappings.some(m => m.targetField === field.value)
  );

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            Column Mapping
            <Badge variant="secondary">
              {mappedCount}/{columnMappings.length} mapped
            </Badge>
          </CardTitle>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={handleAutoMap}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Auto Map
            </Button>
            <Button variant="outline" size="sm" onClick={handleClearAll}>
              Clear All
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Required Fields Status */}
        <div className="mb-4 p-3 bg-muted/50 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-sm font-medium">Required Fields:</span>
            {mappedRequiredFields.length === requiredFields.length ? (
              <Badge variant="outline" className="text-green-600">
                <CheckCircle className="h-3 w-3 mr-1" />
                Complete
              </Badge>
            ) : (
              <Badge variant="destructive">
                {mappedRequiredFields.length}/{requiredFields.length}
              </Badge>
            )}
          </div>
          <div className="flex flex-wrap gap-2">
            {requiredFields.map(field => {
              const isMapped = mappedRequiredFields.some(f => f.value === field.value);
              return (
                <Badge
                  key={field.value}
                  variant={isMapped ? "default" : "outline"}
                  className={isMapped ? "text-green-600" : "text-red-600"}
                >
                  {field.label}
                </Badge>
              );
            })}
          </div>
        </div>

        {/* Column Mappings */}
        <div className="space-y-3">
          {columnMappings.map(mapping => {
            const availableFields = CANDIDATE_FIELDS.filter(field =>
              field.value === 'skip' ||
              field.value === mapping.targetField ||
              !usedFields.has(field.value)
            );

            return (
              <div key={mapping.csvColumn} className="flex items-center gap-4 p-3 border rounded-lg">
                <div className="flex-1">
                  <div className="font-medium text-sm">{mapping.csvColumn}</div>
                  <div className="text-xs text-muted-foreground">CSV Column</div>
                </div>

                <ArrowRight className="h-4 w-4 text-muted-foreground" />

                <div className="flex-1">
                  <Select
                    value={mapping.targetField}
                    onValueChange={(value) => onMappingChange(mapping.csvColumn, value)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select target field..." />
                    </SelectTrigger>
                    <SelectContent>
                      {availableFields.map(field => (
                        <SelectItem key={field.value} value={field.value}>
                          <div className="flex items-center gap-2">
                            {field.label}
                            {field.required && (
                              <Badge variant="outline" className="text-xs">
                                Required
                              </Badge>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {mapping.confidence > 0 && (
                  <div className="flex items-center gap-1">
                    <Badge
                      variant="outline"
                      className={
                        mapping.confidence >= 0.8
                          ? "text-green-600"
                          : mapping.confidence >= 0.5
                          ? "text-yellow-600"
                          : "text-red-600"
                      }
                    >
                      {Math.round(mapping.confidence * 100)}%
                    </Badge>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Mapping Summary */}
        {mappedCount > 0 && (
          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
            <div className="text-sm">
              <strong>{mappedCount}</strong> columns will be imported.
              {mappedRequiredFields.length < requiredFields.length && (
                <span className="text-red-600 ml-2">
                  Missing {requiredFields.length - mappedRequiredFields.length} required field(s).
                </span>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
