import React, { useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from '@/components/ui/input';

interface DeleteConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  itemCount: number;
  itemType: string;
  confirmationText?: string;
}

/**
 * A reusable delete confirmation dialog with text input verification
 */
export default function DeleteConfirmationDialog({
  open,
  onOpenChange,
  onConfirm,
  itemCount,
  itemType,
  confirmationText = 'delete'
}: DeleteConfirmationDialogProps) {
  const [inputText, setInputText] = useState('');
  
  const handleConfirm = () => {
    if (inputText.toLowerCase() === confirmationText.toLowerCase()) {
      onConfirm();
      setInputText('');
    }
  };
  
  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      setInputText('');
    }
    onOpenChange(newOpen);
  };
  
  const isConfirmDisabled = inputText.toLowerCase() !== confirmationText.toLowerCase();
  
  return (
    <AlertDialog open={open} onOpenChange={handleOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete {itemCount} {itemType}
            {itemCount !== 1 ? 's' : ''} from the database.
            
            <div className="mt-4">
              <p className="text-sm font-medium mb-2">
                Type "{confirmationText}" to confirm:
              </p>
              <Input
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                placeholder={confirmationText}
                className="w-full"
                autoFocus
                aria-label={`Type '${confirmationText}' to confirm`}
              />
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isConfirmDisabled}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            Delete Forever
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
