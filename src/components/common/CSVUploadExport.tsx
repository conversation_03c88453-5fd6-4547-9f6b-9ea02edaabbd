
import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Upload, Download, FileWarning, List, LayoutGrid, Table } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface CSVUploadExportProps {
  onUpload: (file: File) => Promise<void>;
  onExport: () => void;
  entityName: string;
  viewMode?: string;
  onViewModeChange?: (mode: string) => void;
}

export function CSVUploadExport({ onUpload, onExport, entityName, viewMode, onViewModeChange }: CSVUploadExportProps) {
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (file.type !== "text/csv" && !file.name.endsWith(".csv")) {
      toast({
        title: "Invalid file format",
        description: "Please upload a CSV file",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);
    try {
      await onUpload(file);
      toast({
        title: "Upload successful",
        description: `${entityName} imported successfully`,
      });
    } catch (error) {
      console.error("Upload error:", error);
      toast({
        title: "Upload failed",
        description: `Failed to import ${entityName.toLowerCase()}: ${(error as Error).message}`,
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  return (
    <div className="flex items-center gap-2">
      <input
        type="file"
        accept=".csv"
        onChange={handleFileChange}
        ref={fileInputRef}
        className="hidden"
        id="csv-upload"
      />
      <Button
        variant="outline"
        size="sm"
        onClick={() => fileInputRef.current?.click()}
        disabled={isUploading}
      >
        {isUploading ? (
          <FileWarning className="h-4 w-4 mr-2 animate-pulse" />
        ) : (
          <Upload className="h-4 w-4 mr-2" />
        )}
        Import CSV
      </Button>
      <Button variant="outline" size="sm" onClick={onExport}>
        <Download className="h-4 w-4 mr-2" />
        Export CSV
      </Button>

      {viewMode !== undefined && onViewModeChange && (
        <div className="flex gap-2 p-1 rounded-lg bg-muted ml-2">
          <Button
            variant={viewMode === "list" ? "default" : "ghost"}
            size="sm"
            onClick={() => onViewModeChange("list")}
            aria-label="Switch to list view"
            title="Switch to list view"
          >
            <List className="h-4 w-4 mr-2" />
            List
          </Button>
          <Button
            variant={viewMode === "table" ? "default" : "ghost"}
            size="sm"
            onClick={() => onViewModeChange("table")}
            aria-label="Switch to table view"
            title="Switch to table view"
          >
            <Table className="h-4 w-4 mr-2" />
            Table
          </Button>
          <Button
            variant={viewMode === "kanban" ? "default" : "ghost"}
            size="sm"
            onClick={() => onViewModeChange("kanban")}
            aria-label="Switch to kanban view"
            title="Switch to kanban view"
          >
            <LayoutGrid className="h-4 w-4 mr-2" />
            Kanban
          </Button>
        </div>
      )}
    </div>
  );
}
