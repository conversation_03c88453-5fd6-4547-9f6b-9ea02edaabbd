import React, { useState, useMemo, useEffect } from 'react';
import { ColDef, GridApi } from 'ag-grid-community';
import { Search, RotateCcw, Eye, EyeOff, Columns } from 'lucide-react';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useColumnVisibility, ColumnInfo } from '@/contexts/ColumnVisibilityContext';

interface ColumnVisibilityPanelProps {
  isOpen: boolean;
  onClose: () => void;
  columns: ColDef[];
  gridApi?: GridApi;
}

export const ColumnVisibilityPanel: React.FC<ColumnVisibilityPanelProps> = ({
  isOpen,
  onClose,
  columns,
  gridApi
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const {
    columnVisibility,
    setColumnVisibility,
    toggleColumnVisibility,
    resetToDefaults,
    getVisibleColumns
  } = useColumnVisibility();

  // Get column information
  const columnInfo = useMemo(() => getVisibleColumns(columns), [columns, getVisibleColumns]);

  // Filter columns based on search term
  const filteredColumns = useMemo(() => {
    if (!searchTerm.trim()) return columnInfo;

    const searchLower = searchTerm.toLowerCase();
    return columnInfo.filter(col =>
      col.headerName.toLowerCase().includes(searchLower) ||
      col.field.toLowerCase().includes(searchLower)
    );
  }, [columnInfo, searchTerm]);

  // Group columns by pinned status
  const groupedColumns = useMemo(() => {
    const pinned = filteredColumns.filter(col => col.pinned);
    const unpinned = filteredColumns.filter(col => !col.pinned);

    return {
      pinned: pinned.sort((a, b) => a.headerName.localeCompare(b.headerName)),
      unpinned: unpinned.sort((a, b) => a.headerName.localeCompare(b.headerName))
    };
  }, [filteredColumns]);

  // Calculate visibility stats
  const visibilityStats = useMemo(() => {
    const total = columnInfo.length;
    const visible = columnInfo.filter(col => col.visible).length;
    return { total, visible, hidden: total - visible };
  }, [columnInfo]);

  // Handle column visibility change
  const handleColumnToggle = (columnField: string, checked: boolean) => {
    setColumnVisibility(columnField, checked);

    // Update AG Grid column visibility
    if (gridApi) {
      gridApi.setColumnsVisible([columnField], checked);
    }
  };

  // Handle show all columns
  const handleShowAll = () => {
    columnInfo.forEach(col => {
      setColumnVisibility(col.field, true);
      if (gridApi) {
        gridApi.setColumnsVisible([col.field], true);
      }
    });
  };

  // Handle hide all columns
  const handleHideAll = () => {
    columnInfo.forEach(col => {
      setColumnVisibility(col.field, false);
      if (gridApi) {
        gridApi.setColumnsVisible([col.field], false);
      }
    });
  };

  // Handle reset to defaults
  const handleReset = () => {
    resetToDefaults();

    // Update AG Grid to match defaults
    if (gridApi) {
      columns.forEach(col => {
        if (col.field) {
          const defaultVisible = col.hide !== true;
          gridApi.setColumnsVisible([col.field], defaultVisible);
        }
      });
    }
  };

  // Clear search when panel closes
  useEffect(() => {
    if (!isOpen) {
      setSearchTerm('');
    }
  }, [isOpen]);

  // Render column item
  const renderColumnItem = (col: ColumnInfo) => (
    <div
      key={col.field}
      className="flex items-center justify-between p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
    >
      <div className="flex items-center space-x-3 flex-1 min-w-0">
        <Checkbox
          id={`column-${col.field}`}
          checked={col.visible}
          onCheckedChange={(checked) => handleColumnToggle(col.field, checked as boolean)}
          className="shrink-0"
        />
        <div className="flex-1 min-w-0">
          <label
            htmlFor={`column-${col.field}`}
            className="text-sm font-medium cursor-pointer block truncate"
          >
            {col.headerName}
          </label>
          <p className="text-xs text-muted-foreground truncate">
            {col.field}
          </p>
        </div>
      </div>
      <div className="flex items-center gap-2 shrink-0">
        {col.pinned && (
          <Badge variant="secondary" className="text-xs">
            {col.pinned}
          </Badge>
        )}
        {col.visible ? (
          <Eye className="h-4 w-4 text-green-600" />
        ) : (
          <EyeOff className="h-4 w-4 text-muted-foreground" />
        )}
      </div>
    </div>
  );

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-[400px] sm:w-[500px] p-0">
        <div className="flex flex-col h-full">
          {/* Header */}
          <SheetHeader className="p-6 pb-4">
            <div className="flex items-center gap-2">
              <Columns className="h-5 w-5" />
              <SheetTitle>Column Visibility</SheetTitle>
            </div>
            <SheetDescription>
              Show or hide columns in the table. Changes are saved automatically.
            </SheetDescription>
          </SheetHeader>

          {/* Stats and Controls */}
          <div className="px-6 pb-4 space-y-4">
            {/* Visibility Stats */}
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  {visibilityStats.visible} visible
                </Badge>
                <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
                  {visibilityStats.hidden} hidden
                </Badge>
              </div>
              <span className="text-muted-foreground">
                of {visibilityStats.total} total
              </span>
            </div>

            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search columns..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleShowAll}
                className="flex-1"
              >
                <Eye className="h-4 w-4 mr-2" />
                Show All
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleHideAll}
                className="flex-1"
              >
                <EyeOff className="h-4 w-4 mr-2" />
                Hide All
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleReset}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
            </div>
          </div>

          <Separator />

          {/* Column List */}
          <ScrollArea className="flex-1 px-6">
            <div className="py-4 space-y-4">
              {/* Pinned Columns */}
              {groupedColumns.pinned.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-3">
                    Pinned Columns
                  </h4>
                  <div className="space-y-2">
                    {groupedColumns.pinned.map(renderColumnItem)}
                  </div>
                </div>
              )}

              {/* Regular Columns */}
              {groupedColumns.unpinned.length > 0 && (
                <div>
                  {groupedColumns.pinned.length > 0 && (
                    <h4 className="text-sm font-medium text-muted-foreground mb-3 mt-6">
                      Regular Columns
                    </h4>
                  )}
                  <div className="space-y-2">
                    {groupedColumns.unpinned.map(renderColumnItem)}
                  </div>
                </div>
              )}

              {/* No Results */}
              {filteredColumns.length === 0 && searchTerm && (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">
                    No columns found matching "{searchTerm}"
                  </p>
                </div>
              )}
            </div>
          </ScrollArea>
        </div>
      </SheetContent>
    </Sheet>
  );
};
