import React from 'react';
import { Columns } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface ColumnVisibilityTriggerProps {
  onClick: () => void;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  showLabel?: boolean;
}

export const ColumnVisibilityTrigger: React.FC<ColumnVisibilityTriggerProps> = ({
  onClick,
  variant = 'outline',
  size = 'sm',
  className,
  showLabel = false
}) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={variant}
            size={size}
            onClick={onClick}
            className={className}
            aria-label="Column visibility"
          >
            <Columns className="h-4 w-4" />
            {showLabel && <span className="ml-2">Columns</span>}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Show/hide columns</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
