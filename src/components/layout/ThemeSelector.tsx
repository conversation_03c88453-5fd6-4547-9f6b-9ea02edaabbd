import React from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import type { ThemeStyle, ThemeMode } from '@/contexts/ThemeContext';
import { Button } from '@/components/ui/button';
import { Check, Palette } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { cn } from '@/lib/utils';

// Theme definitions
interface ThemeDefinition {
  id: ThemeStyle;
  name: string;
  description: string;
  modes: {
    light: {
      primaryColor: string;
      bgColor: string;
      textColor: string;
    };
    dark: {
      primaryColor: string;
      bgColor: string;
      textColor: string;
    };
  };
  category: 'Default' | 'Custom';
}

// Define available themes
const availableThemes: ThemeDefinition[] = [
  {
    id: 'theme-default',
    name: 'Default',
    description: 'Default theme with neutral colors',
    category: 'Default',
    modes: {
      light: {
        primaryColor: 'bg-[#343434]',
        bgColor: 'bg-[#ffffff]',
        textColor: 'text-[#252525]',
      },
      dark: {
        primaryColor: 'bg-[#fafafa]',
        bgColor: 'bg-[#09090b]',
        textColor: 'text-[#fafafa]',
      }
    }
  },
  {
    id: 'theme-tangerine',
    name: 'Tangerine',
    description: 'Warm tangerine theme with orange accents',
    category: 'Custom',
    modes: {
      light: {
        primaryColor: 'bg-[#ea6d2a]',
        bgColor: 'bg-[#faf9f5]',
        textColor: 'text-[#3d3929]',
      },
      dark: {
        primaryColor: 'bg-[#ea6d2a]',
        bgColor: 'bg-[#262624]',
        textColor: 'text-[#c3c0b6]',
      }
    }
  },
  {
    id: 'theme-stargety',
    name: 'Stargety',
    description: 'Fresh green theme with modern aesthetics',
    category: 'Custom',
    modes: {
      light: {
        primaryColor: 'bg-[#8caf3c]',
        bgColor: 'bg-[#faf9f5]',
        textColor: 'text-[#3d3929]',
      },
      dark: {
        primaryColor: 'bg-[#8caf3c]',
        bgColor: 'bg-[#292929]',
        textColor: 'text-[#bfbfbf]',
      }
    }
  },
  {
    id: 'theme-modern',
    name: 'Modern',
    description: 'Clean modern theme with blue accents',
    category: 'Custom',
    modes: {
      light: {
        primaryColor: 'bg-[#3b82f6]',
        bgColor: 'bg-[#ffffff]',
        textColor: 'text-[#333333]',
      },
      dark: {
        primaryColor: 'bg-[#3b82f6]',
        bgColor: 'bg-[#171717]',
        textColor: 'text-[#e5e5e5]',
      }
    }
  },
  {
    id: 'theme-cosmic',
    name: 'Cosmic',
    description: 'Cosmic theme with purple accents',
    category: 'Custom',
    modes: {
      light: {
        primaryColor: 'bg-[#6e56cf]',
        bgColor: 'bg-[#f5f5ff]',
        textColor: 'text-[#2a2a4a]',
      },
      dark: {
        primaryColor: 'bg-[#a48fff]',
        bgColor: 'bg-[#0f0f1a]',
        textColor: 'text-[#e2e2f5]',
      }
    }
  }
];

// Convert theme definitions to theme options for the UI
const generateThemeOptions = () => {
  const options: ThemeOption[] = [];

  availableThemes.forEach(theme => {
    // Add light mode option
    options.push({
      name: `${theme.name} Light`,
      value: theme.id,
      description: theme.description,
      primaryColor: theme.modes.light.primaryColor,
      bgColor: theme.modes.light.bgColor,
      textColor: theme.modes.light.textColor,
      category: theme.category,
      isDark: false
    });

    // Add dark mode option
    options.push({
      name: `${theme.name} Dark`,
      value: theme.id,
      description: `Dark mode of the ${theme.name} theme`,
      primaryColor: theme.modes.dark.primaryColor,
      bgColor: theme.modes.dark.bgColor,
      textColor: theme.modes.dark.textColor,
      category: theme.category,
      isDark: true
    });
  });

  return options;
};

// Theme option interface for UI components
interface ThemeOption {
  name: string;
  value: string;
  description: string;
  primaryColor: string;
  bgColor: string;
  textColor: string;
  category: string;
  isDark: boolean;
}

// Generate theme options
const themeOptions: ThemeOption[] = generateThemeOptions();

// Theme card component for the dialog
interface ThemeCardProps {
  option: ThemeOption;
  isSelected: boolean;
  onSelect: (value: string, isDark: boolean, event: React.MouseEvent) => void;
}

const ThemeCard: React.FC<ThemeCardProps> = ({ option, isSelected, onSelect }) => {
  return (
    <div
      className={cn(
        "flex flex-col gap-2 p-4 rounded-lg border cursor-pointer transition-all",
        isSelected ? "border-primary ring-2 ring-primary/20" : "hover:border-primary/50",
        option.bgColor,
        option.textColor
      )}
      onClick={(e) => onSelect(option.value, option.isDark, e)}
    >
      <div className="flex items-center justify-between">
        <h3 className="font-medium">{option.name}</h3>
        {isSelected && <Check className="h-4 w-4" />}
      </div>
      <div className="text-sm opacity-80">{option.description}</div>
      <div className="flex gap-2 mt-2">
        <div className={cn("w-6 h-6 rounded-full", option.primaryColor)} />
        <div className={cn("w-6 h-6 rounded-full border", option.bgColor)} />
        <div className={cn("w-6 h-6 rounded-full border bg-card")} />
      </div>
    </div>
  );
};

// Theme button component for compact display
interface ThemeButtonProps {
  option: ThemeOption;
  isSelected: boolean;
  onSelect: (value: string, isDark: boolean, event: React.MouseEvent) => void;
}

const ThemeButton: React.FC<ThemeButtonProps> = ({ option, isSelected, onSelect }) => {
  return (
    <Button
      key={`${option.value}-${option.isDark ? 'dark' : 'light'}`}
      variant={isSelected ? "default" : "outline"}
      size="sm"
      className="gap-2"
      onClick={(e) => onSelect(option.value, option.isDark, e)}
    >
      <div className={`w-3 h-3 rounded-full ${option.primaryColor}`} />
      {option.name}
      {isSelected && <Check className="h-3 w-3 ml-1" />}
    </Button>
  );
};

// Main ThemeSelector component props
interface ThemeSelectorProps {
  showTrigger?: boolean;
  buttonText?: string;
}

export function ThemeSelector({ showTrigger = true, buttonText = "Themes" }: ThemeSelectorProps) {
  const { themeMode, themeStyle, setTheme } = useTheme();
  const [open, setOpen] = React.useState(false);

  /**
   * Handles theme selection and applies dark mode if needed
   * @param themeValue The theme style to apply (e.g., 'theme-default')
   * @param isDark Whether to apply dark mode
   * @param event The click event (used to prevent dialog from closing prematurely)
   */
  const handleSelectTheme = React.useCallback((themeValue: string, isDark: boolean = false, event?: React.MouseEvent) => {
    try {
      // Prevent event propagation to avoid dialog closing automatically
      if (event) {
        event.preventDefault();
        event.stopPropagation();
      }

      // Update the context state using the atomic setTheme function
      // This ensures both style and mode are updated together without race conditions
      setTheme(themeValue as ThemeStyle, isDark ? 'dark' : 'light');

      // Close the dialog immediately after applying the theme
      setOpen(false);
    } catch (error) {
      console.error('Error applying theme:', error);
    }
  }, [setTheme, setOpen]);

  /**
   * Handles dialog open/close state changes
   * @param newOpen Whether the dialog should be open
   */
  const handleOpenChange = React.useCallback((newOpen: boolean) => {
    setOpen(newOpen);
  }, []);

  // Memoize the theme options to prevent unnecessary re-renders
  const categorizedThemes = React.useMemo(() => {
    const result: Record<string, ThemeOption[]> = {};

    themeOptions.forEach(option => {
      if (!result[option.category]) {
        result[option.category] = [];
      }
      result[option.category].push(option);
    });

    return result;
  }, []);

  // Check if a theme option is currently selected
  const isThemeSelected = React.useCallback((option: ThemeOption): boolean => {
    return themeStyle === option.value &&
      (option.isDark ? themeMode === 'dark' : themeMode === 'light');
  }, [themeStyle, themeMode]);

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      {showTrigger ? (
        <DialogTrigger asChild>
          <Button variant="outline" size="sm" className="gap-2">
            <Palette className="h-4 w-4" />
            {buttonText}
          </Button>
        </DialogTrigger>
      ) : (
        <div className="flex flex-wrap gap-2">
          {themeOptions.map((option) => (
            <ThemeButton
              key={`${option.value}-${option.isDark ? 'dark' : 'light'}`}
              option={option}
              isSelected={isThemeSelected(option)}
              onSelect={handleSelectTheme}
            />
          ))}
        </div>
      )}

      <DialogContent className="sm:max-w-[650px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Select a theme</DialogTitle>
          <DialogDescription>
            Choose a theme that matches your style and preferences.
          </DialogDescription>
        </DialogHeader>

        {/* Group themes by category */}
        {Object.keys(categorizedThemes).map(category => (
          <div key={category} className="mb-6 last:mb-0">
            <h3 className="text-sm font-medium mb-3">{category} Themes</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {categorizedThemes[category].map((option) => (
                <ThemeCard
                  key={`${option.value}-${option.isDark ? 'dark' : 'light'}`}
                  option={option}
                  isSelected={isThemeSelected(option)}
                  onSelect={handleSelectTheme}
                />
              ))}
            </div>
          </div>
        ))}
      </DialogContent>
    </Dialog>
  );
}
