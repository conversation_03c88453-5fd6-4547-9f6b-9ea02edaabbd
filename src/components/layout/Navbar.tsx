
import { User } from "lucide-react";
import { NotificationDropdown } from "@/components/notifications/NotificationDropdown";
import { ThemeToggle } from "@/components/theme/ThemeToggle";
import GlobalSearch from "@/components/search/GlobalSearch";

export default function Navbar() {
  return (
    <header className="sticky top-0 z-30 flex h-16 w-full items-center justify-between border-b border-border bg-background/95 px-4 backdrop-blur transition-all duration-200">
      <div className="flex items-center gap-2">
        <div className="w-64 hidden md:block">
          <GlobalSearch />
        </div>
      </div>

      <div className="flex items-center gap-4">
        <ThemeToggle />
        <NotificationDropdown />

        <div className="h-8 w-8 overflow-hidden rounded-full bg-secondary">
          <User className="h-5 w-5 mx-auto my-1.5 text-muted-foreground" />
        </div>
      </div>
    </header>
  );
}
