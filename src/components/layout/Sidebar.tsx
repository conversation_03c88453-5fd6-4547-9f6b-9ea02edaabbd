
import { Link, useLocation } from "react-router-dom";
import { useSidebar } from "./SidebarProvider";
import { cn } from "@/lib/utils";
import {
  LayoutDashboard,
  Briefcase,
  Users,
  Calendar,
  Settings,
  ChevronLeft,
  ChevronRight,
  MessageSquare,
  PanelLeft,
  Bell,
  Building
} from "lucide-react";

type NavItem = {
  label: string;
  icon: React.ElementType;
  href: string;
};

const navigationItems: NavItem[] = [
  { label: "Dashboard", icon: LayoutDashboard, href: "/" },
  { label: "Jobs", icon: Briefcase, href: "/jobs" },
  { label: "Candidates", icon: Users, href: "/candidates" },
  { label: "Clients", icon: Building, href: "/clients" },
  { label: "Calendar", icon: Calendar, href: "/calendar" },
  { label: "Messages", icon: MessageSquare, href: "/messages" },
  { label: "Notifications", icon: Bell, href: "/notifications" },
  { label: "Settings", icon: Settings, href: "/settings" },
];

export default function Sidebar() {
  const { expanded, toggle } = useSidebar();
  const location = useLocation();

  return (
    <aside
      className={cn(
        "fixed inset-y-0 left-0 z-40 flex flex-col border-r border-border bg-card transition-all duration-300 ease-in-out",
        expanded ? "w-64" : "w-[70px]"
      )}
    >
      <div className="flex h-16 items-center justify-between px-4">
        <div className="flex items-center">
          {expanded ? (
            <span className="font-semibold text-lg tracking-tight">ATS Pro</span>
          ) : (
            <span className="font-semibold text-lg tracking-tight">ATS</span>
          )}
        </div>
      </div>

      <nav className="flex-1 space-y-1 px-3 py-4">
        {navigationItems.map((item) => {
          const isActive = location.pathname === item.href ||
            (item.href !== '/' && location.pathname.startsWith(item.href));

          return (
            <Link
              key={item.href}
              to={item.href}
              className={cn(
                "group flex items-center rounded-md px-3 py-2.5 text-sm font-medium transition-all duration-200",
                isActive
                  ? "bg-primary text-gray-100"
                  : "text-gray-400 hover:bg-muted hover:text-gray-500"
              )}
            >
              <item.icon
                className={cn(
                  "mr-3 h-5 w-5 flex-shrink-0 transition-all duration-200",
                  isActive ? "text-gray-200" : "text-gray-500 group-hover:text-gray-600"
                )}
              />
              <span className={cn(
                "transition-opacity duration-200",
                expanded ? "opacity-100" : "opacity-0"
              )}>
                {item.label}
              </span>
            </Link>
          );
        })}
      </nav>

      <div className="border-t border-border p-3">
        <button
          onClick={toggle}
          className="flex w-full items-center justify-center rounded-md p-2 text-gray-500 hover:bg-muted hover:text-gray-900 transition-colors"
        >
          {expanded ? (
            <ChevronLeft className="h-5 w-5" />
          ) : (
            <ChevronRight className="h-5 w-5" />
          )}
        </button>
      </div>
    </aside>
  );
}
