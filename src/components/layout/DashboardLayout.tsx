
import { ReactNode } from "react";
import Navbar from "./Navbar";
import Sidebar from "./Sidebar";
import { SidebarProvider } from "./SidebarProvider";
import { VersionDisplay } from "./VersionDisplay";
import { Toaster } from "@/components/ui/toaster";
import { DatabaseStatusAlert } from "@/components/common/DatabaseStatusAlert";


type DashboardLayoutProps = {
  children: ReactNode;
};

export default function DashboardLayout({ children }: DashboardLayoutProps) {


  return (
    <SidebarProvider>
      <div className="min-h-screen bg-background">
        <Sidebar />
        <div className="transition-all duration-300 pl-[70px] lg:pl-64">
          <Navbar />
          <main className="container mx-auto px-4 py-6 md:py-8 animate-fade-in max-w-7xl">
            {/* Database status alert */}
            <DatabaseStatusAlert />

            {/* Breadcrumb could be added here */}
            <div className="mb-2 text-sm text-muted-foreground">
              {/* Breadcrumb placeholder */}
            </div>
            {children}
          </main>
          <footer className="border-t mt-8">
            <div className="container mx-auto px-4 py-4 flex justify-between items-center max-w-7xl">
              <p className="text-xs text-muted-foreground">© 2023 ATS Dashboard. All rights reserved.</p>
              <VersionDisplay />
            </div>
          </footer>
        </div>
        <Toaster />
      </div>
    </SidebarProvider>
  );
}
