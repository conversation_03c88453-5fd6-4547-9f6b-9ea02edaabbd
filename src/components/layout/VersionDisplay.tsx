import React, { useState, useEffect } from 'react';
import { Info } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface VersionInfo {
  version: string;
  lastUpdated: string;
  changelog: Array<{
    version: string;
    date: string;
    changes: string[];
  }>;
}

export const VersionDisplay: React.FC = () => {
  const [versionInfo, setVersionInfo] = useState<VersionInfo | null>(null);

  useEffect(() => {
    // In a real application, this would be an API call
    // For now, we'll import the version.json file directly
    fetch('/version.json')
      .then(response => response.json())
      .then(data => {
        setVersionInfo(data);
      })
      .catch(error => {
        console.error('Failed to load version information:', error);
      });
  }, []);

  if (!versionInfo || !versionInfo.changelog || !Array.isArray(versionInfo.changelog) || versionInfo.changelog.length === 0) {
    // Return a simple version display without changelog if data is incomplete
    return (
      <div className="text-xs text-muted-foreground">
        {versionInfo?.version ? `v${versionInfo.version}` : 'v1.0.0'}
      </div>
    );
  }

  const latestChanges = versionInfo.changelog[0]?.changes || [];

  return (
    <div className="fixed bottom-2 right-2 text-xs text-muted-foreground flex items-center gap-1">
      <span>v{versionInfo.version}</span>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <button className="inline-flex items-center justify-center rounded-full p-1 hover:bg-muted">
              <Info className="h-3 w-3" />
              <span className="sr-only">Version info</span>
            </button>
          </TooltipTrigger>
          <TooltipContent side="top" className="max-w-xs">
            <div className="space-y-2">
              <p className="font-medium">Version {versionInfo.version}</p>
              <p className="text-xs">Last updated: {versionInfo.lastUpdated}</p>
              <div className="text-xs">
                <p className="font-medium">Latest changes:</p>
                <ul className="list-disc pl-4 space-y-1 mt-1">
                  {latestChanges.map((change, index) => (
                    <li key={index}>{change}</li>
                  ))}
                </ul>
              </div>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
};
