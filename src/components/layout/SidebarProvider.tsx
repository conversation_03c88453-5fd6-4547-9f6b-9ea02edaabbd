
import { createContext, useState, useEffect, useContext, ReactNode } from 'react';

interface SidebarContextType {
  expanded: boolean;
  toggle: () => void;
  setExpanded: (state: boolean) => void;
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

export const useSidebar = () => {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
};

export const SidebarProvider = ({ children }: { children: ReactNode }) => {
  const [expanded, setExpanded] = useState(true);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Automatically collapse on mobile
  useEffect(() => {
    if (isMobile) {
      setExpanded(false);
    } else {
      setExpanded(true);
    }
  }, [isMobile]);

  const toggle = () => setExpanded(prev => !prev);

  return (
    <SidebarContext.Provider value={{ expanded, toggle, setExpanded }}>
      {children}
    </SidebarContext.Provider>
  );
};
