import { Bell } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useState } from "react"
import { <PERSON> } from "react-router-dom"

interface Notification {
  id: string;
  title: string;
  message: string;
  read: boolean;
  timestamp: Date;
}

export function NotificationCenter() {
  const [notifications, setNotifications] = useState<Notification[]>([])

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    )
  }

  const unreadCount = notifications.filter(n => !n.read).length

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-red-500 text-xs text-white flex items-center justify-center">
              {unreadCount}
            </span>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        {notifications.slice(0, 10).map(notification => (
          <DropdownMenuItem key={notification.id} className="flex flex-col items-start p-4">
            <div className="font-semibold">{notification.title}</div>
            <div className="text-sm text-gray-500">{notification.message}</div>
            {!notification.read && (
              <Button variant="ghost" size="sm" onClick={() => markAsRead(notification.id)}>
                Mark as read
              </Button>
            )}
          </DropdownMenuItem>
        ))}
        <Link to="/notifications" className="block p-2 text-center text-sm text-blue-500 hover:bg-gray-100">
          View all notifications
        </Link>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}