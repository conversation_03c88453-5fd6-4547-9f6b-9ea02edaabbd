import React from 'react';
import { Notification } from '@/types/notification';
import { formatDistanceToNow } from 'date-fns';
import {
  Bell,
  MessageSquare,
  Calendar,
  CheckSquare,
  AlertCircle,
  Check
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead: (id: string) => void;
  showActions?: boolean;
  selected?: boolean;
  onSelect?: (id: string) => void;
}

export const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onMarkAsRead,
  showActions = true,
  selected = false,
  onSelect
}) => {
  const navigate = useNavigate();

  // Format the timestamp to a relative time (e.g., "2 hours ago")
  const formattedTime = formatDistanceToNow(new Date(notification.timestamp), { addSuffix: true });

  // Get the appropriate icon based on notification type
  const getIcon = () => {
    switch (notification.type) {
      case 'message':
        return <MessageSquare className="h-5 w-5 text-blue-500" />;
      case 'interview':
        return <Calendar className="h-5 w-5 text-purple-500" />;
      case 'task':
        return <CheckSquare className="h-5 w-5 text-green-500" />;
      case 'system':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      case 'application':
      default:
        return <Bell className="h-5 w-5 text-amber-500" />;
    }
  };

  // Handle click on the notification
  const handleClick = () => {
    if (!notification.read) {
      onMarkAsRead(notification.id);
    }

    if (notification.actionUrl) {
      navigate(notification.actionUrl);
    }
  };

  // Handle selection for bulk actions
  const handleSelect = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onSelect) {
      onSelect(notification.id);
    }
  };

  // Handle mark as read button click
  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation();
    onMarkAsRead(notification.id);
  };

  return (
    <div
      className={cn(
        "flex flex-col sm:flex-row items-start p-4 gap-3 hover:bg-muted/50 cursor-pointer transition-colors rounded-md border border-transparent",
        notification.read ? "opacity-80" : "bg-muted/30 hover:border-border",
        selected && "bg-muted border-border"
      )}
      onClick={handleClick}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => e.key === 'Enter' && handleClick()}
      aria-label={`${notification.title}: ${notification.message}${!notification.read ? ' (unread)' : ''}`}
    >
      <div className="flex w-full gap-3">
        {onSelect && (
          <div
            className="flex items-center h-full pt-1"
            onClick={handleSelect}
            role="checkbox"
            aria-checked={selected}
            tabIndex={0}
            onKeyDown={(e) => e.key === 'Enter' && onSelect && onSelect(notification.id)}
          >
            <div className={cn(
              "w-4 h-4 rounded-sm border border-input flex items-center justify-center",
              selected && "bg-primary border-primary"
            )}>
              {selected && <Check className="h-3 w-3 text-primary-foreground" />}
            </div>
          </div>
        )}

        <div className="flex-shrink-0 mt-1">
          {getIcon()}
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
            <h4 className={cn(
              "text-sm font-medium",
              !notification.read && "font-semibold"
            )}>
              {notification.title}
              {!notification.read && (
                <span className="ml-2 inline-flex items-center rounded-full bg-primary/10 px-1.5 py-0.5 text-xs font-medium text-primary ring-1 ring-inset ring-primary/20">
                  New
                </span>
              )}
            </h4>
            <span className="text-xs text-muted-foreground sm:ml-2 whitespace-nowrap mt-1 sm:mt-0">
              {formattedTime}
            </span>
          </div>

          <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
            {notification.message}
          </p>

          {notification.relatedTo && (
            <div className="text-xs text-muted-foreground mt-1 flex items-center">
              <span className="font-medium mr-1">Related to:</span> {notification.relatedTo.name}
            </div>
          )}
        </div>
      </div>

      {showActions && !notification.read && (
        <div className="mt-2 sm:mt-0 ml-0 sm:ml-auto">
          <Button
            variant="outline"
            size="sm"
            className="flex-shrink-0 h-8 px-3 text-xs w-full sm:w-auto"
            onClick={handleMarkAsRead}
          >
            Mark as read
          </Button>
        </div>
      )}
    </div>
  );
};
