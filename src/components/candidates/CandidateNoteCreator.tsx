/**
 * Component for creating candidate notes
 * Updated to use the unified notes system while maintaining backward compatibility
 */

import React from 'react';
import { NoteCreator } from '@/components/notes/NoteCreator';
import { CandidateNote } from '@/types/note';

interface CandidateNoteCreatorProps {
  candidateId: string;
  onNoteAdded?: (noteId: string) => void;
  notesCount?: number;
  isSubmitting?: boolean;
  onAddNote: (content: string) => Promise<CandidateNote | null>;
}

export function CandidateNoteCreator({
  candidateId,
  onNoteAdded,
  notesCount = 0,
  isSubmitting = false,
  onAddNote
}: CandidateNoteCreatorProps) {
  const handleNoteAdded = (note: CandidateNote) => {
    // Maintain backward compatibility with the old interface
    onNoteAdded?.(note.id);
  };

  return (
    <NoteCreator<CandidateNote>
      entityType="candidate"
      entityId={candidateId}
      onNoteAdded={handleNoteAdded}
      notesCount={notesCount}
      isSubmitting={isSubmitting}
      onAddNote={onAddNote}
    />
  );
}
