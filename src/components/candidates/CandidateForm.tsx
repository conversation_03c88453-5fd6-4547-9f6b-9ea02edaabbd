import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Candidate } from '@/types/candidate';
import {
  PrimaryStatus,
  SECONDARY_STATUS_OPTIONS,
  PRIMARY_STATUSES
} from '@/lib/constants/candidateStatus';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { TagInput } from '@/components/ui/tag-input';
import { generateCandidateId } from '@/utils/idGenerators';
import { isValidUrl, normalizeUrl } from '@/utils/urlUtils';

// Custom URL validator using our flexible URL validation
const urlSchema = z.string().refine(
  (val) => val === '' || isValidUrl(val),
  { message: 'Invalid URL format' }
);

// Score validator for assessment scores (0-10)
const scoreSchema = z.number().min(0).max(10).optional();

// Esquema de validación para el formulario
const candidateFormSchema = z.object({
  // Basic Information
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().optional(),
  email: z.string().email('Invalid email address'),
  phone: z.string().optional(),
  location: z.string().optional(),

  // Professional Information
  position: z.string().min(2, 'Position must be at least 2 characters'),
  experienceYears: z.number().int().min(0).optional(),
  education: z.string().optional(),
  currentCompany: z.string().optional(),
  currentPosition: z.string().optional(),
  desiredSalary: z.number().optional(),
  salaryCurrency: z.string().optional(),
  availabilityDate: z.string().optional(),
  source: z.string().optional(),
  skills: z.array(z.string()).optional(),

  // Status Information
  status: z.enum(Object.values(PrimaryStatus) as [string, ...string[]]),
  secondaryStatus: z.string().optional(),
  englishLevel: z.string().optional(),

  // URLs and Links
  portfolio: urlSchema.optional(),
  resumeUrl: urlSchema.optional(),
  coverLetterUrl: urlSchema.optional(),
  socialLinks: z.object({
    linkedin: urlSchema.optional(),
    github: urlSchema.optional(),
    twitter: urlSchema.optional()
  }).optional(),

  // Interview & Assessment
  interviewScore: scoreSchema,
  interviewNotes: z.string().optional(),
  challenge: z.string().optional(),
  challengeNotes: z.string().optional(),
  challengeFeedback: z.string().optional(),

  // Scoring
  driveScore: scoreSchema,
  resilienceScore: scoreSchema,
  collaborationScore: scoreSchema,

  // Additional Fields
  notes: z.string().optional(),
  stargetyId: z.string().optional(),
  isDuplicate: z.string().optional(),
  result: z.string().optional()
});

type CandidateFormValues = z.infer<typeof candidateFormSchema>;

interface CandidateFormProps {
  candidate?: Candidate;
  onSubmit: (data: CandidateFormValues) => void;
  isLoading?: boolean;
}

export default function CandidateForm({ candidate, onSubmit, isLoading = false }: CandidateFormProps) {
  const [formError, setFormError] = useState<string | null>(null);
  const [generatedId, setGeneratedId] = useState<string | null>(null);

  // Split name into first and last name if available
  const getNameParts = (fullName: string = '') => {
    const parts = fullName.trim().split(' ');
    return {
      firstName: parts[0] || '',
      lastName: parts.slice(1).join(' ') || ''
    };
  };

  const { firstName, lastName } = getNameParts(candidate?.name);

  // Generate ID preview when phone number changes
  const updateIdPreview = (firstName: string, lastName: string, phone: string) => {
    if (firstName && lastName && phone) {
      const id = generateCandidateId(firstName, lastName, phone);
      setGeneratedId(id);
    } else {
      setGeneratedId(null);
    }
  };

  // Helper function to extract data from intakeResponses
  const getIntakeValue = (question: string): string => {
    const response = candidate?.intakeResponses?.find(r =>
      r.question.toLowerCase().includes(question.toLowerCase())
    );
    return response?.answer || '';
  };

  // Helper function to parse numeric values safely
  const parseNumeric = (value: string | undefined): number | undefined => {
    if (!value || value === 'Not specified') return undefined;
    const parsed = parseFloat(value.replace(/[^\d.-]/g, ''));
    return isNaN(parsed) ? undefined : parsed;
  };

  // Helper function to parse date values safely for HTML date inputs
  const parseDate = (value: string | undefined): string => {
    if (!value || value === 'Not specified' || value === 'N/A') return '';

    try {
      // Try to parse the date
      const date = new Date(value);
      if (isNaN(date.getTime())) return '';

      // Return in yyyy-MM-dd format for HTML date input
      return date.toISOString().split('T')[0];
    } catch (error) {
      return '';
    }
  };

  // Inicializar el formulario con valores por defecto o del candidato existente
  const form = useForm<CandidateFormValues>({
    resolver: zodResolver(candidateFormSchema),
    defaultValues: {
      // Basic Information
      firstName: firstName,
      lastName: lastName,
      email: candidate?.email || '',
      phone: candidate?.phone || '',
      location: candidate?.location || '',

      // Professional Information
      position: candidate?.position || '',
      experienceYears: parseNumeric(getIntakeValue('experience')),
      education: getIntakeValue('education'),
      currentCompany: candidate?.currentCompany || '',
      currentPosition: candidate?.currentPosition || '',
      desiredSalary: parseNumeric(getIntakeValue('salary')),
      salaryCurrency: getIntakeValue('salary')?.includes('EUR') ? 'EUR' :
                     getIntakeValue('salary')?.includes('GBP') ? 'GBP' : 'USD',
      availabilityDate: parseDate(getIntakeValue('availability')),
      source: getIntakeValue('source'),
      skills: candidate?.skills || [], // Populate from database if available

      // Status Information
      status: candidate?.status || PrimaryStatus.NEW,
      secondaryStatus: candidate?.secondaryStatus || '',
      englishLevel: candidate?.englishLevel || '',

      // URLs and Links
      portfolio: candidate?.portfolio || '',
      resumeUrl: candidate?.resume || '',
      coverLetterUrl: candidate?.coverLetter || '',
      socialLinks: {
        linkedin: candidate?.socialLinks?.linkedin || '',
        github: candidate?.socialLinks?.github || '',
        twitter: candidate?.socialLinks?.twitter || ''
      },

      // Interview & Assessment
      interviewScore: candidate?.interviewScore || undefined,
      interviewNotes: candidate?.interviewNotes || '',
      challenge: candidate?.challenge || '',
      challengeNotes: candidate?.challengeNotes || '',
      challengeFeedback: candidate?.challengeFeedback || '',

      // Scoring
      driveScore: candidate?.driveScore || undefined,
      resilienceScore: candidate?.resilienceScore || undefined,
      collaborationScore: candidate?.collaborationScore || undefined,

      // Additional Fields
      notes: candidate?.notes || '',
      stargetyId: candidate?.stargetyId || '',
      isDuplicate: candidate?.isDuplicate || 'new',
      result: candidate?.result || ''
    },
  });

  // Watch form fields to update ID preview and secondary status options
  const watchFirstName = form.watch('firstName');
  const watchLastName = form.watch('lastName');
  const watchPhone = form.watch('phone');
  const watchStatus = form.watch('status');

  // State to store available secondary status options based on primary status
  const [secondaryStatusOptions, setSecondaryStatusOptions] = useState<{ value: string, label: string }[]>([]);

  // Update ID preview when relevant fields change
  React.useEffect(() => {
    updateIdPreview(watchFirstName, watchLastName, watchPhone);

    // Automatically set the stargetyId field with the generated ID
    if (watchFirstName && watchLastName && watchPhone) {
      const id = generateCandidateId(watchFirstName, watchLastName, watchPhone);
      form.setValue('stargetyId', id);
    } else {
      form.setValue('stargetyId', '');
    }
  }, [watchFirstName, watchLastName, watchPhone, form]);

  // Update secondary status options when primary status changes
  React.useEffect(() => {
    if (watchStatus) {
      const options = SECONDARY_STATUS_OPTIONS[watchStatus as PrimaryStatus] || [];
      setSecondaryStatusOptions(options);

      // If current secondary status is not valid for the new primary status, reset it
      const currentSecondaryStatus = form.getValues('secondaryStatus');
      const isValidOption = options.some(option => option.value === currentSecondaryStatus);

      if (currentSecondaryStatus && !isValidOption) {
        form.setValue('secondaryStatus', '');
      }
    }
  }, [watchStatus, form]);

  // Handle form submission
  const handleSubmit = async (data: CandidateFormValues) => {
    try {
      setFormError(null);

      // Normalize URLs
      const normalizedData = {
        ...data,
        portfolio: data.portfolio ? normalizeUrl(data.portfolio) : '',
        resumeUrl: data.resumeUrl ? normalizeUrl(data.resumeUrl) : '',
        coverLetterUrl: data.coverLetterUrl ? normalizeUrl(data.coverLetterUrl) : '',
        socialLinks: data.socialLinks ? {
          linkedin: data.socialLinks.linkedin ? normalizeUrl(data.socialLinks.linkedin) : '',
          github: data.socialLinks.github ? normalizeUrl(data.socialLinks.github) : '',
          twitter: data.socialLinks.twitter ? normalizeUrl(data.socialLinks.twitter) : ''
        } : undefined,
        // Include the full name for compatibility with existing components
        name: `${data.firstName} ${data.lastName || ''}`.trim()
      };

      // Pass the data to the parent component
      onSubmit(normalizedData as any);
    } catch (error) {
      console.error('Error submitting candidate form:', error);
      setFormError('An error occurred while saving the candidate. Please try again.');
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{candidate ? 'Edit Candidate' : 'Add New Candidate'}</CardTitle>
      </CardHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)}>
          <CardContent className="space-y-8">
            {formError && (
              <div className="bg-destructive/15 text-destructive p-3 rounded-md text-sm">
                {formError}
              </div>
            )}

            {generatedId && (
              <div className="bg-green-50 text-green-800 p-3 rounded-md text-sm dark:bg-green-900/20 dark:text-green-300">
                <p><strong>Stargety ID Generated:</strong> {generatedId}</p>
                <p className="text-xs mt-1">This ID will be automatically saved to the database and used to identify the candidate in the Stargety system.</p>
              </div>
            )}

            {/* Personal Information Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">Personal Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name</FormLabel>
                      <FormControl>
                        <Input id="candidate-first-name" placeholder="John" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name</FormLabel>
                      <FormControl>
                        <Input id="candidate-last-name" placeholder="Doe" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input id="candidate-email" placeholder="<EMAIL>" type="email" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input id="candidate-phone" placeholder="+****************" {...field} />
                      </FormControl>
                      <FormDescription>
                        Used for ID generation and contact
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Location</FormLabel>
                      <FormControl>
                        <Input id="candidate-location" placeholder="City, Country" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Professional Information Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">Professional Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="position"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Current Position</FormLabel>
                      <FormControl>
                        <Input id="candidate-position" placeholder="Frontend Developer" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="currentCompany"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Current Company</FormLabel>
                      <FormControl>
                        <Input id="candidate-current-company" placeholder="Company Name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="experienceYears"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Years of Experience</FormLabel>
                      <FormControl>
                        <Input
                          id="candidate-experience-years"
                          type="number"
                          placeholder="5"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="education"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Education</FormLabel>
                      <FormControl>
                        <Input id="candidate-education" placeholder="Bachelor's in Computer Science" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="desiredSalary"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Desired Salary</FormLabel>
                      <FormControl>
                        <Input
                          id="candidate-desired-salary"
                          type="number"
                          placeholder="75000"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="salaryCurrency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Currency</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger id="candidate-salary-currency">
                            <SelectValue placeholder="Select currency" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="USD">USD</SelectItem>
                          <SelectItem value="EUR">EUR</SelectItem>
                          <SelectItem value="GBP">GBP</SelectItem>
                          <SelectItem value="CAD">CAD</SelectItem>
                          <SelectItem value="AUD">AUD</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="availabilityDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Availability Date</FormLabel>
                      <FormControl>
                        <Input id="candidate-availability-date" type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="source"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Source</FormLabel>
                      <FormControl>
                        <Input id="candidate-source" placeholder="LinkedIn, Referral, etc." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Skills Section */}
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="skills"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Skills</FormLabel>
                      <FormControl>
                        <TagInput
                          id="candidate-skills"
                          value={field.value || []}
                          onChange={field.onChange}
                          placeholder="Type skills and press Enter or comma to add..."
                        />
                      </FormControl>
                      <FormDescription>
                        Add technical skills, programming languages, frameworks, etc.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Status & Pipeline Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">Status & Pipeline</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Primary Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger id="candidate-primary-status">
                            <SelectValue placeholder="Select primary status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {PRIMARY_STATUSES.map((status) => (
                            <SelectItem key={status.value} value={status.value}>
                              {status.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        The main pipeline stage for this candidate
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="secondaryStatus"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Secondary Status</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        disabled={secondaryStatusOptions.length === 0}
                      >
                        <FormControl>
                          <SelectTrigger id="candidate-secondary-status">
                            <SelectValue placeholder="Select secondary status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {secondaryStatusOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Detailed status information within the primary stage
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="englishLevel"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>English Level</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger id="candidate-english-level">
                            <SelectValue placeholder="Select English level" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Beginner">Beginner</SelectItem>
                          <SelectItem value="Intermediate">Intermediate</SelectItem>
                          <SelectItem value="Advanced">Advanced</SelectItem>
                          <SelectItem value="Fluent">Fluent</SelectItem>
                          <SelectItem value="Native">Native</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* URLs & Links Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">URLs & Links</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="portfolio"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Portfolio URL</FormLabel>
                      <FormControl>
                        <Input id="candidate-portfolio" placeholder="https://portfolio.example.com" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="resumeUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Resume URL</FormLabel>
                      <FormControl>
                        <Input id="candidate-resume-url" placeholder="https://resume.example.com" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="coverLetterUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Cover Letter URL</FormLabel>
                      <FormControl>
                        <Input id="candidate-cover-letter-url" placeholder="https://coverletter.example.com" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-4">
                <h4 className="text-md font-medium">Social Links</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="socialLinks.linkedin"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>LinkedIn</FormLabel>
                        <FormControl>
                          <Input id="candidate-linkedin" placeholder="https://linkedin.com/in/username" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="socialLinks.github"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>GitHub</FormLabel>
                        <FormControl>
                          <Input id="candidate-github" placeholder="https://github.com/username" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="socialLinks.twitter"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Twitter</FormLabel>
                        <FormControl>
                          <Input id="candidate-twitter" placeholder="https://twitter.com/username" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>

            {/* Interview & Assessment Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">Interview & Assessment</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="interviewScore"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Interview Score (0-10)</FormLabel>
                      <FormControl>
                        <Input
                          id="candidate-interview-score"
                          type="number"
                          min="0"
                          max="10"
                          step="0.1"
                          placeholder="8.5"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="challenge"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Challenge</FormLabel>
                      <FormControl>
                        <Input id="candidate-challenge" placeholder="Technical challenge name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="interviewNotes"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Interview Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          id="candidate-interview-notes"
                          placeholder="Notes from the interview..."
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="challengeNotes"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Challenge Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          id="candidate-challenge-notes"
                          placeholder="Notes about the technical challenge..."
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="challengeFeedback"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Challenge Feedback</FormLabel>
                      <FormControl>
                        <Textarea
                          id="candidate-challenge-feedback"
                          placeholder="Feedback on the challenge submission..."
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Scoring Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">Scoring</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="driveScore"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Drive Score (0-10)</FormLabel>
                      <FormControl>
                        <Input
                          id="candidate-drive-score"
                          type="number"
                          min="0"
                          max="10"
                          step="0.1"
                          placeholder="7.5"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="resilienceScore"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Resilience Score (0-10)</FormLabel>
                      <FormControl>
                        <Input
                          id="candidate-resilience-score"
                          type="number"
                          min="0"
                          max="10"
                          step="0.1"
                          placeholder="8.0"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="collaborationScore"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Collaboration Score (0-10)</FormLabel>
                      <FormControl>
                        <Input
                          id="candidate-collaboration-score"
                          type="number"
                          min="0"
                          max="10"
                          step="0.1"
                          placeholder="9.0"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Additional Information Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">Additional Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="stargetyId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Stargety ID</FormLabel>
                      <FormControl>
                        <Input
                          id="candidate-stargety-id"
                          placeholder="Auto-generated from name and phone"
                          readOnly
                          className="bg-muted"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Automatically generated from first name, last name, and phone number
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="isDuplicate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Duplicate Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger id="candidate-duplicate-status">
                            <SelectValue placeholder="Select duplicate status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="new">New</SelectItem>
                          <SelectItem value="duplicate">Duplicate</SelectItem>
                          <SelectItem value="verified">Verified</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="result"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Result</FormLabel>
                      <FormControl>
                        <Input id="candidate-result" placeholder="Final result or outcome" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          id="candidate-notes"
                          placeholder="Additional information about this candidate..."
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-end gap-2">
            <Button variant="outline" type="button" onClick={() => window.history.back()}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : candidate ? 'Update Candidate' : 'Create Candidate'}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}
