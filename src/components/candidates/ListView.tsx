import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Plus, Users } from 'lucide-react';
import { Candidate, CandidateStatus } from '@/types/candidate';

interface ListViewProps {
  candidates: Candidate[];
  viewMode?: string;
  onViewModeChange?: (mode: string) => void;
}

export default function ListView({ candidates }: ListViewProps) {
  // Simple function to get status color
  const getStatusColor = (status: CandidateStatus) => {
    switch(status) {
      case 'new': return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
      case 'screening': return 'bg-purple-100 text-purple-800 hover:bg-purple-100';
      case 'interview': return 'bg-amber-100 text-amber-800 hover:bg-amber-100';
      case 'challenge': return 'bg-orange-100 text-orange-800 hover:bg-orange-100';
      case 'offer': return 'bg-green-100 text-green-800 hover:bg-green-100';
      case 'hired': return 'bg-emerald-100 text-emerald-800 hover:bg-emerald-100';
      case 'rejected': return 'bg-red-100 text-red-800 hover:bg-red-100';
      default: return '';
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Candidates ({candidates.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {candidates.length > 0 ? (
              candidates.map((candidate) => {
                const id = candidate.id;
                const name = candidate.name;
                const status = candidate.status || 'new';

                return (
                  <Link
                    key={id}
                    to={`/candidates/${id}`}
                    className="block"
                  >
                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>{name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <h4>{name}</h4>
                        <Badge variant="outline" className={getStatusColor(status as CandidateStatus)}>
                          {status}
                        </Badge>
                      </div>
                    </div>
                  </Link>
                );
              })
            ) : (
              <div className="text-center p-4">
                <Users className="h-8 w-8 mx-auto mb-2" />
                <p>No candidates found</p>
                <Button className="mt-4" asChild>
                  <Link to="/candidates/new">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Candidate
                  </Link>
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
