
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Assessment } from "@/types/candidate";
import { CheckCircle, Clock, XCircle } from "lucide-react";

interface AssessmentCardProps {
  assessment: Assessment;
  onEdit: (id: string) => void;
}

export default function AssessmentCard({ assessment, onEdit }: AssessmentCardProps) {
  const getStatusIcon = () => {
    switch (assessment.status) {
      case "passed":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "failed":
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-amber-500" />;
    }
  };

  const getAssessmentTitle = () => {
    switch (assessment.type) {
      case "english":
        return "English Interview";
      case "portfolio":
        return "Portfolio Review";
      case "challenge":
        return "Design Challenge";
    }
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg flex items-center gap-2">
            {getStatusIcon()}
            {getAssessmentTitle()}
          </CardTitle>
          <span className="text-sm font-medium rounded-full px-2 py-0.5 bg-secondary">
            {assessment.status.charAt(0).toUpperCase() + assessment.status.slice(1)}
          </span>
        </div>
      </CardHeader>
      <CardContent className="pb-4">
        {assessment.date && (
          <p className="text-sm text-muted-foreground mb-2">
            Date: {assessment.date}
          </p>
        )}
        {assessment.reviewer && (
          <p className="text-sm text-muted-foreground mb-2">
            Reviewer: {assessment.reviewer}
          </p>
        )}
        {assessment.score !== undefined && (
          <p className="text-sm mb-2">
            Score: <span className="font-medium">{assessment.score}/100</span>
          </p>
        )}
        {assessment.feedback && (
          <div className="mt-3">
            <h4 className="text-sm font-medium mb-1">Feedback:</h4>
            <p className="text-sm text-muted-foreground">{assessment.feedback}</p>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button variant="outline" size="sm" onClick={() => onEdit(assessment.id)}>
          Update Assessment
        </Button>
      </CardFooter>
    </Card>
  );
}
