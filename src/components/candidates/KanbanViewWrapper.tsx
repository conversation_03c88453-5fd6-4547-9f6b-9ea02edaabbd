import { useState } from 'react';
import { Candidate, CandidateStatus } from '@/types/candidate';
import { PrimaryStatus, PRIMARY_STATUSES, SECONDARY_STATUS_OPTIONS } from '@/lib/constants/candidateStatus';
import KanbanColumn from '@/components/kanban/KanbanColumn';
import KanbanCard from '@/components/kanban/KanbanCard';
import { useToast } from '@/hooks/use-toast';
import { candidatesApi } from '@/services/apiService';

interface KanbanViewWrapperProps {
  candidates: Candidate[];
  viewMode?: string;
  onViewModeChange?: (mode: string) => void;
}

// Define pipeline stages using the centralized status system
const candidateStages = PRIMARY_STATUSES.map(status => ({
  status: status.value as CandidateStatus,
  label: status.label
}));

export default function KanbanViewWrapper({ candidates: initialCandidates }: KanbanViewWrapperProps) {
  const [candidates, setCandidates] = useState<Candidate[]>(initialCandidates);
  const { toast } = useToast();

  // Use all candidates without filtering by search term
  const filteredCandidates = candidates;

  const handleCandidateMove = async (candidateId: string, newStatus: CandidateStatus) => {
    try {
      // Get default secondary status for the new primary status
      const secondaryStatusOptions = SECONDARY_STATUS_OPTIONS[newStatus as PrimaryStatus] || [];
      const defaultSecondaryStatus = secondaryStatusOptions.length > 0 ? secondaryStatusOptions[0].value : '';

      // Update UI immediately for better UX
      setCandidates(prev =>
        prev.map(candidate =>
          candidate.id === candidateId
            ? { ...candidate, status: newStatus, secondaryStatus: defaultSecondaryStatus }
            : candidate
        )
      );

      // Update in PostgreSQL via API
      await candidatesApi.update(candidateId, {
        status: newStatus,
        secondary_status: defaultSecondaryStatus
      });

      toast({
        title: 'Candidate moved',
        description: `Candidate has been moved to ${newStatus} stage`,
      });
    } catch (error) {
      console.error('Error moving candidate:', error);
      toast({
        title: 'Error',
        description: 'Failed to update candidate status. Please try again.',
        variant: 'destructive'
      });

      // Revert UI change on error
      setCandidates(initialCandidates);
    }
  };

  // Removed handleCandidateExport as it's now handled in the parent component

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="text-sm text-muted-foreground">
          <span className="font-medium">{filteredCandidates.length}</span> candidates across {candidateStages.length} stages
        </div>
      </div>

      <div className="flex gap-4 overflow-x-auto pb-6 snap-x">
        {candidateStages.map(stage => (
          <KanbanColumn
            key={stage.status}
            title={stage.label}
            count={filteredCandidates.filter(c => c.status === stage.status).length}

          >
            {filteredCandidates
              .filter(candidate => candidate.status === stage.status)
              .map(candidate => (
                <KanbanCard
                  key={candidate.id}
                  id={candidate.id}
                  title={candidate.name}
                  subtitle={candidate.position}
                  imageUrl={candidate.imageUrl}
                  metadata={[
                    { label: 'Applied', value: candidate.appliedDate },
                    { label: 'Email', value: candidate.email },
                    ...(candidate.secondaryStatus ? [{ label: 'Status', value: candidate.secondaryStatus }] : [])
                  ]}
                  onMove={(newStatus) => handleCandidateMove(candidate.id, newStatus as CandidateStatus)}
                  moveOptions={candidateStages}
                  linkTo={`/candidates/${candidate.id}`}
                />
              ))}
            {filteredCandidates.filter(candidate => candidate.status === stage.status).length === 0 && (
              <div className="p-4 text-center text-sm text-muted-foreground">
                <p>No candidates in this stage</p>
                <p className="mt-2">Drag candidates here or add a new one</p>
              </div>
            )}
          </KanbanColumn>
        ))}
      </div>
    </div>
  );
}
