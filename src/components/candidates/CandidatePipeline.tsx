
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CandidateStatus } from "@/types/candidate";
import {
  PrimaryStatus,
  PRIMARY_STATUSES,
  SECONDARY_STATUS_OPTIONS,
  getSecondaryStatusLabel
} from "@/lib/constants/candidateStatus";
import { ChevronRight, Check, X } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";

interface CandidatePipelineProps {
  currentStatus: CandidateStatus;
  secondaryStatus?: string;
  onStatusChange: (status: CandidateStatus) => void;
}

// Filter out rejected status for the pipeline visualization
const pipelineStages = PRIMARY_STATUSES.filter(
  stage => stage.value !== PrimaryStatus.REJECTED
).sort((a, b) => a.order - b.order);

export default function CandidatePipeline({
  currentStatus,
  secondaryStatus,
  onStatusChange
}: CandidatePipelineProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<CandidateStatus>(currentStatus);

  const handleStatusChange = (status: CandidateStatus) => {
    setSelectedStatus(status);
  };

  const saveChanges = () => {
    onStatusChange(selectedStatus);
    setIsEditing(false);
  };

  const cancelChanges = () => {
    setSelectedStatus(currentStatus);
    setIsEditing(false);
  };

  const getCurrentStageIndex = (status: CandidateStatus) => {
    const stageIndex = pipelineStages.findIndex(stage => stage.value === status);
    return stageIndex >= 0 ? stageIndex : 0; // Default to first stage if not found
  };

  const currentStageIndex = getCurrentStageIndex(currentStatus);
  const selectedStageIndex = getCurrentStageIndex(selectedStatus);

  // Get secondary status options for the current primary status
  const secondaryStatusOptions = SECONDARY_STATUS_OPTIONS[currentStatus as PrimaryStatus] || [];

  return (
    <div className="w-full">
      <div className="mb-2 flex items-center justify-between">
        <div>
          <h3 className="font-medium">Pipeline Stage</h3>
          {secondaryStatus && !isEditing && (
            <p className="text-xs text-muted-foreground mt-1">
              {getSecondaryStatusLabel(secondaryStatus)}
            </p>
          )}
        </div>
        {!isEditing ? (
          <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
            Change Stage
          </Button>
        ) : (
          <div className="flex space-x-2">
            <Button size="sm" variant="ghost" onClick={cancelChanges}>
              <X className="h-4 w-4 mr-1" /> Cancel
            </Button>
            <Button size="sm" onClick={saveChanges}>
              <Check className="h-4 w-4 mr-1" /> Save
            </Button>
          </div>
        )}
      </div>

      <div className="flex items-center w-full mb-6">
        {pipelineStages.map((stage, index) => (
          <div key={stage.value} className="flex items-center">
            <div className="flex flex-col items-center">
            <span className="text-xs mb-3">{stage.label}</span>
            <div
              className={cn(
                "rounded-full w-8 h-8 flex items-center justify-center text-xs font-medium transition-colors cursor-pointer",
                isEditing && "hover:bg-muted",
                (!isEditing && index <= currentStageIndex) || (isEditing && index <= selectedStageIndex)
                  ? "bg-primary text-primary-foreground"
                  : "bg-muted text-muted-foreground"
              )}
              onClick={() => isEditing && handleStatusChange(stage.value as CandidateStatus)}
            >
              {index + 1}
            </div>
            </div>
            <div className="flex flex-col items-center pt-4 mt-4">
              {index < pipelineStages.length - 1 && (
                <ChevronRight className={cn(
                  "h-4 w-4 mx-1",
                  (!isEditing && index < currentStageIndex) || (isEditing && index < selectedStageIndex)
                    ? "text-primary"
                    : "text-muted-foreground"
                )} />
              )}
            </div>
          </div>
        ))}
      </div>

      {isEditing && secondaryStatusOptions.length > 0 && (
        <div className="mt-4">
          <Label htmlFor="secondary-status" className="text-sm">Detailed Status</Label>
          <div className="mt-1">
            <Select disabled>
              <SelectTrigger id="secondary-status" className="w-full">
                <SelectValue placeholder="Secondary status will be set automatically" />
              </SelectTrigger>
              <SelectContent>
                {secondaryStatusOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground mt-1">
              Secondary status will be updated automatically when you change the primary status
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
