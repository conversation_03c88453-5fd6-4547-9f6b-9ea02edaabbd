/**
 * Complete notes section component
 * Combines note creator and timeline for any entity type
 */

import React, { useState } from 'react';
import { NoteCreator } from './NoteCreator';
import { NotesTimeline } from './NotesTimeline';
import { BaseNote, NoteEntityType } from '@/types/note';

interface NotesSectionProps<T extends BaseNote> {
  entityType: NoteEntityType;
  entityId: string;
  notes: T[];
  isLoading: boolean;
  isSubmitting: boolean;
  onAddNote: (content: string) => Promise<T | null>;
  onUpdateNote: (id: string, content: string) => Promise<T | null>;
  onDeleteNote: (id: string) => Promise<boolean>;
  showCreateInterface?: boolean;
  className?: string;
}

export function NotesSection<T extends BaseNote>({
  entityType,
  entityId,
  notes,
  isLoading,
  isSubmitting,
  onAddNote,
  onUpdateNote,
  onDeleteNote,
  showCreateInterface = true,
  className = '',
}: NotesSectionProps<T>) {
  const [highlightedNoteId, setHighlightedNoteId] = useState<string | null>(null);
  const [notesCount, setNotesCount] = useState(0);

  const handleNoteAdded = (note: T) => {
    setHighlightedNoteId(note.id);
    
    // Clear highlight after animation
    setTimeout(() => {
      setHighlightedNoteId(null);
    }, 3000);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {showCreateInterface && (
        <NoteCreator
          entityType={entityType}
          entityId={entityId}
          onNoteAdded={handleNoteAdded}
          notesCount={notesCount}
          isSubmitting={isSubmitting}
          onAddNote={onAddNote}
        />
      )}
      
      <NotesTimeline
        entityType={entityType}
        entityId={entityId}
        notes={notes}
        isLoading={isLoading}
        onUpdateNote={onUpdateNote}
        onDeleteNote={onDeleteNote}
        highlightedNoteId={highlightedNoteId}
        showCreateInterface={showCreateInterface}
        onNotesCountChange={setNotesCount}
      />
    </div>
  );
}
