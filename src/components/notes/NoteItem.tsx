/**
 * Generic note item component
 * Displays individual notes with edit/delete functionality
 */

import React, { useState } from 'react';
import { format } from 'date-fns';
import { User, Edit2, Trash2, Check, X } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle, 
  AlertDialogTrigger 
} from '@/components/ui/alert-dialog';
import { BaseNote, NoteEntityType, NOTE_CONFIGS } from '@/types/note';
import { useUser } from '@/contexts/UserContext';

interface NoteItemProps<T extends BaseNote> {
  note: T;
  entityType: NoteEntityType;
  onUpdate: (id: string, content: string) => Promise<T | null>;
  onDelete: (id: string) => Promise<boolean>;
  isHighlighted?: boolean;
}

export function NoteItem<T extends BaseNote>({
  note,
  entityType,
  onUpdate,
  onDelete,
  isHighlighted = false,
}: NoteItemProps<T>) {
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(note.content);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const { user } = useUser();
  const config = NOTE_CONFIGS[entityType];

  const canEdit = user.id === note.user_id;

  const handleSaveEdit = async () => {
    if (!editContent.trim() || editContent === note.content) {
      setIsEditing(false);
      setEditContent(note.content);
      return;
    }

    setIsUpdating(true);
    const updatedNote = await onUpdate(note.id, editContent.trim());
    
    if (updatedNote) {
      setIsEditing(false);
    } else {
      setEditContent(note.content); // Reset on failure
    }
    setIsUpdating(false);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditContent(note.content);
  };

  const handleDelete = async () => {
    setIsDeleting(true);
    await onDelete(note.id);
    setIsDeleting(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  };

  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <Card className={`transition-all duration-200 ${isHighlighted ? 'ring-2 ring-primary shadow-md' : ''}`}>
      <CardContent className="p-4">
        <div className="flex gap-3">
          <Avatar className="h-8 w-8 flex-shrink-0">
            <AvatarImage src={note.user_avatar} alt={note.user_name || 'User'} />
            <AvatarFallback>
              {note.user_name ? getUserInitials(note.user_name) : <User className="h-4 w-4" />}
            </AvatarFallback>
          </Avatar>

          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <span className="font-medium text-sm">
                  {note.user_name || 'Unknown User'}
                </span>
                <span className="text-xs text-muted-foreground">
                  {format(new Date(note.created_at), 'MMM d, yyyy • h:mm a')}
                </span>
                {note.created_at !== note.updated_at && (
                  <span className="text-xs text-muted-foreground">(edited)</span>
                )}
              </div>

              {canEdit && !isEditing && (
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsEditing(true)}
                    className="h-6 w-6 p-0"
                  >
                    <Edit2 className="h-3 w-3" />
                  </Button>
                  
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                        disabled={isDeleting}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Note</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete this {config.displayName.toLowerCase()} note? 
                          This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction 
                          onClick={handleDelete}
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              )}
            </div>

            {isEditing ? (
              <div className="space-y-2">
                <Textarea
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className="min-h-[80px] text-sm"
                  autoFocus
                />
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    onClick={handleSaveEdit}
                    disabled={isUpdating || !editContent.trim()}
                  >
                    <Check className="h-3 w-3 mr-1" />
                    {isUpdating ? 'Saving...' : 'Save'}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleCancelEdit}
                    disabled={isUpdating}
                  >
                    <X className="h-3 w-3 mr-1" />
                    Cancel
                  </Button>
                  <span className="text-xs text-muted-foreground ml-auto">
                    Ctrl+Enter to save, Esc to cancel
                  </span>
                </div>
              </div>
            ) : (
              <div className="text-sm whitespace-pre-wrap break-words">
                {note.content}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
