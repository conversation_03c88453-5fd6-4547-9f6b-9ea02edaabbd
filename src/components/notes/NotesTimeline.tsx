/**
 * Generic notes timeline component
 * Displays a timeline of notes for any entity type
 */

import React, { useEffect, useRef } from 'react';
import { MessageSquare, Clock } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { NoteItem } from './NoteItem';
import { BaseNote, NoteEntityType, NOTE_CONFIGS } from '@/types/note';

interface NotesTimelineProps<T extends BaseNote> {
  entityType: NoteEntityType;
  entityId: string;
  notes: T[];
  isLoading: boolean;
  onUpdateNote: (id: string, content: string) => Promise<T | null>;
  onDeleteNote: (id: string) => Promise<boolean>;
  highlightedNoteId?: string | null;
  showCreateInterface?: boolean;
  onNotesCountChange?: (count: number) => void;
}

export function NotesTimeline<T extends BaseNote>({
  entityType,
  entityId,
  notes,
  isLoading,
  onUpdateNote,
  onDeleteNote,
  highlightedNoteId,
  showCreateInterface = true,
  onNotesCountChange,
}: NotesTimelineProps<T>) {
  const highlightedNoteRef = useRef<HTMLDivElement>(null);
  const config = NOTE_CONFIGS[entityType];

  // Notify parent of notes count changes
  useEffect(() => {
    onNotesCountChange?.(notes.length);
  }, [notes.length, onNotesCountChange]);

  // Scroll to highlighted note
  useEffect(() => {
    if (highlightedNoteId && highlightedNoteRef.current) {
      setTimeout(() => {
        highlightedNoteRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }, 100);
    }
  }, [highlightedNoteId]);

  if (isLoading) {
    return (
      <Card data-timeline-container>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            {config.displayName} Notes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-muted-foreground">Loading notes...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (notes.length === 0) {
    return (
      <Card data-timeline-container>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            {config.displayName} Notes
            <Badge variant="secondary" className="ml-auto">0 notes</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <Clock className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No Notes Yet</h3>
            <p className="text-muted-foreground mb-4">
              {showCreateInterface 
                ? `Start the conversation by adding the first note about this ${config.displayName.toLowerCase()}.`
                : `No notes have been added for this ${config.displayName.toLowerCase()} yet.`
              }
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card data-timeline-container>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          {config.displayName} Notes
          <Badge variant="secondary" className="ml-auto">
            {notes.length} {notes.length === 1 ? 'note' : 'notes'}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {notes.map((note) => (
            <div
              key={note.id}
              ref={note.id === highlightedNoteId ? highlightedNoteRef : null}
            >
              <NoteItem
                note={note}
                entityType={entityType}
                onUpdate={onUpdateNote}
                onDelete={onDeleteNote}
                isHighlighted={note.id === highlightedNoteId}
              />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
