/**
 * Generic note creator component
 * Reusable for both client and candidate notes
 */

import React, { useState } from 'react';
import { MessageSquare } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { BaseNote, NoteEntityType, NOTE_CONFIGS } from '@/types/note';

interface NoteCreatorProps<T extends BaseNote> {
  entityType: NoteEntityType;
  entityId: string;
  onNoteAdded?: (note: T) => void;
  notesCount?: number;
  isSubmitting?: boolean;
  onAddNote: (content: string) => Promise<T | null>;
}

export function NoteCreator<T extends BaseNote>({
  entityType,
  entityId,
  onNoteAdded,
  notesCount = 0,
  isSubmitting = false,
  onAddNote,
}: NoteCreatorProps<T>) {
  const [newNote, setNewNote] = useState('');
  const config = NOTE_CONFIGS[entityType];

  const handleAddNote = async () => {
    if (!newNote.trim()) {
      return;
    }

    const createdNote = await onAddNote(newNote.trim());

    if (createdNote) {
      setNewNote('');
      onNoteAdded?.(createdNote);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleAddNote();
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Add Note
          {notesCount > 0 && (
            <Badge variant="secondary" className="ml-auto">
              {notesCount} {notesCount === 1 ? 'note' : 'notes'}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <Textarea
          placeholder={config.placeholder}
          value={newNote}
          onChange={(e) => setNewNote(e.target.value)}
          onKeyDown={handleKeyDown}
          className="min-h-[100px]"
        />
        <div className="flex justify-between items-center">
          <span className="text-xs text-muted-foreground">
            Press Ctrl+Enter to submit
          </span>
          <Button 
            onClick={handleAddNote} 
            disabled={isSubmitting || !newNote.trim()}
            type="button"
            size="sm"
          >
            {isSubmitting ? 'Adding Note...' : 'Add Note'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
