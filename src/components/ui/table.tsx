import * as React from "react"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>P<PERSON><PERSON>,
  <PERSON>ltipTrig<PERSON>,
} from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"

// Helper function to truncate URLs
const truncateUrl = (url: string, maxLength: number = 30): string => {
  if (!url || typeof url !== 'string' || url.length <= maxLength) return url;

  try {
    const urlObj = new URL(url);
    const domain = urlObj.hostname;

    // If domain itself is too long, truncate it
    if (domain.length > maxLength - 10) {
      return domain.substring(0, maxLength - 10) + '...';
    }

    // Keep domain and truncate path
    const pathLength = maxLength - domain.length - 3; // 3 for "..."
    if (pathLength <= 0) return domain;

    const path = urlObj.pathname + urlObj.search + urlObj.hash;
    const truncatedPath = path.length > pathLength
      ? path.substring(0, pathLength) + '...'
      : path;

    return domain + truncatedPath;
  } catch (e) {
    // If URL parsing fails, do simple truncation
    return url.substring(0, maxLength - 3) + '...';
  }
};

// Helper function to check if content is a URL
const isUrl = (content: string): boolean => {
  if (!content || typeof content !== 'string') return false;

  try {
    // Check if it's a valid URL
    new URL(content);
    return true;
  } catch (e) {
    // Check if it might be a URL without protocol
    const urlPattern = /^(www\.)?[a-z0-9-]+(\.[a-z0-9-]+)+(\/[^\s]*)?$/i;
    return urlPattern.test(content);
  }
};

// Enhanced Table component with responsive behavior
const Table = React.forwardRef<
  HTMLTableElement,
  React.HTMLAttributes<HTMLTableElement>
>(({ className, ...props }, ref) => (
  <div className="relative w-full overflow-auto">
    <table
      ref={ref}
      className={cn(
        "w-full caption-bottom text-sm border-collapse",
        "min-w-full table-auto md:table-fixed", // Responsive behavior
        className
      )}
      {...props}
    />
  </div>
))
Table.displayName = "Table"

const TableHeader = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <thead
    ref={ref}
    className={cn(
      "[&_tr]:border-b sticky top-0 bg-background z-10",
      className
    )}
    {...props}
  />
))
TableHeader.displayName = "TableHeader"

const TableBody = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <tbody
    ref={ref}
    className={cn(
      "[&_tr:last-child]:border-0",
      "[&_tr:nth-child(even)]:bg-muted/30", // Subtle alternating row colors
      className
    )}
    {...props}
  />
))
TableBody.displayName = "TableBody"

const TableFooter = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <tfoot
    ref={ref}
    className={cn(
      "border-t bg-muted/50 font-medium [&>tr]:last:border-b-0 sticky bottom-0",
      className
    )}
    {...props}
  />
))
TableFooter.displayName = "TableFooter"

const TableRow = React.forwardRef<
  HTMLTableRowElement,
  React.HTMLAttributes<HTMLTableRowElement>
>(({ className, ...props }, ref) => (
  <tr
    ref={ref}
    className={cn(
      "border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",
      className
    )}
    {...props}
  />
))
TableRow.displayName = "TableRow"

const TableHead = React.forwardRef<
  HTMLTableCellElement,
  React.ThHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => (
  <th
    ref={ref}
    className={cn(
      "h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",
      "whitespace-nowrap overflow-hidden text-ellipsis", // Prevent header wrapping
      className
    )}
    {...props}
  />
))
TableHead.displayName = "TableHead"

// Enhanced TableCell with content type handling and tooltips
interface TableCellProps extends React.TdHTMLAttributes<HTMLTableCellElement> {
  contentType?: 'text' | 'url' | 'numeric' | 'date';
  maxWidth?: string;
  showTooltip?: boolean;
  tooltipContent?: React.ReactNode;
}

const TableCell = React.forwardRef<HTMLTableCellElement, TableCellProps>(
  ({
    className,
    children,
    contentType = 'text',
    maxWidth,
    showTooltip = true,
    tooltipContent,
    ...props
  }, ref) => {
    // Determine if we should show a tooltip
    const content = children?.toString() || '';
    const needsTooltip = showTooltip && content.length > 0;

    // Handle different content types
    let displayContent = children;
    let tooltipText = tooltipContent || content;

    if (contentType === 'url' && typeof content === 'string') {
      displayContent = truncateUrl(content);

      // If it's a URL and no custom tooltip is provided, use the full URL
      if (!tooltipContent) tooltipText = content;
    } else if (typeof content === 'string' && isUrl(content)) {
      // Auto-detect URLs even if contentType isn't explicitly set
      displayContent = truncateUrl(content);

      // If it's a URL and no custom tooltip is provided, use the full URL
      if (!tooltipContent) tooltipText = content;
    }

    // Apply content-type specific styling
    const contentTypeClass = {
      'text': '',
      'url': 'text-primary hover:underline',
      'numeric': 'text-right font-mono',
      'date': 'whitespace-nowrap',
    }[contentType];

    // Create the cell content with or without tooltip
    const cellContent = (
      <div
        className={cn(
          "overflow-hidden text-ellipsis whitespace-nowrap",
          contentTypeClass,
          maxWidth && `max-w-[${maxWidth}]`
        )}
      >
        {displayContent}
      </div>
    );

    return (
      <td
        ref={ref}
        className={cn(
          "p-4 align-middle [&:has([role=checkbox])]:pr-0",
          className
        )}
        {...props}
      >
        {needsTooltip ? (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                {cellContent}
              </TooltipTrigger>
              <TooltipContent>
                {tooltipText}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ) : (
          cellContent
        )}
      </td>
    );
  }
);
TableCell.displayName = "TableCell"

const TableCaption = React.forwardRef<
  HTMLTableCaptionElement,
  React.HTMLAttributes<HTMLTableCaptionElement>
>(({ className, ...props }, ref) => (
  <caption
    ref={ref}
    className={cn("mt-4 text-sm text-muted-foreground", className)}
    {...props}
  />
))
TableCaption.displayName = "TableCaption"

export {
  Table,
  TableHeader,
  TableBody,
  TableFooter,
  TableHead,
  TableRow,
  TableCell,
  TableCaption,
}
