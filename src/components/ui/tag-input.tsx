import * as React from "react"
import { X } from "lucide-react"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"

interface TagInputProps {
  value?: string[]
  onChange?: (tags: string[]) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  id?: string
}

const TagInput = React.forwardRef<HTMLInputElement, TagInputProps>(
  ({ value = [], onChange, placeholder = "Type and press Enter or comma to add...", className, disabled, id, ...props }, ref) => {
    const [inputValue, setInputValue] = React.useState("")
    const [tags, setTags] = React.useState<string[]>(value)

    // Update internal state when value prop changes
    React.useEffect(() => {
      setTags(value)
    }, [value])

    const addTag = (tag: string) => {
      const trimmedTag = tag.trim()
      if (trimmedTag && !tags.includes(trimmedTag)) {
        const newTags = [...tags, trimmedTag]
        setTags(newTags)
        onChange?.(newTags)
      }
      setInputValue("")
    }

    const removeTag = (tagToRemove: string) => {
      const newTags = tags.filter(tag => tag !== tagToRemove)
      setTags(newTags)
      onChange?.(newTags)
    }

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === "Enter" || e.key === ",") {
        e.preventDefault()
        addTag(inputValue)
      } else if (e.key === "Backspace" && !inputValue && tags.length > 0) {
        // Remove last tag when backspace is pressed on empty input
        removeTag(tags[tags.length - 1])
      }
    }

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value
      // Handle comma-separated input
      if (value.includes(",")) {
        const newTags = value.split(",").map(tag => tag.trim()).filter(tag => tag)
        newTags.forEach(tag => addTag(tag))
      } else {
        setInputValue(value)
      }
    }

    const handleBlur = () => {
      // Add tag on blur if there's input
      if (inputValue.trim()) {
        addTag(inputValue)
      }
    }

    return (
      <div className={cn("flex flex-col gap-2", className)}>
        {/* Tags Display */}
        {tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {tags.map((tag, index) => (
              <Badge
                key={index}
                variant="secondary"
                className="flex items-center gap-1 px-2 py-1 text-xs"
              >
                <span>{tag}</span>
                {!disabled && (
                  <button
                    type="button"
                    onClick={() => removeTag(tag)}
                    className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5 transition-colors"
                    aria-label={`Remove ${tag}`}
                  >
                    <X className="h-3 w-3" />
                  </button>
                )}
              </Badge>
            ))}
          </div>
        )}
        
        {/* Input Field */}
        <Input
          ref={ref}
          id={id}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          className="w-full"
          {...props}
        />
        
        {/* Helper Text */}
        <p className="text-xs text-muted-foreground">
          Press Enter or comma to add skills. Click × to remove.
        </p>
      </div>
    )
  }
)

TagInput.displayName = "TagInput"

export { TagInput }
