import { Moon, Sun, Palette } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useTheme, ThemeStyle } from "@/contexts/ThemeContext";

/**
 * ThemeToggle - Componente simplificado para cambiar entre temas y modos
 */
export function ThemeToggle() {
  const { themeMode, themeStyle, setThemeStyle, toggleThemeMode } = useTheme();
  const isDarkMode = themeMode === 'dark';

  // Lista de temas disponibles
  const themes = [
    { value: 'theme-default', label: 'Default' },
    { value: 'theme-cosmic', label: 'Cosmic' },
    { value: 'theme-modern', label: 'Modern' },
    { value: 'theme-stargety', label: 'Stargety' },
    { value: 'theme-tangerine', label: 'Tangerine' },
  ];

  return (
    <div className="flex items-center gap-2">
      {/* Dark/Light Mode Toggle */}
      <Button
        variant="ghost"
        size="icon"
        onClick={toggleThemeMode}
        className="rounded-full"
        aria-label="Toggle dark/light mode"
      >
        {isDarkMode ? (
          <Moon className="h-5 w-5" />
        ) : (
          <Sun className="h-5 w-5" />
        )}
      </Button>

      {/* Theme Selector */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="rounded-full"
            aria-label="Select theme"
          >
            <Palette className="h-5 w-5" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {themes.map((t) => (
            <DropdownMenuItem
              key={t.value}
              onClick={() => {
                try {
                  // Aplicar directamente al DOM para asegurar que el cambio sea inmediato
                  const html = document.documentElement;

                  // Limpiar todas las clases de tema existentes
                  const themeClasses = Array.from(html.classList).filter(cls => cls.startsWith('theme-'));
                  themeClasses.forEach(cls => html.classList.remove(cls));

                  // Aplicar la clase del tema seleccionado
                  html.classList.add(t.value);

                  // Guardar en localStorage
                  localStorage.setItem('themeStyle', t.value);

                  // Actualizar el estado del contexto
                  setThemeStyle(t.value as ThemeStyle);

                  console.log(`Tema cambiado a: ${t.value}`);
                } catch (error) {
                  console.error('Error al aplicar el tema:', error);
                }
              }}
              className={themeStyle === t.value ? "bg-accent" : ""}
            >
              {t.label}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
