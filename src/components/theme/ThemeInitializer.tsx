import { useEffect } from 'react';

/**
 * ThemeInitializer component
 *
 * This component initializes the theme on page load by:
 * 1. Reading themeStyle and themeMode from localStorage
 * 2. Applying the appropriate classes to the HTML element
 *
 * It doesn't render anything visible, just handles the theme initialization.
 */
export function ThemeInitializer() {
  useEffect(() => {
    // Migrate old theme format if it exists
    const oldPrefs = localStorage.getItem('themePreferences');
    if (oldPrefs) {
      try {
        const prefs = JSON.parse(oldPrefs);
        if (prefs.theme) {
          localStorage.setItem('themeStyle', prefs.theme);
          localStorage.setItem('themeMode', prefs.darkMode ? 'dark' : 'light');
          localStorage.removeItem('themePreferences');
        }
      } catch (e) {
        console.error('Error migrating old theme preferences:', e);
      }
    }

    // Get theme preferences from localStorage
    const themeStyle = localStorage.getItem('themeStyle') || 'theme-default';
    const themeMode = localStorage.getItem('themeMode') || 'light';

    // Apply theme to document
    const html = document.documentElement;

    // Apply theme style
    const themeClasses = Array.from(html.classList).filter(cls => cls.startsWith('theme-'));
    themeClasses.forEach(cls => html.classList.remove(cls));
    html.classList.add(themeStyle);

    // Apply theme mode
    if (themeMode === 'dark') {
      html.classList.add('dark');
    } else {
      html.classList.remove('dark');
    }

    /**
     * Expose a global function to update the theme
     * This function updates both the theme style and mode in a single operation
     * to avoid race conditions when updating the theme
     * @param style The theme style to apply (e.g., 'theme-default')
     * @param mode The theme mode to apply ('light' or 'dark')
     */
    window.updateTheme = (style?: string, mode?: string) => {
      if (!style && !mode) {
        return;
      }

      // Get current values if not provided
      const newStyle = style || localStorage.getItem('themeStyle') || 'theme-default';
      const newMode = mode || localStorage.getItem('themeMode') || 'light';

      // Apply theme in a single operation
      // Clear all theme classes
      const themeClasses = Array.from(html.classList).filter(cls => cls.startsWith('theme-'));
      themeClasses.forEach(cls => html.classList.remove(cls));

      // Add the new theme class
      html.classList.add(newStyle);

      // Set dark mode
      if (newMode === 'dark') {
        html.classList.add('dark');
      } else {
        html.classList.remove('dark');
      }

      // Update localStorage
      localStorage.setItem('themeStyle', newStyle);
      localStorage.setItem('themeMode', newMode);
    };
  }, []);

  return null;
}

// Add the global updateTheme function type
declare global {
  interface Window {
    updateTheme: (style?: string, mode?: string) => void;
  }
}

export default ThemeInitializer;
