
import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { MoreHorizontal, User, ChevronRight } from "lucide-react";
import { Link } from "react-router-dom";
import { cn } from "@/lib/utils";

interface MetadataItem {
  label: string;
  value: string;
}

interface KanbanCardProps {
  id: string;
  title: string;
  subtitle: string;
  imageUrl?: string;
  metadata: MetadataItem[];
  moveOptions: Array<{ status: string; label: string }>;
  onMove: (newStatus: string) => void;
  linkTo: string;
}

export default function KanbanCard({
  id,
  title,
  subtitle,
  imageUrl,
  metadata,
  moveOptions,
  onMove,
  linkTo
}: KanbanCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  
  return (
    <Card 
      className={cn(
        "transition-all cursor-pointer hover:shadow-md group",
        isHovered ? "ring-1 ring-primary/20" : ""
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CardContent className="p-3">
        <Link to={linkTo} className="block">
          <div className="flex items-center gap-3 mb-2">
            <div className="h-9 w-9 rounded-full overflow-hidden bg-secondary flex items-center justify-center flex-shrink-0">
              {imageUrl ? (
                <img 
                  src={imageUrl} 
                  alt={title}
                  className="h-full w-full object-cover"
                />
              ) : (
                <User className="h-5 w-5 text-muted-foreground" />
              )}
            </div>
            <div className="min-w-0 flex-1">
              <h4 className="font-medium text-sm truncate">{title}</h4>
              <p className="text-xs text-muted-foreground truncate">{subtitle}</p>
            </div>
            
            <div className={cn(
              "transition-opacity",
              isHovered ? "opacity-100" : "opacity-0"
            )}>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-56 p-2" align="end">
                  <div className="space-y-1">
                    <div className="px-2 py-1.5 text-xs font-medium">Move to stage</div>
                    {moveOptions.map(option => (
                      <Button
                        key={option.status}
                        variant="ghost"
                        size="sm"
                        className="w-full justify-between text-xs"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          onMove(option.status);
                        }}
                      >
                        {option.label}
                        <ChevronRight className="h-3.5 w-3.5" />
                      </Button>
                    ))}
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </Link>
        
        <div className="mt-3 space-y-1">
          {metadata.map((item, index) => (
            <div key={index} className="flex items-center justify-between text-xs">
              <span className="text-muted-foreground">{item.label}:</span>
              <span className="truncate max-w-[150px] font-medium">{item.value}</span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
