
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { ReactNode } from "react";

interface KanbanColumnProps {
  title: string;
  count: number;
  children: ReactNode;
  id?: string;
}

export default function KanbanColumn({ title, count, children, id }: KanbanColumnProps) {
  return (
    <Card className="min-w-[300px] max-w-[300px] h-full" id={id}>
      <CardHeader className="px-4 py-3">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium">{title}</h3>
          <span className="bg-muted text-muted-foreground text-xs rounded-full px-2 py-0.5">
            {count}
          </span>
        </div>
      </CardHeader>
      <CardContent className="px-2 pt-0 pb-2 overflow-y-auto max-h-[calc(100vh-220px)] space-y-2">
        {count === 0 ? (
          <div className="flex items-center justify-center h-20 border border-dashed rounded-md">
            <p className="text-sm text-muted-foreground">No items</p>
          </div>
        ) : (
          children
        )}
      </CardContent>
    </Card>
  );
}
