import { useState, useEffect, useRef, useCallback, memo } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2, Search, X, Briefcase, User, Building2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import useSearch from '@/hooks/useSearch';
import { SearchResultItem as SearchResult } from '@/lib/search/search';

// Componente memoizado para mostrar un resultado de búsqueda individual
const SearchResultItem = memo(({ hit, onClick }: {
  hit: SearchResult;
  onClick: (hit: SearchResult) => void;
}) => {
  return (
    <div
      className="flex items-center px-2 py-1.5 rounded-md hover:bg-muted cursor-pointer"
      onClick={() => onClick(hit)}
    >
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium truncate">
          {hit.title || hit.name || hit.company_name}
        </p>
        {hit.description && (
          <p className="text-xs text-muted-foreground truncate">
            {hit.description}
          </p>
        )}
      </div>
    </div>
  );
});

// Componente memoizado para mostrar un grupo de resultados
const SearchResultGroup = memo(({
  entityType,
  group,
  onResultClick
}: {
  entityType: string;
  group: { count: number; hits: SearchResult[] };
  onResultClick: (hit: SearchResult) => void;
}) => {
  // Get entity icon
  const getEntityIcon = (entityType: string) => {
    switch (entityType) {
      case 'jobs':
        return <Briefcase className="h-4 w-4 mr-2" />;
      case 'candidates':
        return <User className="h-4 w-4 mr-2" />;
      case 'clients':
        return <Building2 className="h-4 w-4 mr-2" />;
      default:
        return null;
    }
  };

  // Get entity label
  const getEntityLabel = (entityType: string) => {
    switch (entityType) {
      case 'jobs':
        return 'Jobs';
      case 'candidates':
        return 'Candidates';
      case 'clients':
        return 'Clients';
      default:
        return entityType;
    }
  };

  return (
    <div className="mb-4">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-medium flex items-center">
          {getEntityIcon(entityType)}
          {getEntityLabel(entityType)}
        </h3>
        <span className="text-xs text-muted-foreground">
          {group.count} results
        </span>
      </div>
      <div className="space-y-1">
        {group.hits.map((hit) => (
          <SearchResultItem
            key={`${hit.entity_type}-${hit.id}`}
            hit={hit}
            onClick={onResultClick}
          />
        ))}
      </div>
    </div>
  );
});

export default function GlobalSearch() {
  const [open, setOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const inputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();

  const {
    query,
    setQuery,
    results,
    isLoading,
    resetSearch,
  } = useSearch({
    debounceMs: 200,
    perPage: 5,
    groupBy: 'entity_type',
  });

  // Handle keyboard shortcut to open search (Ctrl+K or Cmd+K)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        setOpen(true);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Focus input when dialog opens
  useEffect(() => {
    if (open && inputRef.current) {
      // Usar requestAnimationFrame en lugar de setTimeout para mejor rendimiento
      requestAnimationFrame(() => {
        inputRef.current?.focus();
      });
    }
  }, [open]);

  // Reset search when dialog closes
  useEffect(() => {
    if (!open) {
      resetSearch();
    }
  }, [open, resetSearch]);

  // Handle result click - memoizado para evitar recreaciones
  const handleResultClick = useCallback((result: SearchResult) => {
    setOpen(false);

    // Navigate based on entity type
    switch (result.entity_type) {
      case 'jobs':
        navigate(`/jobs/${result.id}`);
        break;
      case 'candidates':
        navigate(`/candidates/${result.id}`);
        break;
      case 'clients':
        navigate(`/clients/${result.id}`);
        break;
      default:
        console.warn(`Unknown entity type: ${result.entity_type}`);
    }
  }, [navigate]);

  return (
    <>
      {/* Keyboard shortcut indicator */}
      <Button
        variant="outline"
        className="w-full justify-between text-muted-foreground"
        onClick={() => setOpen(true)}
      >
        <div className="flex items-center">
          <Search className="mr-2 h-4 w-4" />
          <span>Search...</span>
        </div>
        <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground">
          <span className="text-xs">⌘</span>K
        </kbd>
      </Button>

      {/* Search Dialog */}
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-[600px] p-0">
          <div className="flex items-center border-b px-3">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <Input
              ref={inputRef}
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Search for anything..."
              className="flex h-12 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50 border-0 focus-visible:ring-0"
            />
            {query && (
              <Button
                variant="ghost"
                onClick={() => setQuery('')}
                className="h-6 w-6 p-0 rounded-md"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          {query.trim() !== '' && (
            <div className="px-3 py-2">
              <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="mb-2">
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="jobs">Jobs</TabsTrigger>
                  <TabsTrigger value="candidates">Candidates</TabsTrigger>
                  <TabsTrigger value="clients">Clients</TabsTrigger>
                </TabsList>

                {isLoading ? (
                  <div className="flex justify-center py-6 mt-2 mb-4">
                    <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                  </div>
                ) : results && results.found > 0 ? (
                  <div className="mt-2 mb-4">
                    <TabsContent value="all" className="m-0">
                      <ScrollArea className="max-h-[300px]">
                        {results.grouped_hits && Object.entries(results.grouped_hits).map(([entityType, group]) => (
                          <SearchResultGroup
                            key={entityType}
                            entityType={entityType}
                            group={group}
                            onResultClick={handleResultClick}
                          />
                        ))}
                      </ScrollArea>
                    </TabsContent>

                    {['jobs', 'candidates', 'clients'].map((entityType) => (
                      <TabsContent key={entityType} value={entityType} className="m-0">
                        <ScrollArea className="max-h-[300px]">
                          {results.grouped_hits && results.grouped_hits[entityType] ? (
                            <div className="space-y-1">
                              {results.grouped_hits[entityType].hits.map((hit) => (
                                <SearchResultItem
                                  key={`${hit.entity_type}-${hit.id}`}
                                  hit={hit}
                                  onClick={handleResultClick}
                                />
                              ))}
                            </div>
                          ) : (
                            <div className="py-6 text-center text-muted-foreground">
                              No {entityType} found
                            </div>
                          )}
                        </ScrollArea>
                      </TabsContent>
                    ))}
                  </div>
                ) : (
                  <div className="py-6 text-center mt-2 mb-4">
                    <p className="text-muted-foreground">No results found</p>
                  </div>
                )}
              </Tabs>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
