
import { cn } from "@/lib/utils";
import { ReactNode } from "react";

interface StatCardProps {
  title: string;
  value: string;
  icon: ReactNode;
  trendValue?: string;
  trendDirection?: "up" | "down" | "neutral";
  className?: string;
}

export default function StatCard({
  title,
  value,
  icon,
  trendValue,
  trendDirection = "neutral",
  className,
}: StatCardProps) {
  return (
    <div 
      className={cn(
        "relative overflow-hidden rounded-xl bg-card p-6 shadow-sm card-shadow transition-all duration-200", 
        className
      )}
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <h3 className="mt-1.5 text-2xl font-semibold">{value}</h3>
          
          {trendValue && (
            <div className="mt-2 flex items-center text-xs">
              <span 
                className={cn(
                  "font-medium",
                  trendDirection === "up" && "text-green-600",
                  trendDirection === "down" && "text-red-600",
                  trendDirection === "neutral" && "text-muted-foreground"
                )}
              >
                {trendValue}
              </span>
              <span className="ml-1 text-muted-foreground">vs last month</span>
            </div>
          )}
        </div>
        
        <div className="h-12 w-12 rounded-lg bg-primary/10 p-2.5 text-primary">
          {icon}
        </div>
      </div>
    </div>
  );
}
