
import { cn } from "@/lib/utils";
import { Check, Clock, X } from "lucide-react";

type ActivityStatus = "pending" | "approved" | "rejected";

interface Activity {
  id: string;
  type: string;
  name: string;
  position: string;
  time: string;
  status: ActivityStatus;
}

const activities: Activity[] = [
  {
    id: "1",
    type: "Application",
    name: "<PERSON>",
    position: "Senior Product Designer",
    time: "2 hours ago",
    status: "pending"
  },
  {
    id: "2",
    type: "Interview",
    name: "<PERSON>",
    position: "Frontend Developer",
    time: "Yesterday",
    status: "approved"
  },
  {
    id: "3",
    type: "Application",
    name: "<PERSON>",
    position: "Marketing Specialist",
    time: "Yesterday",
    status: "rejected"
  },
  {
    id: "4",
    type: "Interview",
    name: "<PERSON>",
    position: "UX Researcher",
    time: "2 days ago",
    status: "approved"
  }
];

const StatusIcon = ({ status }: { status: ActivityStatus }) => {
  switch (status) {
    case "approved":
      return <Check className="h-4 w-4 text-green-600" />;
    case "rejected":
      return <X className="h-4 w-4 text-red-600" />;
    default:
      return <Clock className="h-4 w-4 text-amber-600" />;
  }
};

export default function RecentActivity() {
  return (
    <div className="rounded-xl bg-card p-6 shadow-sm card-shadow">
      <div className="flex items-center justify-between border-b border-border pb-4">
        <h2 className="font-semibold">Recent Activity</h2>
        <button className="text-sm font-medium text-primary hover:underline">
          View all
        </button>
      </div>
      
      <div className="divide-y divide-border">
        {activities.map((activity) => (
          <div key={activity.id} className="flex items-center justify-between py-4">
            <div className="flex items-center gap-3">
              <div className={cn(
                "flex h-9 w-9 items-center justify-center rounded-full",
                activity.status === "approved" && "bg-green-50",
                activity.status === "rejected" && "bg-red-50",
                activity.status === "pending" && "bg-amber-50",
              )}>
                <StatusIcon status={activity.status} />
              </div>
              
              <div>
                <p className="font-medium">{activity.name}</p>
                <p className="text-sm text-muted-foreground">{activity.type} for {activity.position}</p>
              </div>
            </div>
            
            <div className="text-right">
              <p className="text-sm text-muted-foreground">{activity.time}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
