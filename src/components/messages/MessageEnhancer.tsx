import { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON>alogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, Sparkles } from 'lucide-react';
import { useAI } from '@/contexts/AIContext';

interface MessageEnhancerProps {
  message: string;
  onApply: (enhancedMessage: string) => void;
}

export default function MessageEnhancer({ message, onApply }: MessageEnhancerProps) {
  const [open, setOpen] = useState(false);
  const [enhancedMessage, setEnhancedMessage] = useState('');
  const { enhanceMessage, isEnhancing, settings } = useAI();

  // <PERSON>le enhancing the message
  const handleEnhance = async () => {
    if (!message.trim()) return;
    
    const enhanced = await enhanceMessage(message);
    setEnhancedMessage(enhanced);
  };

  // <PERSON>le applying the enhanced message
  const handleApply = () => {
    onApply(enhancedMessage);
    setOpen(false);
  };

  // <PERSON>le opening the dialog
  const handleOpen = () => {
    setOpen(true);
    setEnhancedMessage('');
    handleEnhance();
  };

  // If AI is not enabled, don't render the component
  if (!settings.enabled || !settings.apiKey) {
    return null;
  }

  return (
    <>
      <Button
        variant="ghost"
        size="icon"
        className="text-muted-foreground hover:text-primary"
        onClick={handleOpen}
        title="Enhance message with AI"
      >
        <Sparkles className="h-4 w-4" />
      </Button>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>AI Message Enhancement</DialogTitle>
            <DialogDescription>
              Review and apply AI-suggested improvements to your message.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Original Message</h3>
              <div className="rounded-md bg-muted p-3 text-sm">
                {message}
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-sm font-medium">Enhanced Message</h3>
              {isEnhancing ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <Textarea
                  value={enhancedMessage}
                  onChange={(e) => setEnhancedMessage(e.target.value)}
                  className="min-h-[120px]"
                />
              )}
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleApply} disabled={isEnhancing || !enhancedMessage.trim()}>
              Apply Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
