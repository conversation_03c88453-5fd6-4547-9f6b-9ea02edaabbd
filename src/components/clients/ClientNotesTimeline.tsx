/**
 * Client notes timeline component
 * Wrapper around the generic NotesTimeline for client-specific usage
 */

import React from 'react';
import { NotesTimeline } from '@/components/notes/NotesTimeline';
import { useClientNotes } from '@/hooks/useNotes';
import { ClientNote } from '@/types/note';

interface ClientNotesTimelineProps {
  clientId: string;
  highlightedNoteId?: string | null;
  showCreateInterface?: boolean;
  onNotesCountChange?: (count: number) => void;
}

export function ClientNotesTimeline({
  clientId,
  highlightedNoteId,
  showCreateInterface = true,
  onNotesCountChange,
}: ClientNotesTimelineProps) {
  const {
    notes,
    isLoading,
    updateNote,
    deleteNote,
  } = useClientNotes(clientId);

  return (
    <NotesTimeline<ClientNote>
      entityType="client"
      entityId={clientId}
      notes={notes}
      isLoading={isLoading}
      onUpdateNote={updateNote}
      onDeleteNote={deleteNote}
      highlightedNoteId={highlightedNoteId}
      showCreateInterface={showCreateInterface}
      onNotesCountChange={onNotesCountChange}
    />
  );
}
