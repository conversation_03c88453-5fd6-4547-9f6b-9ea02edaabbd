/**
 * Client note creator component
 * Wrapper around the generic NoteCreator for client-specific usage
 */

import React from 'react';
import { NoteCreator } from '@/components/notes/NoteCreator';
import { useClientNotes } from '@/hooks/useNotes';
import { ClientNote } from '@/types/note';

interface ClientNoteCreatorProps {
  clientId: string;
  onNoteAdded?: (note: ClientNote) => void;
  notesCount?: number;
}

export function ClientNoteCreator({
  clientId,
  onNoteAdded,
  notesCount = 0,
}: ClientNoteCreatorProps) {
  const { addNote, isSubmitting } = useClientNotes(clientId);

  return (
    <NoteCreator<ClientNote>
      entityType="client"
      entityId={clientId}
      onNoteAdded={onNoteAdded}
      notesCount={notesCount}
      isSubmitting={isSubmitting}
      onAddNote={addNote}
    />
  );
}
