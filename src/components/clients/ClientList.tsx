import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Client } from '@/types/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Building, MapPin, Phone, Mail, Plus, AlertTriangle } from 'lucide-react';

interface ClientListProps {
  clients: Client[];
  isLoading?: boolean;
  onClientNotFound?: (clientId: string) => void;
}

export default function ClientList({ clients, isLoading = false, onClientNotFound }: ClientListProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Filtrar clientes por búsqueda y estado
  const filteredClients = clients.filter((client) => {
    const matchesSearch =
      client.company_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      client.contact_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      client.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      client.industry?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      client.location?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === 'all' || client.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Renderizar estado del cliente como badge
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'inactive':
        return <Badge variant="secondary">Inactive</Badge>;
      case 'lead':
        return <Badge className="bg-blue-500">Lead</Badge>;
      case 'former':
        return <Badge variant="outline">Former</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <CardTitle>Clients</CardTitle>
          <div className="flex items-center gap-2">
            <div className="relative w-full sm:w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search clients..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Tabs defaultValue="all" className="w-auto" onValueChange={setStatusFilter}>
              <TabsList>
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="active">Active</TabsTrigger>
                <TabsTrigger value="lead">Leads</TabsTrigger>
                <TabsTrigger value="inactive">Inactive</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center h-40">
            <p className="text-muted-foreground">Loading clients...</p>
          </div>
        ) : filteredClients.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-40 text-center">
            <Building className="h-8 w-8 text-muted-foreground mb-2" />
            <p className="text-muted-foreground mb-4">No clients found</p>
            <Button asChild>
              <Link to="/clients/new">
                <Plus className="h-4 w-4 mr-2" />
                Add Client
              </Link>
            </Button>
          </div>
        ) : (
          <div className="rounded-md border">
            <div className="hidden md:grid grid-cols-12 gap-4 p-4 border-b bg-muted/50 text-sm font-medium">
              <div className="col-span-4">Company</div>
              <div className="col-span-2">Contact</div>
              <div className="col-span-2">Location</div>
              <div className="col-span-2">Status</div>
              <div className="col-span-2 text-right">Actions</div>
            </div>
            <div className="divide-y">
              {filteredClients.map((client) => (
                <div key={client.id} className="grid grid-cols-1 md:grid-cols-12 gap-4 p-4 items-center">
                  <div className="col-span-4 flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center">
                      {client.logo_url ? (
                        <img
                          src={client.logo_url}
                          alt={client.company_name}
                          className="w-10 h-10 rounded-full object-cover"
                        />
                      ) : (
                        <Building className="h-5 w-5 text-muted-foreground" />
                      )}
                    </div>
                    <div>
                      <div className="font-medium">{client.company_name}</div>
                      <div className="text-sm text-muted-foreground">{client.industry}</div>
                    </div>
                  </div>
                  <div className="col-span-2 md:block">
                    <div className="text-sm">{client.contact_name}</div>
                    <div className="text-xs text-muted-foreground">{client.email}</div>
                  </div>
                  <div className="col-span-2 hidden md:flex items-center">
                    <MapPin className="h-4 w-4 text-muted-foreground mr-1" />
                    <span className="text-sm">{client.location || 'N/A'}</span>
                  </div>
                  <div className="col-span-2 hidden md:block">
                    {renderStatusBadge(client.status)}
                  </div>
                  <div className="col-span-2 flex justify-end gap-2">
                    <Button variant="outline" size="icon" asChild>
                      <Link to={`/clients/${client.id}`}>
                        <Mail className="h-4 w-4" />
                        <span className="sr-only">Contact</span>
                      </Link>
                    </Button>
                    <Button variant="outline" size="icon" asChild>
                      <Link to={`/clients/${client.id}`}>
                        <Phone className="h-4 w-4" />
                        <span className="sr-only">Call</span>
                      </Link>
                    </Button>
                    <Button variant="default" size="sm" asChild>
                      <Link to={`/clients/${client.id}`}>View</Link>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
