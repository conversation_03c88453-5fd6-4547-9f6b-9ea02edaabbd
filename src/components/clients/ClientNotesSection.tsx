/**
 * Client notes section component
 * Wrapper around the generic NotesSection for client-specific usage
 */

import React from 'react';
import { NotesSection } from '@/components/notes/NotesSection';
import { useClientNotes } from '@/hooks/useNotes';
import { ClientNote } from '@/types/note';

interface ClientNotesSectionProps {
  clientId: string;
  showCreateInterface?: boolean;
  className?: string;
}

export function ClientNotesSection({
  clientId,
  showCreateInterface = true,
  className = '',
}: ClientNotesSectionProps) {
  const {
    notes,
    isLoading,
    isSubmitting,
    addNote,
    updateNote,
    deleteNote,
  } = useClientNotes(clientId);

  return (
    <NotesSection<ClientNote>
      entityType="client"
      entityId={clientId}
      notes={notes}
      isLoading={isLoading}
      isSubmitting={isSubmitting}
      onAddNote={addNote}
      onUpdateNote={updateNote}
      onDeleteNote={deleteNote}
      showCreateInterface={showCreateInterface}
      className={className}
    />
  );
}
