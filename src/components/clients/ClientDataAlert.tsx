import { Alert<PERSON><PERSON>gle, Refresh<PERSON><PERSON> } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';

interface ClientDataAlertProps {
  type: 'stale-data' | 'sync-error' | 'not-found';
  message?: string;
  onRefresh?: () => void;
  onDismiss?: () => void;
}

export function ClientDataAlert({ type, message, onRefresh, onDismiss }: ClientDataAlertProps) {
  const getAlertContent = () => {
    switch (type) {
      case 'stale-data':
        return {
          title: 'Data May Be Outdated',
          description: message || 'Some client data may be outdated. Consider refreshing to get the latest information.',
          variant: 'default' as const
        };
      case 'sync-error':
        return {
          title: 'Synchronization Error',
          description: message || 'There was an error synchronizing client data. Some information may not be current.',
          variant: 'destructive' as const
        };
      case 'not-found':
        return {
          title: 'Client Not Found',
          description: message || 'One or more clients could not be found. They may have been deleted.',
          variant: 'destructive' as const
        };
      default:
        return {
          title: 'Data Issue',
          description: message || 'There is an issue with the client data.',
          variant: 'default' as const
        };
    }
  };

  const { title, description, variant } = getAlertContent();

  return (
    <Alert variant={variant} className="mb-4">
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle>{title}</AlertTitle>
      <AlertDescription className="flex items-center justify-between">
        <span>{description}</span>
        <div className="flex gap-2 ml-4">
          {onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              className="flex items-center gap-1"
            >
              <RefreshCw className="h-3 w-3" />
              Refresh
            </Button>
          )}
          {onDismiss && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onDismiss}
            >
              Dismiss
            </Button>
          )}
        </div>
      </AlertDescription>
    </Alert>
  );
}
