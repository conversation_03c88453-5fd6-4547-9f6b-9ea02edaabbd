import { useState, useEffect, useCallback, useRef } from 'react';
import { searchAll, SearchResults } from '@/lib/search/search';
import { EntityType } from '@/lib/search/collection-schemas';
import { searchCollection } from '@/lib/search/search';

interface UseSearchOptions {
  initialQuery?: string;
  debounceMs?: number;
  perPage?: number;
  filterBy?: string;
  sortBy?: string;
  groupBy?: string;
  collection?: EntityType;
}

export function useSearch({
  initialQuery = '',
  debounceMs = 300,
  perPage = 10,
  filterBy,
  sortBy,
  groupBy = 'entity_type',
  collection,
}: UseSearchOptions = {}) {
  const [query, setQuery] = useState(initialQuery);
  const [debouncedQuery, setDebouncedQuery] = useState(initialQuery);
  const [results, setResults] = useState<SearchResults | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [page, setPage] = useState(1);

  const debounceTimerRef = useRef<number | null>(null);

  // Debounce the search query
  useEffect(() => {
    if (debounceTimerRef.current) {
      window.clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = window.setTimeout(() => {
      setDebouncedQuery(query);
      // Reset to page 1 when query changes
      setPage(1);
    }, debounceMs);

    return () => {
      if (debounceTimerRef.current) {
        window.clearTimeout(debounceTimerRef.current);
      }
    };
  }, [query, debounceMs]);

  // Perform the search
  const performSearch = useCallback(async () => {
    if (!debouncedQuery.trim()) {
      setResults(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      let searchResults;

      if (collection) {
        // Search in a specific collection
        searchResults = await searchCollection(collection, debouncedQuery, {
          page,
          perPage,
          filterBy,
          sortBy,
        });
      } else {
        // Search across all collections
        searchResults = await searchAll(debouncedQuery, {
          page,
          perPage,
          filterBy,
          sortBy,
          groupBy,
        });
      }

      setResults(searchResults);
    } catch (err) {
      console.error('Search error:', err);
      setError(err instanceof Error ? err : new Error('An error occurred during search'));
    } finally {
      setIsLoading(false);
    }
  }, [debouncedQuery, page, perPage, filterBy, sortBy, groupBy, collection]);

  // Trigger search when debounced query or page changes
  useEffect(() => {
    performSearch();
  }, [performSearch]);

  // Reset search
  const resetSearch = useCallback(() => {
    setQuery('');
    setDebouncedQuery('');
    setResults(null);
    setPage(1);
    setError(null);
  }, []);

  return {
    query,
    setQuery,
    results,
    isLoading,
    error,
    page,
    setPage,
    resetSearch,
    performSearch,
  };
}

export default useSearch;
