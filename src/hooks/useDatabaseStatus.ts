import { useState, useEffect, useRef, useCallback } from 'react';
import dbClient from '@/lib/database';

interface ConnectionStatus {
  isConnected: boolean;
  lastChecked: Date | null;
  lastError: Error | null;
  retryCount: number;
  poolStatus?: {
    totalCount: number;
    idleCount: number;
    waitingCount: number;
  };
}

// Cache for the last known status to avoid unnecessary re-renders
let statusCache: {
  isConnected: boolean;
  status: any;
  timestamp: number;
} | null = null;

const CACHE_DURATION = 30000; // 30 seconds cache
const POLL_INTERVAL = 60000; // Poll every 60 seconds (reduced from 15)
const DEBOUNCE_DELAY = 500; // 500ms debounce

export function useDatabaseStatus() {
  const [isConnected, setIsConnected] = useState(false);
  const [isChecking, setIsChecking] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState<any>(null);
  const [currentDatabaseType, setCurrentDatabaseType] = useState<string>('postgres');
  const [retryCount, setRetryCount] = useState(0);

  // Refs for debouncing and preventing unnecessary updates
  const debounceTimeoutRef = useRef<NodeJS.Timeout>();
  const lastStatusRef = useRef<string>('');
  const isInitialCheckRef = useRef(true);

  // Debounced update function to prevent rapid UI changes
  const debouncedUpdate = useCallback((newIsConnected: boolean, newStatus: any, newRetryCount: number) => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      // Create a status signature to compare if anything actually changed
      const statusSignature = JSON.stringify({
        isConnected: newIsConnected,
        status: newStatus?.status,
        retryCount: newRetryCount,
        hasError: !!newStatus?.error
      });

      // Only update if the status actually changed or it's the initial check
      if (statusSignature !== lastStatusRef.current || isInitialCheckRef.current) {
        setIsConnected(newIsConnected);
        setConnectionStatus(newStatus);
        setRetryCount(newRetryCount);
        setCurrentDatabaseType('postgres');

        lastStatusRef.current = statusSignature;
        isInitialCheckRef.current = false;
      }

      setIsChecking(false);
    }, DEBOUNCE_DELAY);
  }, []);

  useEffect(() => {
    const checkConnection = async () => {
      // Check cache first
      const now = Date.now();
      if (statusCache && (now - statusCache.timestamp) < CACHE_DURATION && !isInitialCheckRef.current) {
        // Use cached data
        debouncedUpdate(statusCache.isConnected, statusCache.status, retryCount);
        return;
      }

      // Only show checking state on initial load or if we're not connected
      if (isInitialCheckRef.current || !isConnected) {
        setIsChecking(true);
      }

      try {
        // Check connection with the API
        const result = await dbClient.checkConnection();

        // Update cache
        statusCache = {
          isConnected: result.isConnected,
          status: result.status,
          timestamp: now
        };

        // Get retry count from status or increment locally
        const newRetryCount = result.status?.connection?.retryCount !== undefined
          ? result.status.connection.retryCount
          : retryCount;

        debouncedUpdate(result.isConnected, result.status, newRetryCount);
      } catch (error) {
        console.error(`Error connecting to API:`, error);

        const errorStatus = {
          error: error instanceof Error ? error.message : 'Unknown error',
          lastChecked: new Date()
        };

        // Update cache with error state
        statusCache = {
          isConnected: false,
          status: errorStatus,
          timestamp: now
        };

        debouncedUpdate(false, errorStatus, retryCount + 1);
      }
    };

    // Check connection initially
    checkConnection();

    // Set up interval to check periodically with longer interval
    const intervalId = setInterval(checkConnection, POLL_INTERVAL);

    // Clean up interval and debounce timeout on unmount
    return () => {
      clearInterval(intervalId);
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [debouncedUpdate, retryCount, isConnected]);

  return {
    isConnected,
    isChecking,
    databaseType: currentDatabaseType,
    connectionStatus,
    retryCount
  };
}
