/**
 * Hook personalizado para usar el servicio de guardado
 */

import { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { saveData as apiSaveData, EntityType, SaveDataParams } from '../services/apiService';
import { useToast } from '@/hooks/use-toast';

interface UseSaveDataOptions {
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
  showToast?: boolean;
  successMessage?: string;
  errorMessage?: string;
}

/**
 * Hook para guardar datos
 * @param entity Tipo de entidad
 * @param options Opciones
 * @returns Funciones y estado para guardar datos
 */
export const useSaveData = (entity: EntityType, options: UseSaveDataOptions = {}) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  // Opciones por defecto
  const {
    onSuccess,
    onError,
    showToast = true,
    successMessage,
    errorMessage,
  } = options;
  
  // Mutación para guardar datos
  const mutation = useMutation({
    mutationFn: async (params: Omit<SaveDataParams, 'entity'>) => {
      try {
        setIsLoading(true);
        setError(null);
        
        // Guardar datos
        const result = await apiSaveData({
          entity,
          ...params,
        });
        
        // Mostrar toast de éxito
        if (showToast) {
          toast({
            title: 'Success',
            description: successMessage || `${entity} saved successfully`,
          });
        }
        
        // Llamar a onSuccess
        if (onSuccess) {
          onSuccess(result);
        }
        
        return result;
      } catch (error: any) {
        // Establecer error
        setError(error);
        
        // Mostrar toast de error
        if (showToast) {
          toast({
            title: 'Error',
            description: errorMessage || error.message || `Failed to save ${entity}`,
            variant: 'destructive',
          });
        }
        
        // Llamar a onError
        if (onError) {
          onError(error);
        }
        
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
  });
  
  /**
   * Guarda datos
   * @param data Datos a guardar
   * @param id ID del registro (opcional, para actualización)
   * @param apiKey API key (opcional)
   * @returns Datos guardados
   */
  const saveData = async (
    data: Record<string, any>,
    id?: string,
    apiKey?: string
  ) => {
    return mutation.mutateAsync({ data, id, apiKey });
  };
  
  /**
   * Crea un nuevo registro
   * @param data Datos a guardar
   * @param apiKey API key (opcional)
   * @returns Datos guardados
   */
  const createData = async (
    data: Record<string, any>,
    apiKey?: string
  ) => {
    return saveData(data, undefined, apiKey);
  };
  
  /**
   * Actualiza un registro existente
   * @param id ID del registro
   * @param data Datos a guardar
   * @param apiKey API key (opcional)
   * @returns Datos guardados
   */
  const updateData = async (
    id: string,
    data: Record<string, any>,
    apiKey?: string
  ) => {
    return saveData(data, id, apiKey);
  };
  
  return {
    saveData,
    createData,
    updateData,
    isLoading,
    error,
  };
};

export default useSaveData;
