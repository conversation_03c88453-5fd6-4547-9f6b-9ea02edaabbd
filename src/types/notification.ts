export type NotificationType = 
  | 'application' 
  | 'message' 
  | 'interview' 
  | 'task' 
  | 'system';

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  archived: boolean;
  actionUrl?: string;
  relatedTo?: {
    type: 'candidate' | 'job' | 'task' | 'message';
    id: string;
    name: string;
  };
}
