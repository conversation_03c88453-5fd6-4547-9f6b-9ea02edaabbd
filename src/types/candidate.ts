
// Import from centralized status definition
import { PrimaryStatus as CandidateStatus } from '@/lib/constants/candidateStatus';
export type { CandidateStatus };

export type CourseProgress = {
  id: string;
  name: string;
  completed: boolean;
  completionDate?: string;
};

export type PracticeProject = {
  id: string;
  name: string;
  hoursLogged: number;
  startDate: string;
  endDate?: string;
};

export type Assessment = {
  id: string;
  type: "english" | "portfolio" | "challenge";
  status: "pending" | "passed" | "failed";
  score?: number;
  feedback?: string;
  date?: string;
  reviewer?: string;
};

export type Communication = {
  id: string;
  type: "email" | "phone" | "note";
  date: string;
  content: string;
  subject?: string;
  sender: "recruiter" | "candidate";
};

// Additional fields for candidate data
export interface CandidateAdditionalFields {
  firstName?: string;
  lastName?: string;
  location?: string;
  englishLevel?: string;
}

/**
 * Interface for Candidate data in the application - includes ALL database fields
 */
export interface Candidate extends CandidateAdditionalFields {
  id: string;
  name: string;
  email: string;
  phone?: string;
  position: string;
  status: CandidateStatus;
  secondaryStatus?: string;
  appliedDate: string;
  imageUrl?: string;
  assessments?: Assessment[];
  inTalentPool: boolean;
  isInIncubator?: boolean;
  courses?: CourseProgress[];
  projects?: PracticeProject[];
  notes?: string;
  resume?: string;
  coverLetter?: string;
  portfolio?: string;

  // Professional Information
  currentCompany?: string;
  currentPosition?: string;
  experienceYears?: number | null;
  education?: string;
  desiredSalary?: number | null;
  salaryCurrency?: string;
  availabilityDate?: string;
  source?: string;
  skills?: string[];

  // URLs and Links
  resumeUrl?: string;
  linkedinUrl?: string;
  githubUrl?: string;
  portfolioUrl?: string;
  twitterUrl?: string;

  // Assessment & Interview Information
  englishLevel?: string;
  interviewScore?: number | null;
  interviewNotes?: string;
  challenge?: string;
  challengeNotes?: string;
  challengeFeedback?: string;
  driveScore?: number | null;
  resilienceScore?: number | null;
  collaborationScore?: number | null;
  result?: string;

  // Process Information
  isDuplicate?: string;
  stargetyId?: string;

  // Timestamps
  createdAt?: string;
  updatedAt?: string;

  socialLinks?: {
    linkedin?: string;
    github?: string;
    twitter?: string;
  };
  intakeResponses?: Array<{
    question: string;
    answer: string;
  }>;
  communications: Communication[];
}

// No conversion functions needed anymore as we're using PostgreSQL directly
