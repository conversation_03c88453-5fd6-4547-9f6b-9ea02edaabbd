export interface Client {
  id: string;
  company_name: string;
  contact_name: string;
  email: string;
  phone?: string;
  industry?: string;
  location?: string;
  website?: string;
  logo_url?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  status: ClientStatus;
  jobs?: string[]; // Array of job IDs associated with this client
}

export type ClientStatus = 'active' | 'inactive' | 'lead' | 'former';

export interface ClientContact {
  id: string;
  client_id: string;
  name: string;
  position: string;
  email: string;
  phone?: string;
  is_primary: boolean;
  notes?: string;
}
