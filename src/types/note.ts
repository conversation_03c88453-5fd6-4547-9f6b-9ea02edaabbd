/**
 * Generic note types for unified notes system
 * Supports both client and candidate notes with type safety
 */

/**
 * Base interface for a note entity
 */
export interface BaseNote {
  id: string;
  user_id: string;
  content: string;
  created_at: string;
  updated_at: string;
  // Populated fields from joins
  user_name?: string;
  user_email?: string;
  user_avatar?: string;
}

/**
 * Generic note interface with entity reference
 */
export interface Note<T extends string = string> extends BaseNote {
  entity_id: string;
  entity_type: T;
}

/**
 * Client note interface
 */
export interface ClientNote extends BaseNote {
  client_id: string;
}

/**
 * Candidate note interface (extends existing)
 */
export interface CandidateNote extends BaseNote {
  candidate_id: string;
}

/**
 * Generic create note request
 */
export interface CreateNoteRequest<T extends string = string> {
  entity_id: string;
  entity_type: T;
  content: string;
}

/**
 * Specific create requests
 */
export interface CreateClientNoteRequest {
  client_id: string;
  content: string;
}

export interface CreateCandidateNoteRequest {
  candidate_id: string;
  content: string;
}

/**
 * Generic update note request
 */
export interface UpdateNoteRequest {
  content: string;
}

/**
 * Note entity types
 */
export type NoteEntityType = 'client' | 'candidate';

/**
 * Generic note response
 */
export interface NoteResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

/**
 * Note with user information for display
 */
export interface NoteWithUser<T extends BaseNote = BaseNote> extends T {
  user: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
}

/**
 * Note configuration for different entity types
 */
export interface NoteConfig {
  entityType: NoteEntityType;
  entityIdField: string;
  apiEndpoint: string;
  displayName: string;
  placeholder: string;
}

/**
 * Predefined note configurations
 */
export const NOTE_CONFIGS: Record<NoteEntityType, NoteConfig> = {
  client: {
    entityType: 'client',
    entityIdField: 'client_id',
    apiEndpoint: '/client-notes',
    displayName: 'Client',
    placeholder: 'Add a note about this client...'
  },
  candidate: {
    entityType: 'candidate',
    entityIdField: 'candidate_id',
    apiEndpoint: '/candidate-notes',
    displayName: 'Candidate',
    placeholder: 'Add a note about this candidate...'
  }
};
