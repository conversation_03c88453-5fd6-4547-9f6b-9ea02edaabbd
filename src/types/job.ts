export type JobStatus = 'open' | 'closed' | 'draft' | 'archived';

export type JobType = 'full-time' | 'part-time' | 'contract' | 'internship' | 'remote';

export interface Job {
  id: string;
  title: string;
  description: string;
  status: JobStatus;
  type: JobType;
  location: string;
  salary_range?: string;
  department?: string;
  requirements?: string[];
  responsibilities?: string[];
  posted_date: string;
  closing_date?: string;
  applicants_count: number;
  client_id: string; // Required reference to the client associated with this job
  company?: string; // Deprecated - use client relationship instead
  created_at: string;
  updated_at: string;
}

// Tipo para crear un nuevo trabajo
export interface CreateJobInput {
  title: string;
  description: string;
  status: JobStatus;
  type: JobType;
  location: string;
  salary_range?: string;
  department?: string;
  requirements?: string[];
  responsibilities?: string[];
  closing_date?: string;
  client_id: string; // Required client association
}
