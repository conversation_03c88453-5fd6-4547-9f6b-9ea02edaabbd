#!/bin/bash
set -e

# <PERSON>ript to initialize PostgreSQL database
# This script runs automatically when the PostgreSQL container starts

echo "PostgreSQL initialization script starting..."

# Function to check if a table exists
table_exists() {
  local table_name=$1
  psql -U "$POSTGRES_USER" -d "$POSTGRES_DB" -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '$table_name');" | grep -q 't'
}

# Check if the database is already initialized by checking for a key table
if table_exists "users"; then
  echo "Database already initialized. Skipping initialization."
else
  echo "Database not initialized. Running schema.sql..."

  # Run the schema.sql file to create tables
  psql -U "$POSTGRES_USER" -d "$POSTGRES_DB" -f /docker-entrypoint-initdb.d/01-schema.sql

  echo "Schema created successfully."

  # Note: Sample data file was removed as part of project cleanup
  # If you need sample data, you can create your own 02-sample-data.sql file
  if [ "$LOAD_SAMPLE_DATA" = "true" ] && [ -f "/docker-entrypoint-initdb.d/02-sample-data.sql" ]; then
    echo "Loading sample data..."
    psql -U "$POSTGRES_USER" -d "$POSTGRES_DB" -f /docker-entrypoint-initdb.d/02-sample-data.sql
    echo "Sample data loaded successfully."
  elif [ "$LOAD_SAMPLE_DATA" = "true" ]; then
    echo "Warning: LOAD_SAMPLE_DATA is true but sample data file not found."
    echo "Create /docker-entrypoint-initdb.d/02-sample-data.sql if you need sample data."
  fi
fi

echo "PostgreSQL initialization script completed."
