# Docker Compose Environment Variables - PRODUCTION READY
# ✅ Configuración segura para producción pública

# API Configuration (solo estos puertos se exponen al público)
VITE_SERVER_PORT=3001
VITE_API_URL=http://localhost:3001/api

# PostgreSQL Configuration (acceso interno solamente)
VITE_POSTGRES_HOST=postgres
VITE_POSTGRES_PORT=5432
VITE_POSTGRES_DATABASE=postgres
VITE_POSTGRES_USER=postgres
VITE_POSTGRES_PASSWORD=postgres
VITE_POSTGRES_SSL=false
VITE_USE_POSTGRES_ONLY=true

# MinIO Configuration (acceso interno solamente)
VITE_MINIO_ENDPOINT=http://minio:9000
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin

# Typesense Configuration (acceso interno solamente)
VITE_TYPESENSE_HOST=typesense
VITE_TYPESENSE_PORT=8108
VITE_TYPESENSE_PROTOCOL=http
VITE_TYPESENSE_API_KEY=xyz123
TYPESENSE_API_KEY=xyz123
TYPESENSE_DATA_DIR=/data

# PostgreSQL Database Configuration
POSTGRES_PASSWORD=postgres
POSTGRES_USER=postgres
POSTGRES_DB=postgres
LOAD_SAMPLE_DATA=true

# pgAdmin Configuration
PGADMIN_DEFAULT_EMAIL=<EMAIL>
PGADMIN_DEFAULT_PASSWORD=admin
PGADMIN_CONFIG_SERVER_MODE=False

# Node Environment
NODE_ENV=production
