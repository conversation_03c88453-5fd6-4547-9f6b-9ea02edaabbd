-- Schema for ATS Dashboard Guru

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email VARCHAR(255) NOT NULL UNIQUE,
  first_name <PERSON><PERSON><PERSON><PERSON>(100),
  last_name <PERSON><PERSON><PERSON><PERSON>(100),
  role VARCHAR(50) NOT NULL DEFAULT 'user',
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Clients (companies) table
CREATE TABLE IF NOT EXISTS clients (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_name VARCHAR(255) NOT NULL,
  industry VARCHAR(100),
  website VARCHAR(255),
  logo_url TEXT,
  address TEXT,
  city VARCHAR(100),
  state VARCHAR(100),
  zip_code VARCHAR(20),
  country VARCHAR(100),
  phone VA<PERSON>HAR(50),
  email VARCHAR(255),
  status VARCHAR(50) DEFAULT 'active',
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Jobs table
CREATE TABLE IF NOT EXISTS jobs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  requirements TEXT,
  location VARCHAR(255),
  salary_min NUMERIC,
  salary_max NUMERIC,
  salary_currency VARCHAR(10) DEFAULT 'USD',
  employment_type VARCHAR(50),
  remote_type VARCHAR(50),
  status VARCHAR(50) DEFAULT 'open',
  priority VARCHAR(50) DEFAULT 'medium',
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  published_at TIMESTAMP WITH TIME ZONE,
  closed_at TIMESTAMP WITH TIME ZONE
);

-- Candidates table
CREATE TABLE IF NOT EXISTS candidates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100),
  email VARCHAR(255) NOT NULL,
  phone VARCHAR(50),
  location VARCHAR(255),
  resume_url TEXT,
  linkedin_url TEXT,
  github_url TEXT,
  portfolio_url TEXT,
  twitter_url TEXT,
  cover_letter_url TEXT,
  skills TEXT[],
  experience_years INTEGER,
  education TEXT,
  current_company VARCHAR(255),
  current_position VARCHAR(255),
  desired_salary NUMERIC,
  salary_currency VARCHAR(10) DEFAULT 'USD',
  availability_date DATE,
  status VARCHAR(50) DEFAULT 'new' CHECK (status IN ('new', 'screening', 'interview', 'challenge', 'client_interview', 'client_feedback', 'offer', 'hired', 'rejected')),
  secondary_status VARCHAR(100) CHECK (secondary_status IS NULL OR secondary_status IN (
    -- Values from mappings.csv import table
    'Pending',
    'Message sent',
    'Initial message sent',
    'No WhatsApp - contact by email',
    'Form completed',
    'Form Finalized',
    'Appointment to be scheduled',
    'Appointment scheduled',
    'Scheduled appointment',
    'Interview finalized',
    'Interview completed',
    'Did not respond to form',
    'No show',
    'Challenge accepted',
    'Challenge to be sent',
    'Challenge sent',
    'Waiting for challenge',
    'Waiting for challenge / time expired',
    'Challenge received',
    'Presentation assigned',
    'Presentation Finalized',
    'Resume Sent to Client',
    'RTR to be send',
    'RTR sent',
    'RTR received',
    'Feedback and RTR sent',
    'Candidate ready',
    'Rejected by incubator',
    'Rejected incubator',
    'Accepted by incubator',
    'Accepted incubator',
    'Accepted by Customer',
    'Invitation to the incubator',
    'Hired',
    'Rejected / no rating',
    'I rejected / does not qualify',
    'Did not respond',
    'No response',
    'with reservations'
  )),
  source VARCHAR(100),
  notes TEXT,
  -- Additional fields for CSV import support
  stargety_id VARCHAR(50),
  is_duplicate VARCHAR(20) DEFAULT 'new',
  english_level VARCHAR(20),
  interview_score NUMERIC CHECK (interview_score >= 0 AND interview_score <= 10),
  interview_notes TEXT,
  challenge TEXT,
  challenge_notes TEXT,
  challenge_feedback TEXT,
  drive_score NUMERIC CHECK (drive_score >= 0 AND drive_score <= 10),
  resilience_score NUMERIC CHECK (resilience_score >= 0 AND resilience_score <= 10),
  collaboration_score NUMERIC CHECK (collaboration_score >= 0 AND collaboration_score <= 10),
  result TEXT,
  -- Talent Incubator Status
  is_in_incubator BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Job Applications table
CREATE TABLE IF NOT EXISTS job_applications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  job_id UUID REFERENCES jobs(id) ON DELETE CASCADE,
  candidate_id UUID REFERENCES candidates(id) ON DELETE CASCADE,
  status VARCHAR(50) DEFAULT 'applied',
  stage VARCHAR(50) DEFAULT 'new',
  applied_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  cover_letter TEXT,
  score NUMERIC,
  feedback TEXT,
  salary_expectation NUMERIC,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Interviews table
CREATE TABLE IF NOT EXISTS interviews (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  job_application_id UUID REFERENCES job_applications(id) ON DELETE CASCADE,
  interviewer_id UUID REFERENCES users(id),
  type VARCHAR(50) NOT NULL,
  scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
  duration_minutes INTEGER NOT NULL,
  location VARCHAR(255),
  meeting_link TEXT,
  status VARCHAR(50) DEFAULT 'scheduled',
  notes TEXT,
  feedback TEXT,
  score NUMERIC,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Assessments table
CREATE TABLE IF NOT EXISTS assessments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  job_application_id UUID REFERENCES job_applications(id) ON DELETE CASCADE,
  type VARCHAR(50) NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  sent_at TIMESTAMP WITH TIME ZONE,
  due_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  status VARCHAR(50) DEFAULT 'pending',
  score NUMERIC,
  feedback TEXT,
  submission_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Communications table
CREATE TABLE IF NOT EXISTS communications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  candidate_id UUID REFERENCES candidates(id) ON DELETE CASCADE,
  job_application_id UUID REFERENCES job_applications(id),
  user_id UUID REFERENCES users(id),
  type VARCHAR(50) NOT NULL,
  direction VARCHAR(50) NOT NULL,
  subject VARCHAR(255),
  content TEXT,
  sent_at TIMESTAMP WITH TIME ZONE,
  status VARCHAR(50) DEFAULT 'draft',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Candidate Notes table
CREATE TABLE IF NOT EXISTS candidate_notes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  candidate_id UUID NOT NULL REFERENCES candidates(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for candidate_notes table for better performance
CREATE INDEX IF NOT EXISTS idx_candidate_notes_candidate_id ON candidate_notes(candidate_id);
CREATE INDEX IF NOT EXISTS idx_candidate_notes_created_at ON candidate_notes(created_at);
CREATE INDEX IF NOT EXISTS idx_candidate_notes_user_id ON candidate_notes(user_id);

-- Notifications table
CREATE TABLE IF NOT EXISTS notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  type VARCHAR(50) NOT NULL,
  read BOOLEAN DEFAULT FALSE,
  action_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_jobs_client_id ON jobs(client_id);
CREATE INDEX IF NOT EXISTS idx_job_applications_job_id ON job_applications(job_id);
CREATE INDEX IF NOT EXISTS idx_job_applications_candidate_id ON job_applications(candidate_id);
CREATE INDEX IF NOT EXISTS idx_interviews_job_application_id ON interviews(job_application_id);
CREATE INDEX IF NOT EXISTS idx_assessments_job_application_id ON assessments(job_application_id);
CREATE INDEX IF NOT EXISTS idx_communications_candidate_id ON communications(candidate_id);
CREATE INDEX IF NOT EXISTS idx_communications_job_application_id ON communications(job_application_id);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
