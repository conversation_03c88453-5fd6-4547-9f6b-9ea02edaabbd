import { Page, Locator, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';

export class CandidatesPage {
  private helpers: TestHelpers;

  // Page elements
  readonly page: Page;
  readonly addCandidateButton: Locator;
  readonly searchInput: Locator;
  readonly viewToggleButtons: Locator;
  readonly listViewButton: Locator;
  readonly tableViewButton: Locator;
  readonly candidateCards: Locator;
  readonly agGrid: Locator;
  readonly agGridRows: Locator;

  // Modal elements
  readonly addCandidateModal: Locator;
  readonly editCandidateModal: Locator;
  readonly modalTitle: Locator;
  readonly saveButton: Locator;
  readonly cancelButton: Locator;

  // Form fields
  readonly firstNameInput: Locator;
  readonly lastNameInput: Locator;
  readonly emailInput: Locator;
  readonly phoneInput: Locator;
  readonly positionInput: Locator;
  readonly statusSelect: Locator;
  readonly secondaryStatusSelect: Locator;

  constructor(page: Page) {
    this.page = page;
    this.helpers = new TestHelpers(page);

    // Initialize locators
    this.addCandidateButton = page.locator('[data-testid="add-candidate-button"]');
    this.searchInput = page.locator('[data-testid="search-input"]');
    this.viewToggleButtons = page.locator('[data-testid="view-toggle"]');
    this.listViewButton = page.locator('[data-testid="list-view-button"]');
    this.tableViewButton = page.locator('[data-testid="table-view-button"]');
    this.candidateCards = page.locator('[data-testid="candidate-card"]');
    this.agGrid = page.locator('.ag-root-wrapper');
    this.agGridRows = page.locator('.ag-row');

    // Modal elements
    this.addCandidateModal = page.locator('[data-testid="add-candidate-modal"]');
    this.editCandidateModal = page.locator('[data-testid="edit-candidate-modal"]');
    this.modalTitle = page.locator('[data-testid="modal-title"]');
    this.saveButton = page.locator('[data-testid="save-button"]');
    this.cancelButton = page.locator('[data-testid="cancel-button"]');

    // Form fields
    this.firstNameInput = page.locator('[data-testid="first-name-input"]');
    this.lastNameInput = page.locator('[data-testid="last-name-input"]');
    this.emailInput = page.locator('[data-testid="email-input"]');
    this.phoneInput = page.locator('[data-testid="phone-input"]');
    this.positionInput = page.locator('[data-testid="position-input"]');
    this.statusSelect = page.locator('[data-testid="status-select"]');
    this.secondaryStatusSelect = page.locator('[data-testid="secondary-status-select"]');
  }

  /**
   * Navigate to candidates page
   */
  async goto() {
    await this.helpers.navigateTo('/candidates');
    await this.waitForPageLoad();
  }

  /**
   * Wait for the candidates page to load
   */
  async waitForPageLoad() {
    await this.helpers.waitForPageLoad();
    await this.page.waitForSelector('[data-testid="candidates-page"]', { state: 'visible' });
  }

  /**
   * Click add candidate button
   */
  async clickAddCandidate() {
    await this.helpers.clickElement('[data-testid="add-candidate-button"]');
    await this.page.waitForSelector('[data-testid="add-candidate-modal"]', { state: 'visible' });
  }

  /**
   * Fill candidate form
   */
  async fillCandidateForm(candidateData: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    position?: string;
    status?: string;
    secondaryStatus?: string;
  }) {
    await this.helpers.fillField('[data-testid="first-name-input"]', candidateData.firstName);
    await this.helpers.fillField('[data-testid="last-name-input"]', candidateData.lastName);
    await this.helpers.fillField('[data-testid="email-input"]', candidateData.email);
    
    if (candidateData.phone) {
      await this.helpers.fillField('[data-testid="phone-input"]', candidateData.phone);
    }
    
    if (candidateData.position) {
      await this.helpers.fillField('[data-testid="position-input"]', candidateData.position);
    }
    
    if (candidateData.status) {
      await this.helpers.selectOption('[data-testid="status-select"]', candidateData.status);
    }
    
    if (candidateData.secondaryStatus) {
      await this.helpers.selectOption('[data-testid="secondary-status-select"]', candidateData.secondaryStatus);
    }
  }

  /**
   * Save candidate form
   */
  async saveCandidate() {
    await this.helpers.clickElement('[data-testid="save-button"]');
    await this.helpers.waitForToast('Candidate saved successfully');
  }

  /**
   * Cancel candidate form
   */
  async cancelCandidateForm() {
    await this.helpers.clickElement('[data-testid="cancel-button"]');
  }

  /**
   * Switch to list view
   */
  async switchToListView() {
    await this.helpers.clickElement('[data-testid="list-view-button"]');
    await this.page.waitForSelector('[data-testid="candidate-card"]', { state: 'visible' });
  }

  /**
   * Switch to table view
   */
  async switchToTableView() {
    await this.helpers.clickElement('[data-testid="table-view-button"]');
    await this.helpers.waitForAGGrid();
  }

  /**
   * Search for candidates
   */
  async searchCandidates(searchTerm: string) {
    await this.helpers.fillField('[data-testid="search-input"]', searchTerm);
    await this.page.waitForTimeout(500); // Wait for search debounce
  }

  /**
   * Get candidate count in list view
   */
  async getCandidateCardCount(): Promise<number> {
    await this.page.waitForSelector('[data-testid="candidate-card"]', { state: 'visible' });
    return await this.candidateCards.count();
  }

  /**
   * Get candidate count in table view
   */
  async getCandidateTableCount(): Promise<number> {
    return await this.helpers.getAGGridRowCount();
  }

  /**
   * Click on a candidate card to view details
   */
  async clickCandidateCard(index: number = 0) {
    await this.candidateCards.nth(index).click();
    await this.page.waitForURL('**/candidates/**');
  }

  /**
   * Click on a candidate row in table view
   */
  async clickCandidateTableRow(index: number = 0) {
    await this.helpers.waitForAGGrid();
    await this.agGridRows.nth(index).click();
  }

  /**
   * Edit candidate from table view
   */
  async editCandidateFromTable(index: number = 0) {
    await this.helpers.waitForAGGrid();
    // Click on actions dropdown
    await this.helpers.clickAGGridCell(index, 'actions');
    // Click edit option
    await this.helpers.clickElement('[data-testid="edit-candidate-action"]');
    await this.page.waitForSelector('[data-testid="edit-candidate-modal"]', { state: 'visible' });
  }

  /**
   * Delete candidate from table view
   */
  async deleteCandidateFromTable(index: number = 0) {
    await this.helpers.waitForAGGrid();
    // Click on actions dropdown
    await this.helpers.clickAGGridCell(index, 'actions');
    // Click delete option
    await this.helpers.clickElement('[data-testid="delete-candidate-action"]');
    // Confirm deletion
    await this.helpers.clickElement('[data-testid="confirm-delete-button"]');
    await this.helpers.waitForToast('Candidate deleted successfully');
  }

  /**
   * Take screenshot of current view
   */
  async takeScreenshot(name: string) {
    await this.helpers.takeScreenshot(`candidates-${name}`);
  }
}
