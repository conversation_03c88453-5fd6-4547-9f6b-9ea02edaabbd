import { Page, Locator, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';

export class CandidateDetailsPage {
  private helpers: TestHelpers;

  // Page elements
  readonly page: Page;
  readonly backButton: Locator;
  readonly editButton: Locator;
  readonly candidateName: Locator;
  readonly candidatePosition: Locator;
  readonly candidateStatus: Locator;
  readonly candidateSecondaryStatus: Locator;
  readonly profileImage: Locator;
  readonly incubatorBadge: Locator;

  // Tabs
  readonly tabsList: Locator;
  readonly basicInfoTab: Locator;
  readonly professionalTab: Locator;
  readonly socialTab: Locator;
  readonly assessmentTab: Locator;
  readonly processTab: Locator;
  readonly intakeTab: Locator;

  // Tab content areas
  readonly basicInfoContent: Locator;
  readonly professionalContent: Locator;
  readonly socialContent: Locator;
  readonly assessmentContent: Locator;
  readonly processContent: Locator;
  readonly intakeContent: Locator;

  // Quick actions sidebar
  readonly quickActionsCard: Locator;
  readonly whatsappButton: Locator;
  readonly emailButton: Locator;
  readonly talentIncubatorButton: Locator;

  // Edit modal
  readonly editModal: Locator;
  readonly editModalTitle: Locator;
  readonly editSaveButton: Locator;
  readonly editCancelButton: Locator;

  constructor(page: Page) {
    this.page = page;
    this.helpers = new TestHelpers(page);

    // Initialize locators
    this.backButton = page.locator('[data-testid="back-button"]');
    this.editButton = page.locator('[data-testid="edit-candidate-button"]');
    this.candidateName = page.locator('[data-testid="candidate-name"]');
    this.candidatePosition = page.locator('[data-testid="candidate-position"]');
    this.candidateStatus = page.locator('[data-testid="candidate-status"]');
    this.candidateSecondaryStatus = page.locator('[data-testid="candidate-secondary-status"]');
    this.profileImage = page.locator('[data-testid="profile-image"]');
    this.incubatorBadge = page.locator('[data-testid="incubator-badge"]');

    // Tabs
    this.tabsList = page.locator('[data-testid="candidate-tabs"]');
    this.basicInfoTab = page.locator('[data-testid="basic-info-tab"]');
    this.professionalTab = page.locator('[data-testid="professional-tab"]');
    this.socialTab = page.locator('[data-testid="social-tab"]');
    this.assessmentTab = page.locator('[data-testid="assessment-tab"]');
    this.processTab = page.locator('[data-testid="process-tab"]');
    this.intakeTab = page.locator('[data-testid="intake-tab"]');

    // Tab content
    this.basicInfoContent = page.locator('[data-testid="basic-info-content"]');
    this.professionalContent = page.locator('[data-testid="professional-content"]');
    this.socialContent = page.locator('[data-testid="social-content"]');
    this.assessmentContent = page.locator('[data-testid="assessment-content"]');
    this.processContent = page.locator('[data-testid="process-content"]');
    this.intakeContent = page.locator('[data-testid="intake-content"]');

    // Quick actions
    this.quickActionsCard = page.locator('[data-testid="quick-actions"]');
    this.whatsappButton = page.locator('[data-testid="whatsapp-button"]');
    this.emailButton = page.locator('[data-testid="email-button"]');
    this.talentIncubatorButton = page.locator('[data-testid="talent-incubator-button"]');

    // Edit modal
    this.editModal = page.locator('[data-testid="edit-candidate-modal"]');
    this.editModalTitle = page.locator('[data-testid="edit-modal-title"]');
    this.editSaveButton = page.locator('[data-testid="edit-save-button"]');
    this.editCancelButton = page.locator('[data-testid="edit-cancel-button"]');
  }

  /**
   * Navigate to candidate details page
   */
  async goto(candidateId: string) {
    await this.helpers.navigateTo(`/candidates/${candidateId}`);
    await this.waitForPageLoad();
  }

  /**
   * Wait for the candidate details page to load
   */
  async waitForPageLoad() {
    await this.helpers.waitForPageLoad();
    await this.page.waitForSelector('[data-testid="candidate-details-page"]', { state: 'visible' });
  }

  /**
   * Go back to candidates list
   */
  async goBack() {
    await this.helpers.clickElement('[data-testid="back-button"]');
    await this.page.waitForURL('**/candidates');
  }

  /**
   * Click edit candidate button
   */
  async clickEditCandidate() {
    await this.helpers.clickElement('[data-testid="edit-candidate-button"]');
    await this.page.waitForSelector('[data-testid="edit-candidate-modal"]', { state: 'visible' });
  }

  /**
   * Switch to a specific tab
   */
  async switchToTab(tabName: 'basic' | 'professional' | 'social' | 'assessment' | 'process' | 'intake') {
    const tabSelector = `[data-testid="${tabName}-info-tab"]`;
    await this.helpers.clickElement(tabSelector);
    await this.page.waitForSelector(`[data-testid="${tabName}-info-content"]`, { state: 'visible' });
  }

  /**
   * Verify candidate information is displayed
   */
  async verifyCandidateInfo(expectedData: {
    name?: string;
    position?: string;
    status?: string;
    secondaryStatus?: string;
  }) {
    if (expectedData.name) {
      await expect(this.candidateName).toContainText(expectedData.name);
    }
    
    if (expectedData.position) {
      await expect(this.candidatePosition).toContainText(expectedData.position);
    }
    
    if (expectedData.status) {
      await expect(this.candidateStatus).toContainText(expectedData.status);
    }
    
    if (expectedData.secondaryStatus) {
      await expect(this.candidateSecondaryStatus).toContainText(expectedData.secondaryStatus);
    }
  }

  /**
   * Verify tab content is visible
   */
  async verifyTabContent(tabName: string) {
    const contentSelector = `[data-testid="${tabName}-info-content"]`;
    await expect(this.page.locator(contentSelector)).toBeVisible();
  }

  /**
   * Check if candidate is in talent incubator
   */
  async isInTalentIncubator(): Promise<boolean> {
    return await this.incubatorBadge.isVisible();
  }

  /**
   * Add/Remove from talent incubator
   */
  async toggleTalentIncubator() {
    await this.helpers.clickElement('[data-testid="talent-incubator-button"]');
    await this.helpers.waitForToast();
  }

  /**
   * Send WhatsApp message
   */
  async sendWhatsAppMessage() {
    await this.helpers.clickElement('[data-testid="whatsapp-button"]');
    // Note: This will open external WhatsApp, so we just verify the click works
  }

  /**
   * Send email
   */
  async sendEmail() {
    await this.helpers.clickElement('[data-testid="email-button"]');
    // Wait for email modal or external email client
    await this.page.waitForTimeout(1000);
  }

  /**
   * Get field value from any tab
   */
  async getFieldValue(fieldTestId: string): Promise<string> {
    const field = this.page.locator(`[data-testid="${fieldTestId}"]`);
    await expect(field).toBeVisible();
    return await field.textContent() || '';
  }

  /**
   * Verify all tabs are present
   */
  async verifyAllTabsPresent() {
    await expect(this.basicInfoTab).toBeVisible();
    await expect(this.professionalTab).toBeVisible();
    await expect(this.socialTab).toBeVisible();
    await expect(this.assessmentTab).toBeVisible();
    await expect(this.processTab).toBeVisible();
    await expect(this.intakeTab).toBeVisible();
  }

  /**
   * Verify quick actions are present
   */
  async verifyQuickActionsPresent() {
    await expect(this.quickActionsCard).toBeVisible();
    await expect(this.whatsappButton).toBeVisible();
    await expect(this.emailButton).toBeVisible();
  }

  /**
   * Take screenshot of current tab
   */
  async takeScreenshot(name: string) {
    await this.helpers.takeScreenshot(`candidate-details-${name}`);
  }

  /**
   * Edit candidate information
   */
  async editCandidateInfo(updatedData: {
    firstName?: string;
    lastName?: string;
    email?: string;
    phone?: string;
    position?: string;
  }) {
    await this.clickEditCandidate();
    
    if (updatedData.firstName) {
      await this.helpers.fillField('[data-testid="edit-first-name"]', updatedData.firstName);
    }
    
    if (updatedData.lastName) {
      await this.helpers.fillField('[data-testid="edit-last-name"]', updatedData.lastName);
    }
    
    if (updatedData.email) {
      await this.helpers.fillField('[data-testid="edit-email"]', updatedData.email);
    }
    
    if (updatedData.phone) {
      await this.helpers.fillField('[data-testid="edit-phone"]', updatedData.phone);
    }
    
    if (updatedData.position) {
      await this.helpers.fillField('[data-testid="edit-position"]', updatedData.position);
    }
    
    await this.helpers.clickElement('[data-testid="edit-save-button"]');
    await this.helpers.waitForToast('Candidate updated successfully');
  }
}
