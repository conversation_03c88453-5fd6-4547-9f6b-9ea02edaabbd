import { Page, Locator, expect } from '@playwright/test';

export class JobFormPage {
  readonly page: Page;
  readonly titleInput: Locator;
  readonly descriptionInput: Locator;
  readonly locationInput: Locator;
  readonly clientSelect: Locator;
  readonly typeSelect: Locator;
  readonly statusSelect: Locator;
  readonly submitButton: Locator;
  readonly addClientButton: Locator;
  readonly clientDescription: Locator;

  constructor(page: Page) {
    this.page = page;
    this.titleInput = page.locator('[name="title"]');
    this.descriptionInput = page.locator('[name="description"]');
    this.locationInput = page.locator('[name="location"]');
    this.clientSelect = page.locator('[name="client_id"]');
    this.typeSelect = page.locator('[name="type"]');
    this.statusSelect = page.locator('[name="status"]');
    this.submitButton = page.locator('button[type="submit"]');
    this.addClientButton = page.locator('button[type="button"]').filter({ hasText: '+' });
    this.clientDescription = page.locator('[data-testid="client-field-description"]');
  }

  async goto() {
    await this.page.goto('/jobs/new');
  }

  async gotoWithClient(clientId: string) {
    await this.page.goto(`/jobs/new?client=${clientId}`);
  }

  async fillJobForm(jobData: {
    title: string;
    description: string;
    location: string;
    type?: string;
    status?: string;
    clientId?: string;
  }) {
    await this.titleInput.fill(jobData.title);
    await this.descriptionInput.fill(jobData.description);
    await this.locationInput.fill(jobData.location);

    if (jobData.type) {
      await this.typeSelect.click();
      await this.page.click(`text=${jobData.type}`);
    }

    if (jobData.status) {
      await this.statusSelect.click();
      await this.page.click(`text=${jobData.status}`);
    }

    if (jobData.clientId && !await this.clientSelect.isDisabled()) {
      await this.clientSelect.click();
      await this.page.click(`[value="${jobData.clientId}"]`);
    }
  }

  async submitForm() {
    await this.submitButton.click();
  }

  async verifyClientFieldLocked() {
    await expect(this.clientSelect).toBeDisabled();
    await expect(this.addClientButton).toBeDisabled();
    await expect(this.page.locator('text=Client is pre-selected from the client details page')).toBeVisible();
  }

  async verifyClientFieldUnlocked() {
    await expect(this.clientSelect).not.toBeDisabled();
    await expect(this.addClientButton).not.toBeDisabled();
    await expect(this.page.locator('text=Select the client this job is for')).toBeVisible();
  }

  async verifyFormLoaded() {
    await this.page.waitForSelector('form');
    await expect(this.titleInput).toBeVisible();
    await expect(this.descriptionInput).toBeVisible();
    await expect(this.clientSelect).toBeVisible();
  }

  async verifySuccessMessage() {
    await expect(this.page.locator('text=Job created successfully')).toBeVisible();
  }

  async verifyWarningMessage(message: string) {
    await expect(this.page.locator(`text=${message}`)).toBeVisible();
  }
}
