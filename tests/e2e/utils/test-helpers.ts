import { Page, expect } from '@playwright/test';

/**
 * Test utilities and helper functions for E2E tests
 */

export class TestHelpers {
  constructor(private page: Page) {}

  /**
   * Wait for the page to load completely
   */
  async waitForPageLoad() {
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForLoadState('domcontentloaded');
  }

  /**
   * Take a screenshot with a descriptive name
   */
  async takeScreenshot(name: string) {
    await this.page.screenshot({ 
      path: `test-results/screenshots/${name}-${Date.now()}.png`,
      fullPage: true 
    });
  }

  /**
   * Wait for an element to be visible and stable
   */
  async waitForElement(selector: string, timeout = 10000) {
    await this.page.waitForSelector(selector, { state: 'visible', timeout });
    await this.page.waitForTimeout(100); // Small delay for stability
  }

  /**
   * Fill form field with validation
   */
  async fillField(selector: string, value: string) {
    await this.waitForElement(selector);
    await this.page.fill(selector, value);
    await expect(this.page.locator(selector)).toHaveValue(value);
  }

  /**
   * Click element with wait
   */
  async clickElement(selector: string) {
    await this.waitForElement(selector);
    await this.page.click(selector);
  }

  /**
   * Select option from dropdown
   */
  async selectOption(selector: string, value: string) {
    await this.waitForElement(selector);
    await this.page.selectOption(selector, value);
  }

  /**
   * Wait for toast notification
   */
  async waitForToast(message?: string) {
    const toastSelector = '[data-sonner-toast]';
    await this.waitForElement(toastSelector);
    
    if (message) {
      await expect(this.page.locator(toastSelector)).toContainText(message);
    }
    
    return this.page.locator(toastSelector);
  }

  /**
   * Navigate to a specific route
   */
  async navigateTo(path: string) {
    await this.page.goto(path);
    await this.waitForPageLoad();
  }

  /**
   * Check if element exists
   */
  async elementExists(selector: string): Promise<boolean> {
    try {
      await this.page.waitForSelector(selector, { timeout: 1000 });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get table row count
   */
  async getTableRowCount(tableSelector: string): Promise<number> {
    await this.waitForElement(tableSelector);
    const rows = await this.page.locator(`${tableSelector} tbody tr`).count();
    return rows;
  }

  /**
   * Wait for AG Grid to load
   */
  async waitForAGGrid() {
    await this.page.waitForSelector('.ag-root-wrapper', { state: 'visible' });
    await this.page.waitForSelector('.ag-row', { state: 'visible' });
    await this.page.waitForTimeout(500); // Allow grid to stabilize
  }

  /**
   * Get AG Grid row count
   */
  async getAGGridRowCount(): Promise<number> {
    await this.waitForAGGrid();
    return await this.page.locator('.ag-row').count();
  }

  /**
   * Click AG Grid cell
   */
  async clickAGGridCell(row: number, column: string) {
    await this.waitForAGGrid();
    const cellSelector = `.ag-row[row-index="${row}"] .ag-cell[col-id="${column}"]`;
    await this.clickElement(cellSelector);
  }
}
