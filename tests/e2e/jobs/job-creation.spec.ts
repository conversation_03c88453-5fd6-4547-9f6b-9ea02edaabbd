import { test, expect } from '@playwright/test';
import { JobFormPage } from '../pages/JobFormPage';

test.describe('Job Creation with Preselected Client', () => {
  let jobFormPage: JobFormPage;

  test.beforeEach(async ({ page }) => {
    jobFormPage = new JobFormPage(page);
    // Navigate to the application
    await page.goto('/');
  });

  test('should pre-populate and lock client field when navigating from client details', async ({ page }) => {
    // First, navigate to clients page
    await page.goto('/clients');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Click on the first client to go to client details
    const firstClientLink = page.locator('[data-testid="client-item"]').first();
    if (await firstClientLink.count() > 0) {
      await firstClientLink.click();
    } else {
      // If no clients exist, create one first
      await page.click('text=Add New Client');
      await page.fill('[name="company_name"]', 'Test Company');
      await page.fill('[name="contact_name"]', '<PERSON>');
      await page.fill('[name="email"]', '<EMAIL>');
      await page.click('button[type="submit"]');
      await page.waitForURL('**/clients/**');
    }
    
    // Now we should be on a client details page
    await expect(page).toHaveURL(/\/clients\/[^\/]+$/);
    
    // Click on "Add New Job" button
    await page.click('text=Add New Job');
    
    // Verify we're on the job creation page with client parameter
    await expect(page).toHaveURL(/\/jobs\/new\?client=.+/);
    
    // Wait for the form to load
    await page.waitForSelector('form');
    
    // Verify that the client field is pre-populated and disabled
    const clientSelect = page.locator('[name="client_id"]');
    await expect(clientSelect).toBeDisabled();
    
    // Verify the description text indicates the field is locked
    await expect(page.locator('text=Client is pre-selected from the client details page')).toBeVisible();
    
    // Verify the "+" button for adding new client is also disabled
    const addClientButton = page.locator('button[type="button"]').filter({ hasText: '+' });
    await expect(addClientButton).toBeDisabled();
    
    // Fill out the rest of the form to test submission
    await page.fill('[name="title"]', 'Test Job Position');
    await page.fill('[name="description"]', 'This is a test job description for automated testing purposes.');
    await page.fill('[name="location"]', 'Remote');
    
    // Select job type
    await page.click('[name="type"]');
    await page.click('text=full-time');
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Verify successful creation (should redirect to jobs list)
    await expect(page).toHaveURL('/jobs');
    
    // Verify success message appears
    await expect(page.locator('text=Job created successfully')).toBeVisible();
  });

  test('should allow normal client selection when accessing job creation directly', async ({ page }) => {
    // Navigate directly to job creation page without client parameter
    await page.goto('/jobs/new');
    
    // Wait for the form to load
    await page.waitForSelector('form');
    
    // Verify that the client field is NOT disabled
    const clientSelect = page.locator('[name="client_id"]');
    await expect(clientSelect).not.toBeDisabled();
    
    // Verify the description text is the normal one
    await expect(page.locator('text=Select the client this job is for')).toBeVisible();
    
    // Verify the "+" button for adding new client is enabled
    const addClientButton = page.locator('button[type="button"]').filter({ hasText: '+' });
    await expect(addClientButton).not.toBeDisabled();
  });

  test('should show warning when invalid client ID is provided in URL', async ({ page }) => {
    // Navigate to job creation page with invalid client ID
    await page.goto('/jobs/new?client=invalid-client-id');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Verify warning message appears
    await expect(page.locator('text=The specified client was not found')).toBeVisible();
    
    // Verify the client field is not locked (since client wasn't found)
    const clientSelect = page.locator('[name="client_id"]');
    await expect(clientSelect).not.toBeDisabled();
  });
});
