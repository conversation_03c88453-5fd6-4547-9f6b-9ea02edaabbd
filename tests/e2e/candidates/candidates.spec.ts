import { test, expect } from '@playwright/test';
import { CandidatesPage } from '../pages/CandidatesPage';
import { CandidateDetailsPage } from '../pages/CandidateDetailsPage';
import { 
  validCandidateData, 
  minimalCandidateData, 
  candidateDataForEdit,
  updatedCandidateData,
  invalidCandidateData,
  generateRandomCandidate
} from '../fixtures/candidate-data';

test.describe('Candidates Management', () => {
  let candidatesPage: CandidatesPage;
  let candidateDetailsPage: CandidateDetailsPage;

  test.beforeEach(async ({ page }) => {
    candidatesPage = new CandidatesPage(page);
    candidateDetailsPage = new CandidateDetailsPage(page);
    
    // Navigate to candidates page before each test
    await candidatesPage.goto();
  });

  test.describe('Candidates List View', () => {
    test('should display candidates page with correct elements', async () => {
      // Verify page loads correctly
      await expect(candidatesPage.page).toHaveTitle(/Candidates/);
      
      // Verify main elements are present
      await expect(candidatesPage.addCandidateButton).toBeVisible();
      await expect(candidatesPage.searchInput).toBeVisible();
      await expect(candidatesPage.viewToggleButtons).toBeVisible();
      
      // Take screenshot for visual verification
      await candidatesPage.takeScreenshot('candidates-list-initial');
    });

    test('should switch between list and table views', async () => {
      // Start in list view (default)
      await candidatesPage.switchToListView();
      await expect(candidatesPage.candidateCards.first()).toBeVisible();
      await candidatesPage.takeScreenshot('candidates-list-view');
      
      // Switch to table view
      await candidatesPage.switchToTableView();
      await expect(candidatesPage.agGrid).toBeVisible();
      await candidatesPage.takeScreenshot('candidates-table-view');
      
      // Switch back to list view
      await candidatesPage.switchToListView();
      await expect(candidatesPage.candidateCards.first()).toBeVisible();
    });

    test('should search candidates', async () => {
      // Get initial count
      const initialCount = await candidatesPage.getCandidateCardCount();
      
      // Search for a specific term
      await candidatesPage.searchCandidates('Software');
      
      // Wait for search results
      await candidatesPage.page.waitForTimeout(1000);
      
      // Verify search results (count should be different or same)
      const searchCount = await candidatesPage.getCandidateCardCount();
      expect(searchCount).toBeGreaterThanOrEqual(0);
      
      // Clear search
      await candidatesPage.searchCandidates('');
      await candidatesPage.page.waitForTimeout(1000);
      
      // Verify we're back to original results
      const finalCount = await candidatesPage.getCandidateCardCount();
      expect(finalCount).toBe(initialCount);
    });
  });

  test.describe('Candidate Creation', () => {
    test('should create a new candidate with complete data', async () => {
      // Click add candidate button
      await candidatesPage.clickAddCandidate();
      
      // Fill form with valid data
      await candidatesPage.fillCandidateForm(validCandidateData);
      
      // Take screenshot of filled form
      await candidatesPage.takeScreenshot('candidate-form-filled');
      
      // Save candidate
      await candidatesPage.saveCandidate();
      
      // Verify success message and redirect
      await expect(candidatesPage.page).toHaveURL(/.*candidates.*/);
      
      // Verify candidate appears in list
      await candidatesPage.searchCandidates(validCandidateData.email);
      await candidatesPage.page.waitForTimeout(1000);
      
      const searchResults = await candidatesPage.getCandidateCardCount();
      expect(searchResults).toBeGreaterThan(0);
    });

    test('should create a candidate with minimal required data', async () => {
      await candidatesPage.clickAddCandidate();
      await candidatesPage.fillCandidateForm(minimalCandidateData);
      await candidatesPage.saveCandidate();
      
      // Verify candidate was created
      await candidatesPage.searchCandidates(minimalCandidateData.email);
      await candidatesPage.page.waitForTimeout(1000);
      
      const searchResults = await candidatesPage.getCandidateCardCount();
      expect(searchResults).toBeGreaterThan(0);
    });

    test('should validate required fields', async () => {
      await candidatesPage.clickAddCandidate();
      
      // Try to save without filling required fields
      await candidatesPage.page.click('[data-testid="save-button"]');
      
      // Verify validation errors appear
      await expect(candidatesPage.page.locator('.error-message')).toBeVisible();
      
      // Cancel form
      await candidatesPage.cancelCandidateForm();
    });

    test('should cancel candidate creation', async () => {
      await candidatesPage.clickAddCandidate();
      await candidatesPage.fillCandidateForm(validCandidateData);
      await candidatesPage.cancelCandidateForm();
      
      // Verify modal is closed and we're back to candidates list
      await expect(candidatesPage.addCandidateModal).not.toBeVisible();
    });
  });

  test.describe('Candidate Details View', () => {
    test('should view candidate details', async () => {
      // First create a candidate to view
      const testCandidate = generateRandomCandidate();
      await candidatesPage.clickAddCandidate();
      await candidatesPage.fillCandidateForm(testCandidate);
      await candidatesPage.saveCandidate();
      
      // Search for the candidate and click to view details
      await candidatesPage.searchCandidates(testCandidate.email);
      await candidatesPage.page.waitForTimeout(1000);
      await candidatesPage.clickCandidateCard(0);
      
      // Verify we're on the details page
      await candidateDetailsPage.waitForPageLoad();
      await expect(candidateDetailsPage.page).toHaveURL(/.*candidates\/\d+/);
      
      // Verify candidate information is displayed
      await candidateDetailsPage.verifyCandidateInfo({
        name: `${testCandidate.firstName} ${testCandidate.lastName}`,
        position: testCandidate.position
      });
      
      // Verify all tabs are present
      await candidateDetailsPage.verifyAllTabsPresent();
      
      // Take screenshot
      await candidateDetailsPage.takeScreenshot('candidate-details-basic');
    });

    test('should navigate through all tabs', async () => {
      // Navigate to first candidate details
      await candidatesPage.clickCandidateCard(0);
      await candidateDetailsPage.waitForPageLoad();
      
      // Test each tab
      const tabs = ['basic', 'professional', 'social', 'assessment', 'process', 'intake'] as const;
      
      for (const tab of tabs) {
        await candidateDetailsPage.switchToTab(tab);
        await candidateDetailsPage.verifyTabContent(tab);
        await candidateDetailsPage.takeScreenshot(`candidate-details-${tab}-tab`);
      }
    });

    test('should display quick actions', async () => {
      await candidatesPage.clickCandidateCard(0);
      await candidateDetailsPage.waitForPageLoad();
      
      // Verify quick actions are present
      await candidateDetailsPage.verifyQuickActionsPresent();
      
      // Test WhatsApp button (just verify it's clickable)
      await expect(candidateDetailsPage.whatsappButton).toBeEnabled();
      
      // Test email button
      await expect(candidateDetailsPage.emailButton).toBeEnabled();
    });

    test('should go back to candidates list', async () => {
      await candidatesPage.clickCandidateCard(0);
      await candidateDetailsPage.waitForPageLoad();
      
      // Click back button
      await candidateDetailsPage.goBack();
      
      // Verify we're back on candidates page
      await expect(candidateDetailsPage.page).toHaveURL(/.*candidates$/);
      await candidatesPage.waitForPageLoad();
    });
  });

  test.describe('Candidate Editing', () => {
    test('should edit candidate from details page', async () => {
      // Create a test candidate
      const testCandidate = generateRandomCandidate();
      await candidatesPage.clickAddCandidate();
      await candidatesPage.fillCandidateForm(testCandidate);
      await candidatesPage.saveCandidate();
      
      // Navigate to details
      await candidatesPage.searchCandidates(testCandidate.email);
      await candidatesPage.page.waitForTimeout(1000);
      await candidatesPage.clickCandidateCard(0);
      await candidateDetailsPage.waitForPageLoad();
      
      // Edit candidate
      await candidateDetailsPage.editCandidateInfo(updatedCandidateData);
      
      // Verify changes are reflected
      if (updatedCandidateData.firstName) {
        await candidateDetailsPage.verifyCandidateInfo({
          name: updatedCandidateData.firstName
        });
      }
    });

    test('should edit candidate from table view', async () => {
      // Switch to table view
      await candidatesPage.switchToTableView();
      
      // Edit first candidate
      await candidatesPage.editCandidateFromTable(0);
      
      // Verify edit modal opens
      await expect(candidatesPage.editCandidateModal).toBeVisible();
      
      // Make some changes and save
      await candidatesPage.page.fill('[data-testid="edit-first-name"]', 'Updated Name');
      await candidatesPage.page.click('[data-testid="edit-save-button"]');
      
      // Verify success
      await candidatesPage.page.waitForSelector('[data-sonner-toast]', { state: 'visible' });
    });
  });

  test.describe('Table View Functionality', () => {
    test('should display candidates in table format', async () => {
      await candidatesPage.switchToTableView();
      
      // Verify AG Grid is loaded
      await expect(candidatesPage.agGrid).toBeVisible();
      
      // Verify we have rows
      const rowCount = await candidatesPage.getCandidateTableCount();
      expect(rowCount).toBeGreaterThan(0);
      
      // Take screenshot
      await candidatesPage.takeScreenshot('candidates-table-loaded');
    });

    test('should sort candidates in table', async () => {
      await candidatesPage.switchToTableView();
      
      // Click on a column header to sort
      await candidatesPage.page.click('.ag-header-cell[col-id="name"] .ag-header-cell-label');
      await candidatesPage.page.waitForTimeout(500);
      
      // Take screenshot of sorted table
      await candidatesPage.takeScreenshot('candidates-table-sorted');
    });

    test('should filter candidates in table', async () => {
      await candidatesPage.switchToTableView();
      
      // Open column menu for filtering (if available)
      await candidatesPage.page.click('.ag-header-cell[col-id="status"] .ag-header-cell-menu-button');
      await candidatesPage.page.waitForTimeout(500);
      
      // Take screenshot
      await candidatesPage.takeScreenshot('candidates-table-filter-menu');
    });
  });

  test.describe('Talent Incubator Functionality', () => {
    test('should add candidate to talent incubator', async () => {
      // Navigate to candidate details
      await candidatesPage.clickCandidateCard(0);
      await candidateDetailsPage.waitForPageLoad();

      // Check initial incubator status
      const initialStatus = await candidateDetailsPage.isInTalentIncubator();

      // Toggle talent incubator
      await candidateDetailsPage.toggleTalentIncubator();

      // Verify status changed
      const newStatus = await candidateDetailsPage.isInTalentIncubator();
      expect(newStatus).toBe(!initialStatus);

      // Take screenshot
      await candidateDetailsPage.takeScreenshot('talent-incubator-toggled');
    });
  });

  test.describe('Visual Regression Tests', () => {
    test('should match candidates list visual snapshot', async () => {
      await candidatesPage.waitForPageLoad();
      await expect(candidatesPage.page).toHaveScreenshot('candidates-list.png');
    });

    test('should match candidates table visual snapshot', async () => {
      await candidatesPage.switchToTableView();
      await expect(candidatesPage.page).toHaveScreenshot('candidates-table.png');
    });

    test('should match candidate details visual snapshot', async () => {
      await candidatesPage.clickCandidateCard(0);
      await candidateDetailsPage.waitForPageLoad();
      await expect(candidateDetailsPage.page).toHaveScreenshot('candidate-details.png');
    });

    test('should match add candidate modal visual snapshot', async () => {
      await candidatesPage.clickAddCandidate();
      await expect(candidatesPage.page).toHaveScreenshot('add-candidate-modal.png');
    });
  });
});
