import { test, expect } from '@playwright/test';
import { CandidatesPage } from '../pages/CandidatesPage';
import { 
  searchTestData, 
  candidatesListData,
  generateRandomCandidates
} from '../fixtures/candidate-data';

test.describe('Candidate Search and Filtering', () => {
  let candidatesPage: CandidatesPage;

  test.beforeEach(async ({ page }) => {
    candidatesPage = new CandidatesPage(page);
    await candidatesPage.goto();
  });

  test.describe('Search Functionality', () => {
    test('should search candidates by name', async () => {
      // Search by first name
      await candidatesPage.searchCandidates('John');
      await candidatesPage.page.waitForTimeout(1000);
      
      const results = await candidatesPage.getCandidateCardCount();
      expect(results).toBeGreaterThanOrEqual(0);
      
      // Take screenshot of search results
      await candidatesPage.takeScreenshot('search-by-name');
      
      // Clear search
      await candidatesPage.searchCandidates('');
      await candidatesPage.page.waitForTimeout(1000);
    });

    test('should search candidates by email', async () => {
      await candidatesPage.searchCandidates('@example.com');
      await candidatesPage.page.waitForTimeout(1000);
      
      const results = await candidatesPage.getCandidateCardCount();
      expect(results).toBeGreaterThanOrEqual(0);
      
      await candidatesPage.takeScreenshot('search-by-email');
    });

    test('should search candidates by position', async () => {
      await candidatesPage.searchCandidates('Developer');
      await candidatesPage.page.waitForTimeout(1000);
      
      const results = await candidatesPage.getCandidateCardCount();
      expect(results).toBeGreaterThanOrEqual(0);
      
      await candidatesPage.takeScreenshot('search-by-position');
    });

    test('should handle empty search results', async () => {
      await candidatesPage.searchCandidates('NonExistentCandidate12345');
      await candidatesPage.page.waitForTimeout(1000);
      
      const results = await candidatesPage.getCandidateCardCount();
      expect(results).toBe(0);
      
      // Verify empty state message
      await expect(candidatesPage.page.locator('[data-testid="no-results-message"]')).toBeVisible();
      
      await candidatesPage.takeScreenshot('search-no-results');
    });

    test('should clear search and show all candidates', async () => {
      // Get initial count
      const initialCount = await candidatesPage.getCandidateCardCount();
      
      // Search for something specific
      await candidatesPage.searchCandidates('Developer');
      await candidatesPage.page.waitForTimeout(1000);
      
      // Clear search
      await candidatesPage.searchCandidates('');
      await candidatesPage.page.waitForTimeout(1000);
      
      // Verify we're back to all candidates
      const finalCount = await candidatesPage.getCandidateCardCount();
      expect(finalCount).toBe(initialCount);
    });

    test('should search with special characters', async () => {
      await candidatesPage.searchCandidates('@#$%');
      await candidatesPage.page.waitForTimeout(1000);
      
      // Should not crash and should handle gracefully
      const results = await candidatesPage.getCandidateCardCount();
      expect(results).toBeGreaterThanOrEqual(0);
    });
  });

  test.describe('Table Search and Filtering', () => {
    test('should search in table view', async () => {
      // Switch to table view
      await candidatesPage.switchToTableView();
      
      // Search for candidates
      await candidatesPage.searchCandidates('Software');
      await candidatesPage.page.waitForTimeout(1000);
      
      const results = await candidatesPage.getCandidateTableCount();
      expect(results).toBeGreaterThanOrEqual(0);
      
      await candidatesPage.takeScreenshot('table-search-results');
    });

    test('should use AG Grid quick filter', async () => {
      await candidatesPage.switchToTableView();
      
      // Use AG Grid's built-in quick filter if available
      const quickFilterInput = candidatesPage.page.locator('.ag-input-field-input[placeholder*="filter"]');
      
      if (await quickFilterInput.isVisible()) {
        await quickFilterInput.fill('Developer');
        await candidatesPage.page.waitForTimeout(1000);
        
        const results = await candidatesPage.getCandidateTableCount();
        expect(results).toBeGreaterThanOrEqual(0);
        
        await candidatesPage.takeScreenshot('ag-grid-quick-filter');
      }
    });

    test('should filter by status column', async () => {
      await candidatesPage.switchToTableView();
      
      // Try to access column filter menu
      const statusHeader = candidatesPage.page.locator('.ag-header-cell[col-id="status"]');
      
      if (await statusHeader.isVisible()) {
        // Click on status column header
        await statusHeader.click();
        await candidatesPage.page.waitForTimeout(500);
        
        await candidatesPage.takeScreenshot('status-column-filter');
      }
    });

    test('should sort by different columns', async () => {
      await candidatesPage.switchToTableView();
      
      // Test sorting by name column
      const nameHeader = candidatesPage.page.locator('.ag-header-cell[col-id="name"]');
      if (await nameHeader.isVisible()) {
        await nameHeader.click();
        await candidatesPage.page.waitForTimeout(500);
        await candidatesPage.takeScreenshot('sorted-by-name-asc');
        
        // Click again for descending sort
        await nameHeader.click();
        await candidatesPage.page.waitForTimeout(500);
        await candidatesPage.takeScreenshot('sorted-by-name-desc');
      }
      
      // Test sorting by status column
      const statusHeader = candidatesPage.page.locator('.ag-header-cell[col-id="status"]');
      if (await statusHeader.isVisible()) {
        await statusHeader.click();
        await candidatesPage.page.waitForTimeout(500);
        await candidatesPage.takeScreenshot('sorted-by-status');
      }
    });
  });

  test.describe('Advanced Search Scenarios', () => {
    test('should handle rapid search input changes', async () => {
      // Simulate rapid typing
      const searchTerms = ['J', 'Jo', 'Joh', 'John', 'Joh', 'Jo', 'J', ''];
      
      for (const term of searchTerms) {
        await candidatesPage.searchCandidates(term);
        await candidatesPage.page.waitForTimeout(100); // Short delay between searches
      }
      
      // Final search should work correctly
      await candidatesPage.searchCandidates('Developer');
      await candidatesPage.page.waitForTimeout(1000);
      
      const results = await candidatesPage.getCandidateCardCount();
      expect(results).toBeGreaterThanOrEqual(0);
    });

    test('should maintain search state when switching views', async () => {
      // Search in list view
      await candidatesPage.searchCandidates('Engineer');
      await candidatesPage.page.waitForTimeout(1000);
      
      const listResults = await candidatesPage.getCandidateCardCount();
      
      // Switch to table view
      await candidatesPage.switchToTableView();
      await candidatesPage.page.waitForTimeout(1000);
      
      const tableResults = await candidatesPage.getCandidateTableCount();
      
      // Results should be consistent (or at least both > 0 if search found results)
      if (listResults > 0) {
        expect(tableResults).toBeGreaterThan(0);
      } else {
        expect(tableResults).toBe(0);
      }
      
      await candidatesPage.takeScreenshot('search-state-maintained');
    });

    test('should search with case insensitive matching', async () => {
      // Test different cases
      const searchTerms = ['developer', 'DEVELOPER', 'Developer', 'dEvElOpEr'];
      
      let previousResults = -1;
      
      for (const term of searchTerms) {
        await candidatesPage.searchCandidates(term);
        await candidatesPage.page.waitForTimeout(1000);
        
        const results = await candidatesPage.getCandidateCardCount();
        
        if (previousResults !== -1) {
          // Results should be the same regardless of case
          expect(results).toBe(previousResults);
        }
        
        previousResults = results;
      }
    });

    test('should handle search with partial matches', async () => {
      // Test partial name matching
      await candidatesPage.searchCandidates('Joh');
      await candidatesPage.page.waitForTimeout(1000);
      
      const partialResults = await candidatesPage.getCandidateCardCount();
      
      // Search for full name
      await candidatesPage.searchCandidates('John');
      await candidatesPage.page.waitForTimeout(1000);
      
      const fullResults = await candidatesPage.getCandidateCardCount();
      
      // Partial search should return same or more results than full search
      expect(partialResults).toBeGreaterThanOrEqual(fullResults);
    });
  });

  test.describe('Performance Tests', () => {
    test('should handle search with large dataset', async () => {
      // This test assumes there are many candidates in the system
      // Measure search performance
      const startTime = Date.now();
      
      await candidatesPage.searchCandidates('test');
      await candidatesPage.page.waitForTimeout(2000); // Wait for search to complete
      
      const endTime = Date.now();
      const searchTime = endTime - startTime;
      
      // Search should complete within reasonable time (5 seconds)
      expect(searchTime).toBeLessThan(5000);
      
      await candidatesPage.takeScreenshot('large-dataset-search');
    });

    test('should debounce search input', async () => {
      // Type quickly and verify only final search is executed
      await candidatesPage.page.fill('[data-testid="search-input"]', 'a');
      await candidatesPage.page.fill('[data-testid="search-input"]', 'ab');
      await candidatesPage.page.fill('[data-testid="search-input"]', 'abc');
      await candidatesPage.page.fill('[data-testid="search-input"]', 'abcd');
      
      // Wait for debounce
      await candidatesPage.page.waitForTimeout(1000);
      
      // Verify search input has final value
      const searchValue = await candidatesPage.page.inputValue('[data-testid="search-input"]');
      expect(searchValue).toBe('abcd');
    });
  });
});
