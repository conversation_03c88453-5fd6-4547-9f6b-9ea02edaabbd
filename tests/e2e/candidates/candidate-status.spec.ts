import { test, expect } from '@playwright/test';
import { CandidatesPage } from '../pages/CandidatesPage';
import { CandidateDetailsPage } from '../pages/CandidateDetailsPage';
import { 
  statusTestData,
  generateRandomCandidate
} from '../fixtures/candidate-data';

test.describe('Candidate Status Management', () => {
  let candidatesPage: CandidatesPage;
  let candidateDetailsPage: CandidateDetailsPage;

  test.beforeEach(async ({ page }) => {
    candidatesPage = new CandidatesPage(page);
    candidateDetailsPage = new CandidateDetailsPage(page);
    await candidatesPage.goto();
  });

  test.describe('Status Display and Updates', () => {
    test('should display candidate status correctly', async () => {
      // Navigate to first candidate details
      await candidatesPage.clickCandidateCard(0);
      await candidateDetailsPage.waitForPageLoad();
      
      // Verify status is displayed
      await expect(candidateDetailsPage.candidateStatus).toBeVisible();
      
      // Switch to process tab to see detailed status info
      await candidateDetailsPage.switchToTab('process');
      
      // Verify status information in process tab
      await candidateDetailsPage.verifyTabContent('process');
      
      await candidateDetailsPage.takeScreenshot('candidate-status-display');
    });

    test('should show primary and secondary status', async () => {
      await candidatesPage.clickCandidateCard(0);
      await candidateDetailsPage.waitForPageLoad();
      
      // Check if secondary status is displayed
      const hasSecondaryStatus = await candidateDetailsPage.candidateSecondaryStatus.isVisible();
      
      if (hasSecondaryStatus) {
        const secondaryStatusText = await candidateDetailsPage.candidateSecondaryStatus.textContent();
        expect(secondaryStatusText).toBeTruthy();
      }
      
      await candidateDetailsPage.takeScreenshot('primary-secondary-status');
    });

    test('should update status through pipeline', async () => {
      await candidatesPage.clickCandidateCard(0);
      await candidateDetailsPage.waitForPageLoad();
      
      // Go to process tab
      await candidateDetailsPage.switchToTab('process');
      
      // Look for pipeline component
      const pipelineComponent = candidateDetailsPage.page.locator('[data-testid="candidate-pipeline"]');
      
      if (await pipelineComponent.isVisible()) {
        // Try to click on next status in pipeline
        const nextStatusButton = candidateDetailsPage.page.locator('[data-testid="pipeline-next-status"]');
        
        if (await nextStatusButton.isVisible()) {
          await nextStatusButton.click();
          
          // Wait for status update
          await candidateDetailsPage.page.waitForTimeout(1000);
          
          await candidateDetailsPage.takeScreenshot('status-updated-via-pipeline');
        }
      }
    });
  });

  test.describe('Status Filtering in Table View', () => {
    test('should filter candidates by status in table', async () => {
      await candidatesPage.switchToTableView();
      
      // Get initial count
      const initialCount = await candidatesPage.getCandidateTableCount();
      
      // Try to filter by status
      const statusFilter = candidatesPage.page.locator('[data-testid="status-filter"]');
      
      if (await statusFilter.isVisible()) {
        await statusFilter.click();
        
        // Select a specific status
        await candidatesPage.page.click('[data-testid="status-filter-new"]');
        await candidatesPage.page.waitForTimeout(1000);
        
        const filteredCount = await candidatesPage.getCandidateTableCount();
        expect(filteredCount).toBeLessThanOrEqual(initialCount);
        
        await candidatesPage.takeScreenshot('filtered-by-status');
      }
    });

    test('should show status badges in table', async () => {
      await candidatesPage.switchToTableView();
      
      // Verify status column exists and shows badges
      const statusCells = candidatesPage.page.locator('.ag-cell[col-id="status"]');
      
      if (await statusCells.first().isVisible()) {
        const statusCount = await statusCells.count();
        expect(statusCount).toBeGreaterThan(0);
        
        // Check if status badges are styled correctly
        const firstStatusCell = statusCells.first();
        await expect(firstStatusCell).toBeVisible();
        
        await candidatesPage.takeScreenshot('status-badges-in-table');
      }
    });
  });

  test.describe('Status Validation', () => {
    test('should validate status transitions', async () => {
      // Create a new candidate with 'new' status
      const testCandidate = generateRandomCandidate();
      testCandidate.status = 'new';
      
      await candidatesPage.clickAddCandidate();
      await candidatesPage.fillCandidateForm(testCandidate);
      await candidatesPage.saveCandidate();
      
      // Navigate to candidate details
      await candidatesPage.searchCandidates(testCandidate.email);
      await candidatesPage.page.waitForTimeout(1000);
      await candidatesPage.clickCandidateCard(0);
      await candidateDetailsPage.waitForPageLoad();
      
      // Verify initial status
      await candidateDetailsPage.verifyCandidateInfo({
        status: 'new'
      });
      
      await candidateDetailsPage.takeScreenshot('initial-status-new');
    });

    test('should handle invalid status updates gracefully', async () => {
      await candidatesPage.clickCandidateCard(0);
      await candidateDetailsPage.waitForPageLoad();
      
      // Try to edit candidate with invalid status (if possible)
      await candidateDetailsPage.clickEditCandidate();
      
      // Look for status dropdown in edit form
      const statusDropdown = candidateDetailsPage.page.locator('[data-testid="edit-status-select"]');
      
      if (await statusDropdown.isVisible()) {
        // Verify only valid statuses are available
        await statusDropdown.click();
        
        const statusOptions = candidateDetailsPage.page.locator('[data-testid="status-option"]');
        const optionCount = await statusOptions.count();
        
        expect(optionCount).toBeGreaterThan(0);
        expect(optionCount).toBeLessThanOrEqual(statusTestData.primaryStatuses.length);
        
        await candidateDetailsPage.takeScreenshot('status-dropdown-options');
        
        // Cancel edit
        await candidateDetailsPage.page.click('[data-testid="edit-cancel-button"]');
      }
    });
  });

  test.describe('Secondary Status Management', () => {
    test('should show appropriate secondary statuses for primary status', async () => {
      await candidatesPage.clickCandidateCard(0);
      await candidateDetailsPage.waitForPageLoad();
      
      // Edit candidate to test secondary status
      await candidateDetailsPage.clickEditCandidate();
      
      const primaryStatusSelect = candidateDetailsPage.page.locator('[data-testid="edit-primary-status"]');
      const secondaryStatusSelect = candidateDetailsPage.page.locator('[data-testid="edit-secondary-status"]');
      
      if (await primaryStatusSelect.isVisible() && await secondaryStatusSelect.isVisible()) {
        // Select a primary status
        await primaryStatusSelect.click();
        await candidateDetailsPage.page.click('[data-testid="primary-status-screening"]');
        
        // Wait for secondary status options to update
        await candidateDetailsPage.page.waitForTimeout(500);
        
        // Check secondary status options
        await secondaryStatusSelect.click();
        
        const secondaryOptions = candidateDetailsPage.page.locator('[data-testid="secondary-status-option"]');
        const secondaryCount = await secondaryOptions.count();
        
        expect(secondaryCount).toBeGreaterThan(0);
        
        await candidateDetailsPage.takeScreenshot('secondary-status-options');
        
        // Cancel edit
        await candidateDetailsPage.page.click('[data-testid="edit-cancel-button"]');
      }
    });

    test('should clear secondary status when primary status changes', async () => {
      // This test verifies that secondary status is cleared when primary status changes
      // to a status that doesn't support the current secondary status
      
      await candidatesPage.clickCandidateCard(0);
      await candidateDetailsPage.waitForPageLoad();
      
      await candidateDetailsPage.clickEditCandidate();
      
      const primaryStatusSelect = candidateDetailsPage.page.locator('[data-testid="edit-primary-status"]');
      
      if (await primaryStatusSelect.isVisible()) {
        // Change primary status multiple times
        await primaryStatusSelect.click();
        await candidateDetailsPage.page.click('[data-testid="primary-status-new"]');
        await candidateDetailsPage.page.waitForTimeout(300);
        
        await primaryStatusSelect.click();
        await candidateDetailsPage.page.click('[data-testid="primary-status-interview"]');
        await candidateDetailsPage.page.waitForTimeout(300);
        
        await candidateDetailsPage.takeScreenshot('primary-status-changed');
        
        // Cancel edit
        await candidateDetailsPage.page.click('[data-testid="edit-cancel-button"]');
      }
    });
  });

  test.describe('Status History and Tracking', () => {
    test('should track status changes over time', async () => {
      await candidatesPage.clickCandidateCard(0);
      await candidateDetailsPage.waitForPageLoad();
      
      // Look for status history component
      await candidateDetailsPage.switchToTab('process');
      
      const statusHistory = candidateDetailsPage.page.locator('[data-testid="status-history"]');
      
      if (await statusHistory.isVisible()) {
        // Verify status history is displayed
        await expect(statusHistory).toBeVisible();
        
        const historyItems = candidateDetailsPage.page.locator('[data-testid="status-history-item"]');
        const historyCount = await historyItems.count();
        
        expect(historyCount).toBeGreaterThanOrEqual(1);
        
        await candidateDetailsPage.takeScreenshot('status-history');
      }
    });

    test('should show status timestamps', async () => {
      await candidatesPage.clickCandidateCard(0);
      await candidateDetailsPage.waitForPageLoad();
      
      await candidateDetailsPage.switchToTab('process');
      
      // Look for timestamp information
      const timestamps = candidateDetailsPage.page.locator('[data-testid="status-timestamp"]');
      
      if (await timestamps.first().isVisible()) {
        const timestampCount = await timestamps.count();
        expect(timestampCount).toBeGreaterThan(0);
        
        // Verify timestamp format
        const firstTimestamp = await timestamps.first().textContent();
        expect(firstTimestamp).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}|\d{4}-\d{2}-\d{2}/); // Date format
        
        await candidateDetailsPage.takeScreenshot('status-timestamps');
      }
    });
  });

  test.describe('Bulk Status Updates', () => {
    test('should support bulk status updates in table view', async () => {
      await candidatesPage.switchToTableView();
      
      // Look for bulk selection checkboxes
      const selectAllCheckbox = candidatesPage.page.locator('[data-testid="select-all-candidates"]');
      
      if (await selectAllCheckbox.isVisible()) {
        // Select multiple candidates
        await selectAllCheckbox.click();
        
        // Look for bulk actions menu
        const bulkActionsMenu = candidatesPage.page.locator('[data-testid="bulk-actions-menu"]');
        
        if (await bulkActionsMenu.isVisible()) {
          await bulkActionsMenu.click();
          
          // Look for bulk status update option
          const bulkStatusUpdate = candidatesPage.page.locator('[data-testid="bulk-status-update"]');
          
          if (await bulkStatusUpdate.isVisible()) {
            await bulkStatusUpdate.click();
            
            await candidateDetailsPage.takeScreenshot('bulk-status-update-modal');
            
            // Cancel the bulk update
            await candidateDetailsPage.page.click('[data-testid="bulk-update-cancel"]');
          }
        }
      }
    });
  });
});
