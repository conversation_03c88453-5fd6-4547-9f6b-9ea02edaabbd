# E2E Testing Suite with <PERSON><PERSON>

This directory contains end-to-end tests for the ATS Dashboard application using Playwright.

## Overview

The test suite covers comprehensive testing of the candidates management functionality including:

- **Candidate Creation**: Adding new candidates with various data scenarios
- **Candidate Editing**: Updating candidate information
- **Candidate Details**: Viewing detailed candidate information across all tabs
- **List Management**: Viewing candidates in list and table formats
- **Search & Filtering**: Testing search functionality and filters
- **Status Management**: Testing candidate status updates and workflows
- **Visual Testing**: Screenshot comparisons and visual regression testing

## Test Structure

```
tests/e2e/
├── candidates/
│   ├── candidates.spec.ts          # Main candidate functionality tests
│   ├── candidate-search.spec.ts    # Search and filtering tests
│   └── candidate-status.spec.ts    # Status management tests
├── pages/
│   ├── CandidatesPage.ts           # Page object for candidates list
│   └── CandidateDetailsPage.ts     # Page object for candidate details
├── utils/
│   └── test-helpers.ts             # Common test utilities
├── fixtures/
│   └── candidate-data.ts           # Test data fixtures
├── global-setup.ts                 # Global test setup
└── README.md                       # This file
```

## Running Tests

### Prerequisites

1. Ensure the application is running:
   ```bash
   npm run dev:full
   ```

2. Install Playwright browsers (if not already done):
   ```bash
   npx playwright install
   ```

### Test Commands

```bash
# Run all E2E tests
npm run test:e2e

# Run tests with UI mode (interactive)
npm run test:e2e:ui

# Run tests in headed mode (see browser)
npm run test:e2e:headed

# Debug tests
npm run test:e2e:debug

# View test report
npm run test:e2e:report
```

### Running Specific Tests

```bash
# Run only candidate tests
npx playwright test candidates/

# Run specific test file
npx playwright test candidates/candidate-search.spec.ts

# Run specific test
npx playwright test -g "should create a new candidate"

# Run tests on specific browser
npx playwright test --project=chromium
```

## Test Configuration

The tests are configured in `playwright.config.ts` with the following settings:

- **Base URL**: `http://localhost:5173`
- **Browsers**: Chromium, Firefox, WebKit, Mobile Chrome, Mobile Safari
- **Retries**: 2 on CI, 0 locally
- **Screenshots**: On failure
- **Videos**: On failure
- **Traces**: On first retry

## Page Objects

### CandidatesPage
Handles interactions with the candidates list page:
- Navigation and page loading
- Adding new candidates
- Switching between list/table views
- Search functionality
- Bulk operations

### CandidateDetailsPage
Handles interactions with candidate details page:
- Tab navigation
- Information verification
- Editing candidates
- Quick actions (WhatsApp, email, etc.)
- Talent incubator management

## Test Data

Test data is managed through fixtures in `candidate-data.ts`:

- `validCandidateData`: Complete candidate with all fields
- `minimalCandidateData`: Candidate with only required fields
- `candidateDataForEdit`: Data for testing edits
- `invalidCandidateData`: Invalid data for validation testing
- `generateRandomCandidate()`: Creates unique test candidates

## Visual Testing

The test suite includes visual regression testing:

- Screenshots are taken at key points
- Visual comparisons detect UI changes
- Screenshots are stored in `test-results/`

### Updating Visual Baselines

```bash
# Update all screenshots
npx playwright test --update-snapshots

# Update specific test screenshots
npx playwright test candidate-details --update-snapshots
```

## Test Helpers

Common utilities in `test-helpers.ts`:

- `waitForPageLoad()`: Ensures page is fully loaded
- `fillField()`: Fills form fields with validation
- `clickElement()`: Clicks elements with wait
- `waitForToast()`: Waits for notification messages
- `takeScreenshot()`: Takes named screenshots
- `waitForAGGrid()`: Waits for AG Grid to load

## Best Practices

### Test Organization
- Group related tests in describe blocks
- Use descriptive test names
- Keep tests independent and isolated
- Use page objects for reusable interactions

### Data Management
- Use fixtures for test data
- Generate unique data to avoid conflicts
- Clean up test data when necessary
- Use realistic test data

### Assertions
- Use specific assertions over generic ones
- Verify both positive and negative scenarios
- Include visual verifications
- Test error handling

### Performance
- Use appropriate wait strategies
- Avoid unnecessary delays
- Parallelize tests when possible
- Monitor test execution time

## Debugging Tests

### Local Debugging
```bash
# Run with debug mode
npm run test:e2e:debug

# Run specific test with debug
npx playwright test candidate-creation --debug
```

### CI Debugging
- Check test artifacts in CI
- Review screenshots and videos
- Examine trace files
- Check console logs

## Troubleshooting

### Common Issues

1. **Tests timing out**
   - Increase timeout values
   - Check if application is running
   - Verify network connectivity

2. **Element not found**
   - Check if selectors are correct
   - Verify element is visible
   - Add appropriate waits

3. **Visual test failures**
   - Check for UI changes
   - Update baselines if changes are expected
   - Verify browser/OS differences

4. **Flaky tests**
   - Add proper waits
   - Make tests more robust
   - Check for race conditions

### Getting Help

- Check Playwright documentation: https://playwright.dev/
- Review test logs and artifacts
- Use debug mode for step-by-step execution
- Check browser developer tools

## Contributing

When adding new tests:

1. Follow existing patterns and structure
2. Use page objects for UI interactions
3. Add appropriate test data fixtures
4. Include visual testing where relevant
5. Document complex test scenarios
6. Ensure tests are reliable and maintainable
