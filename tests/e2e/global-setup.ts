import { chromium, FullConfig } from '@playwright/test';

/**
 * Global setup for Playwright tests
 * This runs once before all tests
 */
async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup for E2E tests...');

  // Create a browser instance for setup
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Wait for the application to be ready
    console.log('⏳ Waiting for application to be ready...');
    
    // Navigate to the base URL
    const baseURL = config.projects[0].use.baseURL || 'http://localhost:5173';
    await page.goto(baseURL);
    
    // Wait for the app to load
    await page.waitForLoadState('networkidle');
    
    // Check if the app is running correctly
    await page.waitForSelector('body', { timeout: 30000 });
    
    console.log('✅ Application is ready for testing');
    
    // You can add more setup steps here, such as:
    // - Database seeding
    // - Authentication setup
    // - Test data preparation
    
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await context.close();
    await browser.close();
  }

  console.log('✅ Global setup completed successfully');
}

export default globalSetup;
