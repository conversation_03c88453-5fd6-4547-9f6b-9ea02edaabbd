import { test, expect } from '@playwright/test';

test.describe('Playwright Setup Verification', () => {
  test('should verify <PERSON><PERSON> is working correctly', async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Verify the page loads
    await expect(page).toHaveTitle(/ATS Dashboard/);
    
    // Take a screenshot to verify visual setup
    await page.screenshot({ path: 'test-results/setup-verification.png', fullPage: true });
    
    console.log('✅ Playwright setup verification completed successfully');
  });

  test('should verify navigation works', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Try to navigate to candidates page
    const candidatesLink = page.locator('a[href*="candidates"]').first();
    
    if (await candidatesLink.isVisible()) {
      await candidatesLink.click();
      await page.waitForLoadState('networkidle');
      
      // Verify we're on candidates page
      await expect(page).toHaveURL(/.*candidates.*/);
      
      console.log('✅ Navigation verification completed successfully');
    } else {
      console.log('ℹ️ Candidates link not found - this is expected if the app structure is different');
    }
  });
});
