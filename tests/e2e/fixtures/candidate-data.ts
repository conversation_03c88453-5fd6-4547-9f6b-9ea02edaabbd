/**
 * Test data fixtures for candidate tests
 */

export interface CandidateTestData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  position?: string;
  status?: string;
  secondaryStatus?: string;
  location?: string;
  stargetyId?: string;
  currentCompany?: string;
  experienceYears?: string;
  education?: string;
  skills?: string[];
  desiredSalary?: string;
  salaryCurrency?: string;
  availabilityDate?: string;
  source?: string;
  englishLevel?: string;
  portfolio?: string;
  resume?: string;
  coverLetter?: string;
  socialLinks?: {
    linkedin?: string;
    github?: string;
    twitter?: string;
  };
}

export const validCandidateData: CandidateTestData = {
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '+1234567890',
  position: 'Software Engineer',
  status: 'new',
  location: 'New York, NY',
  stargetyId: 'JD-7890',
  currentCompany: 'Tech Corp',
  experienceYears: '5',
  education: 'Bachelor in Computer Science',
  skills: ['JavaScript', 'React', 'Node.js'],
  desiredSalary: '80000',
  salaryCurrency: 'USD',
  availabilityDate: '2026-02-01',
  source: 'LinkedIn',
  englishLevel: 'Advanced',
  portfolio: 'https://johndoe.dev',
  resume: 'https://example.com/resume.pdf',
  coverLetter: 'https://example.com/cover-letter.pdf',
  socialLinks: {
    linkedin: 'https://linkedin.com/in/johndoe',
    github: 'https://github.com/johndoe',
    twitter: 'https://twitter.com/johndoe'
  }
};

export const minimalCandidateData: CandidateTestData = {
  firstName: 'Jane',
  lastName: 'Smith',
  email: '<EMAIL>'
};

export const candidateDataForEdit: CandidateTestData = {
  firstName: 'Michael',
  lastName: 'Johnson',
  email: '<EMAIL>',
  phone: '+1987654321',
  position: 'Frontend Developer',
  status: 'screening',
  location: 'San Francisco, CA'
};

export const updatedCandidateData: Partial<CandidateTestData> = {
  firstName: 'Michael Updated',
  phone: '+1555123456',
  position: 'Senior Frontend Developer',
  currentCompany: 'New Tech Company'
};

export const invalidCandidateData = {
  firstName: '',
  lastName: '',
  email: 'invalid-email',
  phone: 'invalid-phone'
};

export const duplicateCandidateData: CandidateTestData = {
  firstName: 'Duplicate',
  lastName: 'User',
  email: '<EMAIL>', // Same email as validCandidateData
  phone: '+1111111111'
};

export const candidatesListData: CandidateTestData[] = [
  {
    firstName: 'Alice',
    lastName: 'Wilson',
    email: '<EMAIL>',
    position: 'UX Designer',
    status: 'new'
  },
  {
    firstName: 'Bob',
    lastName: 'Brown',
    email: '<EMAIL>',
    position: 'Backend Developer',
    status: 'screening'
  },
  {
    firstName: 'Carol',
    lastName: 'Davis',
    email: '<EMAIL>',
    position: 'Product Manager',
    status: 'interview'
  },
  {
    firstName: 'David',
    lastName: 'Miller',
    email: '<EMAIL>',
    position: 'DevOps Engineer',
    status: 'offer'
  },
  {
    firstName: 'Eva',
    lastName: 'Garcia',
    email: '<EMAIL>',
    position: 'Data Scientist',
    status: 'hired'
  }
];

export const searchTestData = {
  searchTerms: [
    'John',
    'Software',
    '<EMAIL>',
    'New York',
    'JavaScript'
  ],
  expectedResults: {
    'John': 1,
    'Software': 1,
    '<EMAIL>': 1,
    'New York': 1,
    'JavaScript': 1,
    'NonExistent': 0
  }
};

export const statusTestData = {
  primaryStatuses: [
    'new',
    'screening',
    'interview',
    'offer',
    'hired',
    'rejected'
  ],
  secondaryStatuses: {
    new: ['pending_review', 'contacted'],
    screening: ['phone_screen', 'technical_screen'],
    interview: ['first_round', 'second_round', 'final_round'],
    offer: ['offer_sent', 'negotiating'],
    hired: ['onboarding', 'completed'],
    rejected: ['not_qualified', 'withdrew']
  }
};

export const talentIncubatorTestData = {
  candidateForIncubator: {
    firstName: 'Talent',
    lastName: 'Incubator',
    email: '<EMAIL>',
    position: 'Junior Developer',
    status: 'new'
  }
};

/**
 * Generate random candidate data for testing
 */
export function generateRandomCandidate(): CandidateTestData {
  const timestamp = Date.now();
  return {
    firstName: `Test${timestamp}`,
    lastName: `User${timestamp}`,
    email: `test.user.${timestamp}@example.com`,
    phone: `+1${Math.floor(Math.random() * 9000000000) + 1000000000}`,
    position: 'Test Position',
    status: 'new'
  };
}

/**
 * Generate multiple random candidates
 */
export function generateRandomCandidates(count: number): CandidateTestData[] {
  return Array.from({ length: count }, () => generateRandomCandidate());
}
